-- Database Functions and Triggers for CareSyncAI

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create audit log entries
CREATE OR REPLACE FUNCTION create_audit_log()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_log (
        user_id,
        action,
        table_name,
        record_id,
        old_values,
        new_values,
        ip_address
    ) VALUES (
        auth.uid(),
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
        CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END,
        inet_client_addr()
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate fall risk score
CREATE OR REPLACE FUNCTION calculate_fall_risk_score(
    p_age INTEGER,
    p_mobility_score INTEGER,
    p_cognitive_score INTEGER,
    p_medications JSONB,
    p_medical_conditions TEXT[]
)
RETURNS DECIMAL(3,2) AS $$
DECLARE
    risk_score DECIMAL(3,2) := 0.0;
    med_count INTEGER;
    high_risk_conditions TEXT[] := ARRAY['dementia', 'parkinsons', 'stroke', 'diabetes'];
BEGIN
    -- Age factor (0-0.3)
    risk_score := risk_score + LEAST(0.3, (p_age - 65) * 0.01);
    
    -- Mobility factor (0-0.4)
    risk_score := risk_score + (10 - p_mobility_score) * 0.04;
    
    -- Cognitive factor (0-0.2)
    risk_score := risk_score + (10 - p_cognitive_score) * 0.02;
    
    -- Medication count factor (0-0.1)
    med_count := jsonb_array_length(COALESCE(p_medications, '[]'::jsonb));
    risk_score := risk_score + LEAST(0.1, med_count * 0.01);
    
    -- High-risk conditions factor (0-0.2)
    IF p_medical_conditions && high_risk_conditions THEN
        risk_score := risk_score + 0.2;
    END IF;
    
    RETURN LEAST(1.0, risk_score);
END;
$$ LANGUAGE plpgsql;

-- Function to update patient fall risk score
CREATE OR REPLACE FUNCTION update_patient_fall_risk()
RETURNS TRIGGER AS $$
BEGIN
    NEW.fall_risk_score := calculate_fall_risk_score(
        EXTRACT(YEAR FROM AGE(NEW.date_of_birth))::INTEGER,
        NEW.mobility_score,
        NEW.cognitive_score,
        NEW.medications,
        NEW.medical_conditions
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to check inventory reorder levels
CREATE OR REPLACE FUNCTION check_inventory_reorder()
RETURNS TRIGGER AS $$
BEGIN
    -- Update status based on quantity
    IF NEW.current_quantity <= 0 THEN
        NEW.status := 'out_of_stock';
    ELSIF NEW.current_quantity <= NEW.reorder_level THEN
        NEW.status := 'low_stock';
    ELSE
        NEW.status := 'in_stock';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate patient number
CREATE OR REPLACE FUNCTION generate_patient_number()
RETURNS TRIGGER AS $$
DECLARE
    year_suffix TEXT;
    sequence_num INTEGER;
BEGIN
    year_suffix := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    SELECT COALESCE(MAX(
        CASE 
            WHEN patient_number ~ ('^P' || year_suffix || '[0-9]+$') 
            THEN SUBSTRING(patient_number FROM LENGTH('P' || year_suffix) + 1)::INTEGER
            ELSE 0
        END
    ), 0) + 1 INTO sequence_num
    FROM patients;
    
    NEW.patient_number := 'P' || year_suffix || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate medical record vitals
CREATE OR REPLACE FUNCTION validate_vitals()
RETURNS TRIGGER AS $$
DECLARE
    heart_rate INTEGER;
    systolic INTEGER;
    diastolic INTEGER;
    temperature DECIMAL;
    oxygen_sat INTEGER;
BEGIN
    -- Extract vital signs from JSONB
    heart_rate := (NEW.vitals->>'heart_rate')::INTEGER;
    systolic := (NEW.vitals->>'systolic_bp')::INTEGER;
    diastolic := (NEW.vitals->>'diastolic_bp')::INTEGER;
    temperature := (NEW.vitals->>'temperature')::DECIMAL;
    oxygen_sat := (NEW.vitals->>'oxygen_saturation')::INTEGER;
    
    -- Set requires_attention flag for abnormal vitals
    NEW.requires_attention := (
        (heart_rate IS NOT NULL AND (heart_rate < 50 OR heart_rate > 120)) OR
        (systolic IS NOT NULL AND (systolic < 90 OR systolic > 180)) OR
        (diastolic IS NOT NULL AND (diastolic < 60 OR diastolic > 110)) OR
        (temperature IS NOT NULL AND (temperature < 96.0 OR temperature > 101.0)) OR
        (oxygen_sat IS NOT NULL AND oxygen_sat < 95) OR
        NEW.pain_level > 7
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to encrypt sensitive data
CREATE OR REPLACE FUNCTION encrypt_sensitive_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Encrypt SSN if provided
    IF NEW.ssn_encrypted IS NOT NULL AND NEW.ssn_encrypted != OLD.ssn_encrypted THEN
        NEW.ssn_encrypted := crypt(NEW.ssn_encrypted, gen_salt('bf'));
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to get patient summary for AI
CREATE OR REPLACE FUNCTION get_patient_ai_summary(patient_uuid UUID)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'patient_id', p.id,
        'age', EXTRACT(YEAR FROM AGE(p.date_of_birth)),
        'medical_conditions', p.medical_conditions,
        'medications', p.medications,
        'mobility_score', p.mobility_score,
        'cognitive_score', p.cognitive_score,
        'fall_risk_score', p.fall_risk_score,
        'recent_vitals', (
            SELECT jsonb_agg(vitals ORDER BY record_date DESC)
            FROM medical_records 
            WHERE patient_id = p.id 
            AND record_date >= NOW() - INTERVAL '7 days'
            LIMIT 5
        ),
        'recent_notes', (
            SELECT array_agg(notes ORDER BY record_date DESC)
            FROM medical_records 
            WHERE patient_id = p.id 
            AND notes IS NOT NULL
            AND record_date >= NOW() - INTERVAL '30 days'
            LIMIT 10
        )
    ) INTO result
    FROM patients p
    WHERE p.id = patient_uuid;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for updated_at columns
CREATE TRIGGER update_caregivers_updated_at
    BEFORE UPDATE ON caregivers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_patients_updated_at
    BEFORE UPDATE ON patients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medical_records_updated_at
    BEFORE UPDATE ON medical_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_records_updated_at
    BEFORE UPDATE ON records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shop_inventory_updated_at
    BEFORE UPDATE ON shop_inventory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_updated_at
    BEFORE UPDATE ON billing
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_care_schedules_updated_at
    BEFORE UPDATE ON care_schedules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create audit log triggers
CREATE TRIGGER audit_caregivers
    AFTER INSERT OR UPDATE OR DELETE ON caregivers
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

CREATE TRIGGER audit_patients
    AFTER INSERT OR UPDATE OR DELETE ON patients
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

CREATE TRIGGER audit_medical_records
    AFTER INSERT OR UPDATE OR DELETE ON medical_records
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

CREATE TRIGGER audit_billing
    AFTER INSERT OR UPDATE OR DELETE ON billing
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

-- Create business logic triggers
CREATE TRIGGER generate_patient_number_trigger
    BEFORE INSERT ON patients
    FOR EACH ROW EXECUTE FUNCTION generate_patient_number();

CREATE TRIGGER update_fall_risk_trigger
    BEFORE INSERT OR UPDATE ON patients
    FOR EACH ROW EXECUTE FUNCTION update_patient_fall_risk();

CREATE TRIGGER check_inventory_reorder_trigger
    BEFORE INSERT OR UPDATE ON shop_inventory
    FOR EACH ROW EXECUTE FUNCTION check_inventory_reorder();

CREATE TRIGGER validate_vitals_trigger
    BEFORE INSERT OR UPDATE ON medical_records
    FOR EACH ROW EXECUTE FUNCTION validate_vitals();

CREATE TRIGGER encrypt_patient_data_trigger
    BEFORE INSERT OR UPDATE ON patients
    FOR EACH ROW EXECUTE FUNCTION encrypt_sensitive_data();
