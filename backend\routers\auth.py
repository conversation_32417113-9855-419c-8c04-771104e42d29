"""
Authentication API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import Optional
from datetime import timedelta

from core.auth import AuthService, create_access_token, verify_token
from core.config import settings

router = APIRouter()
security = HTTPBearer()
auth_service = AuthService()

class LoginRequest(BaseModel):
    email: str = Field(..., description="User email address")
    password: str = Field(..., min_length=6, description="User password")

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: dict

class RegisterRequest(BaseModel):
    email: str = Field(..., description="User email address")
    password: str = Field(..., min_length=6, description="User password")
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    phone: Optional[str] = Field(None, min_length=10, max_length=20)
    role: str = Field(default="caregiver", description="User role")
    license_number: Optional[str] = None
    license_expiry: Optional[str] = None

class PasswordChangeRequest(BaseModel):
    current_password: str = Field(..., min_length=6)
    new_password: str = Field(..., min_length=6)

class PasswordResetRequest(BaseModel):
    email: str = Field(..., description="User email address")

@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest):
    """
    Authenticate user and return access token
    """
    try:
        user = await auth_service.authenticate_user(
            login_data.email, 
            login_data.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is inactive",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={
                "sub": user["user_id"],
                "caregiver_id": user["caregiver_id"],
                "email": user["email"],
                "role": user["role"]
            },
            expires_delta=access_token_expires
        )
        
        return LoginResponse(
            access_token=access_token,
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user={
                "id": user["caregiver_id"],
                "email": user["email"],
                "first_name": user["first_name"],
                "last_name": user["last_name"],
                "role": user["role"]
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )

@router.post("/register", status_code=status.HTTP_201_CREATED)
async def register(register_data: RegisterRequest):
    """
    Register a new user
    Note: In production, this might be restricted to admin users only
    """
    try:
        user = await auth_service.register_user(register_data.dict())
        return {
            "message": "User registered successfully",
            "user_id": user["user_id"],
            "email": user["email"]
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Registration failed: {str(e)}"
        )

@router.get("/me")
async def get_current_user_profile(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Get current user profile information
    """
    try:
        payload = verify_token(credentials.credentials)
        user_details = await auth_service.get_current_user_details(payload["sub"])
        
        if not user_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Remove sensitive information
        safe_user_details = {
            "id": user_details["id"],
            "first_name": user_details["first_name"],
            "last_name": user_details["last_name"],
            "email": user_details["email"],
            "phone": user_details["phone"],
            "role": user_details["role"],
            "license_number": user_details["license_number"],
            "license_expiry": user_details["license_expiry"],
            "is_active": user_details["is_active"],
            "hire_date": user_details["hire_date"],
            "certifications": user_details["certifications"]
        }
        
        return safe_user_details
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user profile: {str(e)}"
        )

@router.put("/me")
async def update_current_user_profile(
    update_data: dict,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Update current user profile
    """
    try:
        payload = verify_token(credentials.credentials)
        
        # Remove sensitive fields that shouldn't be updated via this endpoint
        forbidden_fields = ["user_id", "id", "role", "created_at", "updated_at"]
        for field in forbidden_fields:
            update_data.pop(field, None)
        
        updated_user = await auth_service.update_user_profile(
            payload["sub"], 
            update_data
        )
        
        return {
            "message": "Profile updated successfully",
            "user": updated_user
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Profile update failed: {str(e)}"
        )

@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Change user password
    """
    try:
        payload = verify_token(credentials.credentials)
        
        # Verify current password
        user = await auth_service.authenticate_user(
            payload["email"], 
            password_data.current_password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Current password is incorrect"
            )
        
        # Change password
        success = await auth_service.change_password(
            payload["sub"], 
            password_data.new_password
        )
        
        if success:
            return {"message": "Password changed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to change password"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Password change failed: {str(e)}"
        )

@router.post("/reset-password")
async def reset_password(reset_data: PasswordResetRequest):
    """
    Send password reset email
    """
    try:
        success = await auth_service.reset_password(reset_data.email)
        
        # Always return success message for security (don't reveal if email exists)
        return {
            "message": "If the email exists in our system, a password reset link has been sent"
        }
    except Exception as e:
        # Log error but don't expose it to user
        return {
            "message": "If the email exists in our system, a password reset link has been sent"
        }

@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Logout user (client-side token removal)
    """
    # In a stateless JWT system, logout is typically handled client-side
    # by removing the token. For enhanced security, you could implement
    # a token blacklist here.
    return {"message": "Logged out successfully"}

@router.post("/refresh")
async def refresh_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Refresh access token
    """
    try:
        payload = verify_token(credentials.credentials)
        
        # Create new access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        new_access_token = create_access_token(
            data={
                "sub": payload["sub"],
                "caregiver_id": payload["caregiver_id"],
                "email": payload["email"],
                "role": payload["role"]
            },
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": new_access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Token refresh failed: {str(e)}"
        )
