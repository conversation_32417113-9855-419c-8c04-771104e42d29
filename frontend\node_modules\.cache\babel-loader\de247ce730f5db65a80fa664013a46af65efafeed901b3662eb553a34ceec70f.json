{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: `${API_BASE_URL}/api`,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('access_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expired or invalid\n    localStorage.removeItem('access_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nclass AuthService {\n  async login(email, password) {\n    try {\n      const response = await api.post('/auth/login', {\n        email,\n        password\n      });\n      const {\n        access_token\n      } = response.data;\n      localStorage.setItem('access_token', access_token);\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Login failed');\n    }\n  }\n  async register(userData) {\n    try {\n      await api.post('/auth/register', userData);\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Registration failed');\n    }\n  }\n  async logout() {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.error('Logout API call failed:', error);\n    } finally {\n      localStorage.removeItem('access_token');\n    }\n  }\n  async getCurrentUser() {\n    try {\n      const response = await api.get('/auth/me');\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || 'Failed to get user profile');\n    }\n  }\n  async updateProfile(data) {\n    try {\n      const response = await api.put('/auth/me', data);\n      return response.data.user;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      throw new Error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || 'Failed to update profile');\n    }\n  }\n  async changePassword(currentPassword, newPassword) {\n    try {\n      await api.post('/auth/change-password', {\n        current_password: currentPassword,\n        new_password: newPassword\n      });\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      throw new Error(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail) || 'Failed to change password');\n    }\n  }\n  async resetPassword(email) {\n    try {\n      await api.post('/auth/reset-password', {\n        email\n      });\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      throw new Error(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || 'Failed to send reset email');\n    }\n  }\n  async refreshToken() {\n    try {\n      const response = await api.post('/auth/refresh');\n      const {\n        access_token\n      } = response.data;\n      localStorage.setItem('access_token', access_token);\n      return access_token;\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      localStorage.removeItem('access_token');\n      throw new Error(((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || 'Failed to refresh token');\n    }\n  }\n  isAuthenticated() {\n    return !!localStorage.getItem('access_token');\n  }\n  getToken() {\n    return localStorage.getItem('access_token');\n  }\n}\nexport const authService = new AuthService();\nexport { api };", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "AuthService", "login", "email", "password", "post", "access_token", "data", "setItem", "_error$response2", "_error$response2$data", "Error", "detail", "register", "userData", "_error$response3", "_error$response3$data", "logout", "console", "getCurrentUser", "get", "_error$response4", "_error$response4$data", "updateProfile", "put", "user", "_error$response5", "_error$response5$data", "changePassword", "currentPassword", "newPassword", "current_password", "new_password", "_error$response6", "_error$response6$data", "resetPassword", "_error$response7", "_error$response7$data", "refreshToken", "_error$response8", "_error$response8$data", "isAuthenticated", "getToken", "authService"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/src/services/authService.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: `${API_BASE_URL}/api`,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('access_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('access_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  access_token: string;\n  token_type: string;\n  expires_in: number;\n  user: {\n    id: string;\n    email: string;\n    first_name: string;\n    last_name: string;\n    role: string;\n  };\n}\n\nexport interface RegisterRequest {\n  email: string;\n  password: string;\n  first_name: string;\n  last_name: string;\n  phone?: string;\n  role?: string;\n  license_number?: string;\n  license_expiry?: string;\n}\n\nexport interface UserProfile {\n  id: string;\n  first_name: string;\n  last_name: string;\n  email: string;\n  phone?: string;\n  role: string;\n  license_number?: string;\n  license_expiry?: string;\n  is_active: boolean;\n  hire_date?: string;\n  certifications?: string[];\n}\n\nclass AuthService {\n  async login(email: string, password: string): Promise<LoginResponse> {\n    try {\n      const response = await api.post<LoginResponse>('/auth/login', {\n        email,\n        password,\n      });\n\n      const { access_token } = response.data;\n      localStorage.setItem('access_token', access_token);\n\n      return response.data;\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.detail || 'Login failed'\n      );\n    }\n  }\n\n  async register(userData: RegisterRequest): Promise<void> {\n    try {\n      await api.post('/auth/register', userData);\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.detail || 'Registration failed'\n      );\n    }\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await api.post('/auth/logout');\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.error('Logout API call failed:', error);\n    } finally {\n      localStorage.removeItem('access_token');\n    }\n  }\n\n  async getCurrentUser(): Promise<UserProfile> {\n    try {\n      const response = await api.get<UserProfile>('/auth/me');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.detail || 'Failed to get user profile'\n      );\n    }\n  }\n\n  async updateProfile(data: Partial<UserProfile>): Promise<UserProfile> {\n    try {\n      const response = await api.put<{ user: UserProfile }>('/auth/me', data);\n      return response.data.user;\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.detail || 'Failed to update profile'\n      );\n    }\n  }\n\n  async changePassword(currentPassword: string, newPassword: string): Promise<void> {\n    try {\n      await api.post('/auth/change-password', {\n        current_password: currentPassword,\n        new_password: newPassword,\n      });\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.detail || 'Failed to change password'\n      );\n    }\n  }\n\n  async resetPassword(email: string): Promise<void> {\n    try {\n      await api.post('/auth/reset-password', { email });\n    } catch (error: any) {\n      throw new Error(\n        error.response?.data?.detail || 'Failed to send reset email'\n      );\n    }\n  }\n\n  async refreshToken(): Promise<string> {\n    try {\n      const response = await api.post<{ access_token: string }>('/auth/refresh');\n      const { access_token } = response.data;\n      localStorage.setItem('access_token', access_token);\n      return access_token;\n    } catch (error: any) {\n      localStorage.removeItem('access_token');\n      throw new Error(\n        error.response?.data?.detail || 'Failed to refresh token'\n      );\n    }\n  }\n\n  isAuthenticated(): boolean {\n    return !!localStorage.getItem('access_token');\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('access_token');\n  }\n}\n\nexport const authService = new AuthService();\nexport { api };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAE7E;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAE,GAAGN,YAAY,MAAM;EAC9BO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;IACvCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AA6CD,MAAMU,WAAW,CAAC;EAChB,MAAMC,KAAKA,CAACC,KAAa,EAAEC,QAAgB,EAA0B;IACnE,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMf,GAAG,CAAC0B,IAAI,CAAgB,aAAa,EAAE;QAC5DF,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEE;MAAa,CAAC,GAAGZ,QAAQ,CAACa,IAAI;MACtCnB,YAAY,CAACoB,OAAO,CAAC,cAAc,EAAEF,YAAY,CAAC;MAElD,OAAOZ,QAAQ,CAACa,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAU,EAAE;MAAA,IAAAkB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIC,KAAK,CACb,EAAAF,gBAAA,GAAAlB,KAAK,CAACG,QAAQ,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBF,IAAI,cAAAG,qBAAA,uBAApBA,qBAAA,CAAsBE,MAAM,KAAI,cAClC,CAAC;IACH;EACF;EAEA,MAAMC,QAAQA,CAACC,QAAyB,EAAiB;IACvD,IAAI;MACF,MAAMnC,GAAG,CAAC0B,IAAI,CAAC,gBAAgB,EAAES,QAAQ,CAAC;IAC5C,CAAC,CAAC,OAAOvB,KAAU,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIL,KAAK,CACb,EAAAI,gBAAA,GAAAxB,KAAK,CAACG,QAAQ,cAAAqB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBR,IAAI,cAAAS,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,KAAI,qBAClC,CAAC;IACH;EACF;EAEA,MAAMK,MAAMA,CAAA,EAAkB;IAC5B,IAAI;MACF,MAAMtC,GAAG,CAAC0B,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC,CAAC,OAAOd,KAAK,EAAE;MACd;MACA2B,OAAO,CAAC3B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRH,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;IACzC;EACF;EAEA,MAAMsB,cAAcA,CAAA,EAAyB;IAC3C,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAMf,GAAG,CAACyC,GAAG,CAAc,UAAU,CAAC;MACvD,OAAO1B,QAAQ,CAACa,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAU,EAAE;MAAA,IAAA8B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIX,KAAK,CACb,EAAAU,gBAAA,GAAA9B,KAAK,CAACG,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBV,MAAM,KAAI,4BAClC,CAAC;IACH;EACF;EAEA,MAAMW,aAAaA,CAAChB,IAA0B,EAAwB;IACpE,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMf,GAAG,CAAC6C,GAAG,CAAwB,UAAU,EAAEjB,IAAI,CAAC;MACvE,OAAOb,QAAQ,CAACa,IAAI,CAACkB,IAAI;IAC3B,CAAC,CAAC,OAAOlC,KAAU,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIhB,KAAK,CACb,EAAAe,gBAAA,GAAAnC,KAAK,CAACG,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnB,IAAI,cAAAoB,qBAAA,uBAApBA,qBAAA,CAAsBf,MAAM,KAAI,0BAClC,CAAC;IACH;EACF;EAEA,MAAMgB,cAAcA,CAACC,eAAuB,EAAEC,WAAmB,EAAiB;IAChF,IAAI;MACF,MAAMnD,GAAG,CAAC0B,IAAI,CAAC,uBAAuB,EAAE;QACtC0B,gBAAgB,EAAEF,eAAe;QACjCG,YAAY,EAAEF;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvC,KAAU,EAAE;MAAA,IAAA0C,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIvB,KAAK,CACb,EAAAsB,gBAAA,GAAA1C,KAAK,CAACG,QAAQ,cAAAuC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBtB,MAAM,KAAI,2BAClC,CAAC;IACH;EACF;EAEA,MAAMuB,aAAaA,CAAChC,KAAa,EAAiB;IAChD,IAAI;MACF,MAAMxB,GAAG,CAAC0B,IAAI,CAAC,sBAAsB,EAAE;QAAEF;MAAM,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAA6C,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAI1B,KAAK,CACb,EAAAyB,gBAAA,GAAA7C,KAAK,CAACG,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBzB,MAAM,KAAI,4BAClC,CAAC;IACH;EACF;EAEA,MAAM0B,YAAYA,CAAA,EAAoB;IACpC,IAAI;MACF,MAAM5C,QAAQ,GAAG,MAAMf,GAAG,CAAC0B,IAAI,CAA2B,eAAe,CAAC;MAC1E,MAAM;QAAEC;MAAa,CAAC,GAAGZ,QAAQ,CAACa,IAAI;MACtCnB,YAAY,CAACoB,OAAO,CAAC,cAAc,EAAEF,YAAY,CAAC;MAClD,OAAOA,YAAY;IACrB,CAAC,CAAC,OAAOf,KAAU,EAAE;MAAA,IAAAgD,gBAAA,EAAAC,qBAAA;MACnBpD,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;MACvC,MAAM,IAAIc,KAAK,CACb,EAAA4B,gBAAA,GAAAhD,KAAK,CAACG,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsB5B,MAAM,KAAI,yBAClC,CAAC;IACH;EACF;EAEA6B,eAAeA,CAAA,EAAY;IACzB,OAAO,CAAC,CAACrD,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAC/C;EAEAqD,QAAQA,CAAA,EAAkB;IACxB,OAAOtD,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAC7C;AACF;AAEA,OAAO,MAAMsD,WAAW,GAAG,IAAI1C,WAAW,CAAC,CAAC;AAC5C,SAAStB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}