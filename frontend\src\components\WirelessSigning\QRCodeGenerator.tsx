import React, { useEffect, useState } from 'react';

interface QRCodeGeneratorProps {
  data: string;
  size?: number;
  className?: string;
}

const QRCodeGenerator: React.FC<QRCodeGeneratorProps> = ({
  data,
  size = 200,
  className = ''
}) => {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    generateQRCode();
  }, [data, size]);

  const generateQRCode = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Parse the data to create a mobile-friendly URL
      const parsedData = JSON.parse(data);
      const mobileUrl = `${parsedData.mobileSigningUrl}?documentId=${parsedData.documentId}&signatureAreaId=${parsedData.signatureAreaId}&server=${encodeURIComponent(parsedData.serverUrl)}`;

      // Use Google Charts API to generate QR code (free and reliable)
      const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(mobileUrl)}`;

      setQrCodeUrl(qrApiUrl);
      setIsLoading(false);
    } catch (err) {
      console.error('Error generating QR code:', err);
      setError('Failed to generate QR code');
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`inline-block ${className}`}>
        <div
          className="border border-gray-300 rounded-lg bg-gray-100 flex items-center justify-center"
          style={{ width: size, height: size }}
        >
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-xs text-gray-500">Generating QR Code...</p>
          </div>
        </div>
        <div className="mt-2 text-center">
          <p className="text-xs text-gray-500">
            Scan with mobile device to sign
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`inline-block ${className}`}>
        <div
          className="border border-red-300 rounded-lg bg-red-50 flex items-center justify-center"
          style={{ width: size, height: size }}
        >
          <div className="text-center p-4">
            <p className="text-xs text-red-600 mb-2">QR Code Error</p>
            <p className="text-xs text-red-500">{error}</p>
          </div>
        </div>
        <div className="mt-2 text-center">
          <p className="text-xs text-gray-500">
            Manual URL available below
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`inline-block ${className}`}>
      <img
        src={qrCodeUrl}
        alt="QR Code for Mobile Signing"
        width={size}
        height={size}
        className="border border-gray-300 rounded-lg"
        onError={() => setError('Failed to load QR code')}
      />
      <div className="mt-2 text-center">
        <p className="text-xs text-gray-500">
          Scan with mobile device to sign
        </p>
      </div>
    </div>
  );
};

export default QRCodeGenerator;
