"""
Pagination utilities for CareSyncAI API
"""

from fastapi import Query
from pydantic import BaseModel
from typing import Optional
from core.config import settings

class PaginationParams(BaseModel):
    """Pagination parameters for API endpoints"""
    page: int = Query(1, ge=1, description="Page number (1-based)")
    limit: int = Query(
        settings.DEFAULT_PAGE_SIZE, 
        ge=1, 
        le=settings.MAX_PAGE_SIZE, 
        description="Number of items per page"
    )
    
    @property
    def offset(self) -> int:
        """Calculate offset for database queries"""
        return (self.page - 1) * self.limit

class PaginationResponse(BaseModel):
    """Standard pagination response format"""
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool
    
    @classmethod
    def create(cls, total_count: int, page: int, page_size: int):
        """Create pagination response"""
        total_pages = (total_count + page_size - 1) // page_size
        return cls(
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_previous=page > 1
        )

def paginate_query(query, page: int, page_size: int):
    """Apply pagination to a database query"""
    offset = (page - 1) * page_size
    return query.range(offset, offset + page_size - 1)
