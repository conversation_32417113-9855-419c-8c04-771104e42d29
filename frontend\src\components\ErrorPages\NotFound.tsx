import React from 'react';
import { Link } from 'react-router-dom';
import {
  HomeIcon,
  ExclamationTriangleIcon,
  ArrowLeftIcon,
} from '@heroicons/react/24/outline';

const NotFound: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex items-center justify-center px-4">
      <div className="max-w-lg w-full text-center">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-purple-200 rounded-full opacity-20 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-blue-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-amber-200 rounded-full opacity-20 animate-pulse delay-500"></div>
        </div>

        {/* Main Content */}
        <div className="relative z-10">
          {/* Error Icon */}
          <div className="mx-auto w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mb-8 shadow-2xl">
            <ExclamationTriangleIcon className="h-12 w-12 text-white" />
          </div>

          {/* Error Code */}
          <h1 className="text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 mb-4">
            404
          </h1>

          {/* Error Message */}
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Page Not Found
          </h2>

          <p className="text-lg text-gray-600 mb-8 leading-relaxed">
            The page you're looking for doesn't exist or may have been moved. 
            This could happen when refreshing a page or accessing a direct URL.
          </p>

          {/* Helpful Information */}
          <div className="bg-white rounded-2xl p-6 shadow-xl border-2 border-purple-100 mb-8">
            <h3 className="text-lg font-bold text-gray-900 mb-3">💡 Quick Tips:</h3>
            <ul className="text-left text-gray-600 space-y-2">
              <li className="flex items-start">
                <span className="text-purple-600 mr-2">•</span>
                <span>Always navigate from the dashboard instead of refreshing pages</span>
              </li>
              <li className="flex items-start">
                <span className="text-purple-600 mr-2">•</span>
                <span>Use the navigation menu to access different sections</span>
              </li>
              <li className="flex items-start">
                <span className="text-purple-600 mr-2">•</span>
                <span>Check if the development server is running</span>
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/dashboard"
              className="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold rounded-2xl hover:from-purple-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-xl"
            >
              <HomeIcon className="h-5 w-5 mr-2 group-hover:rotate-12 transition-transform duration-300" />
              Go to Dashboard
            </Link>

            <button
              onClick={() => window.history.back()}
              className="group inline-flex items-center px-6 py-3 bg-white text-purple-600 font-bold rounded-2xl border-2 border-purple-200 hover:bg-purple-50 hover:border-purple-300 transform hover:scale-105 transition-all duration-300 shadow-lg"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
              Go Back
            </button>
          </div>

          {/* Additional Help */}
          <div className="mt-8 text-sm text-gray-500">
            <p>
              If you continue to experience issues, try starting from the{' '}
              <Link to="/" className="text-purple-600 hover:text-purple-700 font-medium">
                home page
              </Link>
              {' '}or contact support.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
