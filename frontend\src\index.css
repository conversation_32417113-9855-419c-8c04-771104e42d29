@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  body {
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }

  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-error {
    @apply mt-1 text-sm text-danger-600;
  }

  .form-group {
    @apply mb-4;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }

  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }

  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }

  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }

  /* Alert Components */
  .alert {
    @apply p-4 rounded-md border-l-4;
  }

  .alert-success {
    @apply alert bg-secondary-50 border-secondary-400 text-secondary-700;
  }

  .alert-error {
    @apply alert bg-danger-50 border-danger-400 text-danger-700;
  }

  .alert-warning {
    @apply alert bg-warning-50 border-warning-400 text-warning-700;
  }

  .alert-info {
    @apply alert bg-primary-50 border-primary-400 text-primary-700;
  }

  /* Loading Components */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* Mobile-first responsive utilities */
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .mobile-grid {
    @apply grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3;
  }

  /* Healthcare-specific components */
  .patient-card {
    @apply card hover:shadow-medium transition-shadow duration-200 cursor-pointer;
  }

  .vitals-display {
    @apply grid grid-cols-2 gap-4 sm:grid-cols-4;
  }

  .risk-indicator {
    @apply flex items-center space-x-2;
  }

  .risk-low {
    @apply text-secondary-600;
  }

  .risk-medium {
    @apply text-warning-600;
  }

  .risk-high {
    @apply text-danger-600;
  }
}
