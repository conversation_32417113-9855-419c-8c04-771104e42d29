#!/bin/bash

# CareSyncAI Deployment Script
# This script deploys the CareSyncAI application to production

set -e

echo "🚀 Starting CareSyncAI deployment..."

# Configuration
ENVIRONMENT=${1:-production}
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    if [ ! -f ".env" ]; then
        log_error ".env file not found. Please create it from .env.example"
        exit 1
    fi
    
    log_info "Prerequisites check passed ✅"
}

# Create backup
create_backup() {
    log_info "Creating backup..."
    mkdir -p "$BACKUP_DIR"
    
    # Backup database (if using local database)
    if [ "$ENVIRONMENT" = "production" ]; then
        log_info "Backing up production data..."
        # Add database backup commands here
    fi
    
    # Backup configuration files
    cp .env "$BACKUP_DIR/"
    cp docker-compose.yml "$BACKUP_DIR/"
    
    log_info "Backup created at $BACKUP_DIR ✅"
}

# Build and deploy
deploy() {
    log_info "Building and deploying CareSyncAI..."
    
    # Pull latest images
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull
    
    # Build custom images
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
    
    # Stop existing containers
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Start services
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log_info "Deployment completed ✅"
}

# Health check
health_check() {
    log_info "Performing health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check backend health
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_info "Backend health check passed ✅"
    else
        log_error "Backend health check failed ❌"
        return 1
    fi
    
    # Check frontend
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        log_info "Frontend health check passed ✅"
    else
        log_error "Frontend health check failed ❌"
        return 1
    fi
    
    # Check AI service
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        log_info "AI service health check passed ✅"
    else
        log_warn "AI service health check failed ⚠️"
    fi
    
    log_info "Health checks completed ✅"
}

# Cleanup old images
cleanup() {
    log_info "Cleaning up old Docker images..."
    docker image prune -f
    docker volume prune -f
    log_info "Cleanup completed ✅"
}

# Main deployment process
main() {
    echo "🏥 CareSyncAI Deployment Script"
    echo "Environment: $ENVIRONMENT"
    echo "================================"
    
    check_prerequisites
    create_backup
    deploy
    health_check
    cleanup
    
    echo ""
    log_info "🎉 CareSyncAI deployment completed successfully!"
    echo ""
    echo "Services available at:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:8000"
    echo "  AI Service: http://localhost:8001"
    echo "  Grafana: http://localhost:3001"
    echo "  Prometheus: http://localhost:9090"
    echo ""
    echo "To view logs: docker-compose logs -f"
    echo "To stop services: docker-compose down"
}

# Run main function
main "$@"
