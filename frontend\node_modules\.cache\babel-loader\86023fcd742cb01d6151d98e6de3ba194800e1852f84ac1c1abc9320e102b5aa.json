{"ast": null, "code": "/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n    length = values.length,\n    offset = array.length;\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\nexport default arrayPush;", "map": {"version": 3, "names": ["arrayPush", "array", "values", "index", "length", "offset"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/node_modules/lodash-es/_arrayPush.js"], "sourcesContent": ["/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n      length = values.length,\n      offset = array.length;\n\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\n\nexport default arrayPush;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAChC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,MAAM,CAACE,MAAM;IACtBC,MAAM,GAAGJ,KAAK,CAACG,MAAM;EAEzB,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvBH,KAAK,CAACI,MAAM,GAAGF,KAAK,CAAC,GAAGD,MAAM,CAACC,KAAK,CAAC;EACvC;EACA,OAAOF,KAAK;AACd;AAEA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}