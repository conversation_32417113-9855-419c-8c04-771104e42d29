import { api } from './api';

// Types for HIPAA Service
export interface AuditLogEntry {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  userRole: string;
  action: 'view' | 'create' | 'update' | 'delete' | 'export' | 'print' | 'login' | 'logout' | 'access_denied';
  resourceType: 'resident' | 'medical_record' | 'medication' | 'document' | 'staff' | 'system' | 'phi';
  resourceId: string;
  resourceName: string;
  ipAddress: string;
  userAgent: string;
  sessionId: string;
  geolocation?: {
    latitude: number;
    longitude: number;
    city: string;
    country: string;
  };
  details: {
    fieldsAccessed?: string[];
    fieldsModified?: string[];
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    reason?: string;
    authorization?: string;
    dataClassification?: 'public' | 'internal' | 'confidential' | 'restricted';
    minimumNecessary?: boolean;
  };
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  complianceFlags: string[];
  retentionDate: string; // When this log can be purged
  tamperProof: {
    hash: string;
    signature: string;
    verified: boolean;
  };
}

export interface AccessControlPolicy {
  id: string;
  name: string;
  description: string;
  roleId: string;
  roleName: string;
  permissions: {
    [resource: string]: {
      read: boolean;
      write: boolean;
      delete: boolean;
      export: boolean;
      print: boolean;
      share: boolean;
      conditions?: {
        timeRestrictions?: {
          allowedHours: { start: string; end: string };
          allowedDays: string[];
          timezone: string;
        };
        locationRestrictions?: {
          allowedIPs: string[];
          allowedCountries: string[];
          requireVPN: boolean;
        };
        dataRestrictions?: {
          ownDataOnly: boolean;
          assignedPatientsOnly: boolean;
          departmentOnly: boolean;
          emergencyOverride: boolean;
        };
      };
    };
  };
  minimumNecessaryRule: {
    enabled: boolean;
    autoEnforce: boolean;
    requireJustification: boolean;
    approvalRequired: boolean;
  };
  createdDate: string;
  lastModified: string;
  isActive: boolean;
}

export interface PrivacyConsent {
  id: string;
  residentId: string;
  residentName: string;
  consentType: 'treatment' | 'payment' | 'operations' | 'marketing' | 'research' | 'disclosure' | 'directory';
  status: 'granted' | 'denied' | 'revoked' | 'expired' | 'pending';
  grantedDate: string;
  expiryDate?: string;
  revokedDate?: string;
  consentDetails: {
    purpose: string;
    dataTypes: string[];
    recipients: string[];
    retentionPeriod: string;
    geographicScope: string[];
    optOut: boolean;
    rightToWithdraw: boolean;
  };
  legalBasis: string;
  documentedBy: string;
  witnessedBy?: string;
  digitalSignature?: {
    signatureData: string;
    timestamp: string;
    ipAddress: string;
    certificateId: string;
  };
  consentForm: {
    formId: string;
    version: string;
    language: string;
    accessibilityCompliant: boolean;
  };
  communicationPreferences: {
    preferredMethod: 'email' | 'mail' | 'phone' | 'sms';
    frequency: 'immediate' | 'daily' | 'weekly' | 'monthly';
    optOutInstructions: string;
  };
}

export interface SecurityIncident {
  id: string;
  incidentNumber: string;
  incidentDate: string;
  discoveredDate: string;
  reportedDate: string;
  reportedBy: string;
  incidentType: 'unauthorized_access' | 'data_breach' | 'system_compromise' | 'physical_security' | 'policy_violation' | 'malware' | 'phishing';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'reported' | 'investigating' | 'contained' | 'resolved' | 'closed';
  affectedResidents: number;
  affectedRecords: number;
  affectedSystems: string[];
  description: string;
  rootCause?: string;
  timeline: Array<{
    timestamp: string;
    event: string;
    performedBy: string;
    evidence?: string[];
  }>;
  containmentActions: Array<{
    action: string;
    performedBy: string;
    timestamp: string;
    effectiveness: 'effective' | 'partial' | 'ineffective';
  }>;
  remediationActions: Array<{
    action: string;
    assignedTo: string;
    dueDate: string;
    status: 'pending' | 'in_progress' | 'completed';
    completedDate?: string;
  }>;
  preventiveActions: Array<{
    action: string;
    assignedTo: string;
    dueDate: string;
    status: 'pending' | 'in_progress' | 'completed';
  }>;
  riskAssessment: {
    probabilityOfHarm: 'low' | 'medium' | 'high';
    natureOfPHI: string[];
    whoUnauthorizedlyAccessed: string;
    wasAcquisitionLikely: boolean;
    mitigatingFactors: string[];
  };
  notificationRequired: boolean;
  notificationsSent: Array<{
    recipient: string;
    recipientType: 'individual' | 'media' | 'hhs' | 'state' | 'law_enforcement';
    method: 'email' | 'mail' | 'phone' | 'website' | 'media';
    sentDate: string;
    acknowledged: boolean;
    acknowledgedDate?: string;
  }>;
  regulatoryReporting: {
    hhs: { 
      required: boolean; 
      submitted: boolean; 
      submissionDate?: string;
      confirmationNumber?: string;
    };
    state: { 
      required: boolean; 
      submitted: boolean; 
      submissionDate?: string;
      confirmationNumber?: string;
    };
    other: Array<{ 
      agency: string; 
      required: boolean; 
      submitted: boolean; 
      submissionDate?: string;
      confirmationNumber?: string;
    }>;
  };
  legalReview: {
    required: boolean;
    reviewedBy?: string;
    reviewDate?: string;
    recommendations: string[];
  };
  costImpact: {
    investigationCosts: number;
    notificationCosts: number;
    legalCosts: number;
    regulatoryCosts: number;
    businessImpact: number;
    totalCost: number;
  };
}

export interface ComplianceAssessment {
  id: string;
  assessmentType: 'annual_risk' | 'quarterly_review' | 'incident_response' | 'penetration_test' | 'vulnerability_scan';
  assessmentDate: string;
  assessor: string;
  scope: string[];
  findings: Array<{
    id: string;
    category: 'administrative' | 'physical' | 'technical';
    severity: 'low' | 'medium' | 'high' | 'critical';
    finding: string;
    evidence: string[];
    recommendation: string;
    status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
    assignedTo?: string;
    dueDate?: string;
    resolvedDate?: string;
  }>;
  overallScore: number; // 0-100
  complianceLevel: 'non_compliant' | 'partially_compliant' | 'substantially_compliant' | 'fully_compliant';
  executiveSummary: string;
  nextAssessmentDate: string;
}

class HIPAAService {
  // Audit Logging
  async logActivity(activity: Partial<AuditLogEntry>): Promise<void> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      // In mock mode, just log to console
      console.log('HIPAA Audit Log:', activity);
      return Promise.resolve();
    }

    try {
      await api.post('/hipaa/audit-log', activity);
    } catch (error) {
      // Audit logging should never fail silently
      console.error('Failed to log audit activity:', error);
      throw new Error('Audit logging failed');
    }
  }

  // Get Audit Logs
  async getAuditLogs(filters?: {
    startDate?: string;
    endDate?: string;
    userId?: string;
    resourceType?: string;
    action?: string;
    riskLevel?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ logs: AuditLogEntry[]; total: number }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // Mock audit logs would be returned here
          resolve({ logs: [], total: 0 });
        }, 1000);
      });
    }

    try {
      const response = await api.get('/hipaa/audit-logs', { params: filters });
      return response.data;
    } catch (error) {
      throw new Error('Failed to retrieve audit logs');
    }
  }

  // Access Control Management
  async getAccessControlPolicies(): Promise<AccessControlPolicy[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([]);
        }, 800);
      });
    }

    try {
      const response = await api.get<AccessControlPolicy[]>('/hipaa/access-control-policies');
      return response.data;
    } catch (error) {
      throw new Error('Failed to get access control policies');
    }
  }

  // Privacy Consent Management
  async getPrivacyConsents(residentId?: string): Promise<PrivacyConsent[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([]);
        }, 1000);
      });
    }

    try {
      const response = await api.get<PrivacyConsent[]>('/hipaa/privacy-consents', {
        params: residentId ? { residentId } : {}
      });
      return response.data;
    } catch (error) {
      throw new Error('Failed to get privacy consents');
    }
  }

  // Create/Update Privacy Consent
  async updatePrivacyConsent(consent: Partial<PrivacyConsent>): Promise<PrivacyConsent> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ...consent,
            id: consent.id || `consent_${Date.now()}`
          } as PrivacyConsent);
        }, 1200);
      });
    }

    try {
      const response = await api.post<PrivacyConsent>('/hipaa/privacy-consents', consent);
      return response.data;
    } catch (error) {
      throw new Error('Failed to update privacy consent');
    }
  }

  // Security Incident Management
  async reportSecurityIncident(incident: Partial<SecurityIncident>): Promise<SecurityIncident> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ...incident,
            id: `incident_${Date.now()}`,
            incidentNumber: `INC-${Date.now()}`,
            status: 'reported'
          } as SecurityIncident);
        }, 1500);
      });
    }

    try {
      const response = await api.post<SecurityIncident>('/hipaa/security-incidents', incident);
      return response.data;
    } catch (error) {
      throw new Error('Failed to report security incident');
    }
  }

  // Get Security Incidents
  async getSecurityIncidents(filters?: {
    status?: string;
    severity?: string;
    incidentType?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<SecurityIncident[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([]);
        }, 1000);
      });
    }

    try {
      const response = await api.get<SecurityIncident[]>('/hipaa/security-incidents', { params: filters });
      return response.data;
    } catch (error) {
      throw new Error('Failed to get security incidents');
    }
  }

  // Compliance Assessment
  async createComplianceAssessment(assessment: Partial<ComplianceAssessment>): Promise<ComplianceAssessment> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ...assessment,
            id: `assessment_${Date.now()}`,
            overallScore: 85,
            complianceLevel: 'substantially_compliant'
          } as ComplianceAssessment);
        }, 2000);
      });
    }

    try {
      const response = await api.post<ComplianceAssessment>('/hipaa/compliance-assessments', assessment);
      return response.data;
    } catch (error) {
      throw new Error('Failed to create compliance assessment');
    }
  }

  // Generate Compliance Report
  async generateComplianceReport(reportType: 'summary' | 'detailed' | 'executive', 
                                dateRange: { start: string; end: string }): Promise<{
    reportData: any;
    complianceScore: number;
    riskLevel: string;
    recommendations: string[];
    actionItems: Array<{
      item: string;
      priority: string;
      dueDate: string;
      assignedTo: string;
    }>;
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            reportData: {},
            complianceScore: 94.5,
            riskLevel: 'low',
            recommendations: [
              'Update access control policies for new staff roles',
              'Conduct quarterly security awareness training',
              'Review and update incident response procedures'
            ],
            actionItems: [
              {
                item: 'Complete annual risk assessment',
                priority: 'high',
                dueDate: '2024-03-15',
                assignedTo: 'Security Officer'
              }
            ]
          });
        }, 2500);
      });
    }

    try {
      const response = await api.post('/hipaa/compliance-reports', { reportType, dateRange });
      return response.data;
    } catch (error) {
      throw new Error('Failed to generate compliance report');
    }
  }

  // Breach Risk Assessment
  async assessBreachRisk(incidentData: {
    affectedRecords: number;
    dataTypes: string[];
    unauthorizedParties: string[];
    safeguards: string[];
    mitigatingFactors: string[];
  }): Promise<{
    riskLevel: 'low' | 'medium' | 'high';
    notificationRequired: boolean;
    timelineRequirements: {
      individualNotification: string;
      mediaNotification?: string;
      hhsNotification: string;
    };
    recommendations: string[];
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            riskLevel: 'medium',
            notificationRequired: true,
            timelineRequirements: {
              individualNotification: '60 days',
              hhsNotification: '60 days'
            },
            recommendations: [
              'Notify affected individuals within 60 days',
              'Submit breach report to HHS',
              'Review and strengthen access controls',
              'Conduct additional staff training'
            ]
          });
        }, 1800);
      });
    }

    try {
      const response = await api.post('/hipaa/breach-risk-assessment', incidentData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to assess breach risk');
    }
  }

  // Data Encryption Status
  async getEncryptionStatus(): Promise<{
    dataAtRest: {
      encrypted: boolean;
      algorithm: string;
      keyManagement: string;
      lastRotation: string;
    };
    dataInTransit: {
      encrypted: boolean;
      protocol: string;
      certificateExpiry: string;
    };
    backups: {
      encrypted: boolean;
      location: string;
      lastBackup: string;
    };
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            dataAtRest: {
              encrypted: true,
              algorithm: 'AES-256',
              keyManagement: 'AWS KMS',
              lastRotation: '2024-01-01'
            },
            dataInTransit: {
              encrypted: true,
              protocol: 'TLS 1.3',
              certificateExpiry: '2024-12-31'
            },
            backups: {
              encrypted: true,
              location: 'AWS S3 (encrypted)',
              lastBackup: '2024-01-20'
            }
          });
        }, 1000);
      });
    }

    try {
      const response = await api.get('/hipaa/encryption-status');
      return response.data;
    } catch (error) {
      throw new Error('Failed to get encryption status');
    }
  }
}

export const hipaaService = new HIPAAService();
