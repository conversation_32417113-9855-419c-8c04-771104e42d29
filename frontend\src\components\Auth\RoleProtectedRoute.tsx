import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { hasPermission, canAccessModule, getRestrictedMessage, UserRole } from '../../utils/roleBasedAccess';
import { 
  ExclamationTriangleIcon, 
  ShieldExclamationIcon,
  LockClosedIcon,
  UserIcon
} from '@heroicons/react/24/outline';

interface RoleProtectedRouteProps {
  children: React.ReactNode;
  requiredModule: keyof import('../../utils/roleBasedAccess').RolePermissions;
  requiredAction?: keyof import('../../utils/roleBasedAccess').Permission;
  fallbackPath?: string;
  showAccessDenied?: boolean;
}

const AccessDeniedPage: React.FC<{
  userRole: UserRole;
  module: string;
  requiredAction?: string;
}> = ({ userRole, module, requiredAction }) => {
  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'from-red-500 to-pink-600';
      case 'supervisor':
        return 'from-purple-500 to-blue-600';
      case 'caregiver':
        return 'from-green-500 to-emerald-600';
      case 'billing':
        return 'from-amber-500 to-yellow-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getRoleTitle = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'System Administrator';
      case 'supervisor':
        return 'Care Supervisor';
      case 'caregiver':
        return 'Care Provider';
      case 'billing':
        return 'Billing Specialist';
      default:
        return 'User';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 flex items-center justify-center p-6">
      <div className="max-w-2xl w-full">
        {/* Main Access Denied Card */}
        <div className="relative overflow-hidden bg-gradient-to-br from-white via-red-50 to-pink-50 rounded-3xl shadow-2xl border-4 border-red-200/50 p-10">
          {/* Animated Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-red-100/30 via-pink-100/30 to-red-100/30"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-red-400/20 to-pink-500/20 rounded-full animate-pulse"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-red-300/20 to-pink-400/20 rounded-full animate-bounce"></div>
          
          <div className="relative z-10 text-center">
            {/* Icon */}
            <div className="mx-auto mb-8">
              <div className="relative p-6 bg-gradient-to-br from-red-500 to-pink-600 rounded-3xl shadow-xl">
                <ShieldExclamationIcon className="h-16 w-16 text-white" />
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full animate-ping"></div>
              </div>
            </div>

            {/* Title */}
            <h1 className="text-4xl font-black bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent mb-4">
              Access Restricted
            </h1>

            {/* Subtitle */}
            <p className="text-xl text-gray-700 mb-8">
              You don't have permission to access this {module} {requiredAction ? `(${requiredAction})` : 'module'}
            </p>

            {/* User Role Info */}
            <div className="mb-8 p-6 bg-white/70 rounded-2xl border-2 border-gray-200">
              <div className="flex items-center justify-center mb-4">
                <div className={`p-3 bg-gradient-to-r ${getRoleColor(userRole)} rounded-2xl shadow-lg`}>
                  <UserIcon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4 text-left">
                  <div className="text-lg font-bold text-gray-900">Current Role</div>
                  <div className={`text-sm font-medium bg-gradient-to-r ${getRoleColor(userRole)} bg-clip-text text-transparent`}>
                    {getRoleTitle(userRole)}
                  </div>
                </div>
              </div>
              
              <div className="text-sm text-gray-600 leading-relaxed">
                {getRestrictedMessage(userRole, module)}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.history.back()}
                className="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold rounded-2xl hover:from-purple-500 hover:to-blue-500 shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-purple-400"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative">Go Back</div>
              </button>
              
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-amber-500 to-yellow-600 text-purple-900 font-bold rounded-2xl hover:from-yellow-400 hover:to-amber-400 shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-yellow-400"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative">Return to Dashboard</div>
              </button>
            </div>

            {/* Contact Information */}
            <div className="mt-8 p-4 bg-blue-50 rounded-2xl border border-blue-200">
              <div className="flex items-center justify-center text-blue-700">
                <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
                <span className="text-sm font-medium">
                  Need access? Contact your supervisor or system administrator
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Info Cards */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Security Notice */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border-2 border-blue-200">
            <div className="flex items-center mb-4">
              <LockClosedIcon className="h-6 w-6 text-blue-600 mr-3" />
              <h3 className="text-lg font-bold text-blue-900">Security Notice</h3>
            </div>
            <p className="text-sm text-blue-700 leading-relaxed">
              This restriction is in place to protect patient privacy and ensure HIPAA compliance. 
              Access is granted based on your role and the principle of least privilege.
            </p>
          </div>

          {/* Role Information */}
          <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-6 border-2 border-purple-200">
            <div className="flex items-center mb-4">
              <UserIcon className="h-6 w-6 text-purple-600 mr-3" />
              <h3 className="text-lg font-bold text-purple-900">Role-Based Access</h3>
            </div>
            <p className="text-sm text-purple-700 leading-relaxed">
              CareSyncAI uses role-based access control to ensure that users only have access 
              to the information and functions necessary for their job responsibilities.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({
  children,
  requiredModule,
  requiredAction = 'read',
  fallbackPath = '/dashboard',
  showAccessDenied = true,
}) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
        <div className="text-center">
          <div className="relative mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full animate-spin"></div>
            <div className="absolute inset-0 w-16 h-16 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full animate-ping opacity-20"></div>
          </div>
          <p className="text-lg font-medium text-gray-700 mb-2">Checking permissions...</p>
          <p className="text-sm text-gray-500">Loading user role and access rights</p>

          {/* Debug info in development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200 text-left max-w-md">
              <p className="text-xs text-yellow-800 font-mono">
                <strong>Debug Info:</strong><br/>
                Module: {requiredModule}<br/>
                Action: {requiredAction}<br/>
                Loading: {loading ? 'true' : 'false'}<br/>
                User: {user ? 'exists' : 'null'}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  const userRole = (user.role as UserRole) || 'caregiver';

  // Check if user can access the module
  if (!canAccessModule(userRole, requiredModule)) {
    if (showAccessDenied) {
      return (
        <AccessDeniedPage 
          userRole={userRole} 
          module={requiredModule} 
          requiredAction={requiredAction}
        />
      );
    }
    return <Navigate to={fallbackPath} replace />;
  }

  // Check specific permission if required
  if (requiredAction && !hasPermission(userRole, requiredModule, requiredAction)) {
    if (showAccessDenied) {
      return (
        <AccessDeniedPage 
          userRole={userRole} 
          module={requiredModule} 
          requiredAction={requiredAction}
        />
      );
    }
    return <Navigate to={fallbackPath} replace />;
  }

  return <>{children}</>;
};

export default RoleProtectedRoute;
