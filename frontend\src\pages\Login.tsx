import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { EyeIcon, EyeSlashIcon, HeartIcon, EnvelopeIcon, LockClosedIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';

const Login: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },
    validationSchema: Yup.object({
      email: Yup.string()
        .email('Invalid email address')
        .required('Email is required'),
      password: Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .required('Password is required'),
    }),
    onSubmit: async (values, { setSubmitting, setFieldError }) => {
      try {
        await login(values.email, values.password);
        navigate('/dashboard');
      } catch (error) {
        console.error('Login failed:', error);
        setFieldError('password', 'Invalid email or password');
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <div className="min-h-screen relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      {/* Purple & Gold Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-purple-700 to-purple-900">
        {/* Purple & Gold Floating Elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full opacity-40 animate-pulse shadow-lg"></div>
        <div className="absolute top-32 right-20 w-16 h-16 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full opacity-50 animate-bounce shadow-lg"></div>
        <div className="absolute bottom-20 left-20 w-24 h-24 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full opacity-45 animate-pulse shadow-lg"></div>
        <div className="absolute bottom-32 right-10 w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-full opacity-40 animate-bounce shadow-lg"></div>
        <div className="absolute top-1/2 left-10 w-14 h-14 bg-gradient-to-r from-yellow-300 to-amber-400 rounded-full opacity-35 animate-pulse shadow-lg"></div>
        <div className="absolute top-20 right-1/3 w-18 h-18 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full opacity-40 animate-bounce shadow-lg"></div>

        {/* Healthcare Icons Background */}
        <div className="absolute top-1/4 left-1/4 opacity-15">
          <HeartIcon className="h-32 w-32 text-yellow-200 animate-pulse drop-shadow-lg" />
        </div>
        <div className="absolute bottom-1/4 right-1/4 opacity-15">
          <HeartIcon className="h-24 w-24 text-amber-100 animate-pulse drop-shadow-lg" />
        </div>
        <div className="absolute top-1/3 right-1/3 opacity-12">
          <HeartIcon className="h-20 w-20 text-yellow-100 animate-pulse drop-shadow-lg" />
        </div>

        {/* Purple & Gold Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-t from-purple-900/40 via-transparent to-purple-800/20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-amber-900/20 via-transparent to-yellow-900/20"></div>

        {/* Purple & Gold Pattern Overlay */}
        <div className="absolute inset-0 opacity-10" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,215,0,0.3) 2px, transparent 2px),
                           radial-gradient(circle at 75% 75%, rgba(147,51,234,0.2) 1px, transparent 1px)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center">
            <div className="h-20 w-20 bg-gradient-to-r from-purple-600 to-amber-600 rounded-2xl flex items-center justify-center p-1 shadow-2xl border-2 border-yellow-300/50">
              <img
                src="/care-solai-logo.jpg"
                alt="Care-SolAI Logo"
                className="h-18 w-18 object-cover rounded-xl"
              />
            </div>
          </div>
          <h2 className="mt-6 text-3xl font-bold text-white">
            Welcome Back
          </h2>
          <p className="mt-2 text-sm text-blue-100">
            Sign in to your Care-SolAI dashboard
          </p>
        </div>

        {/* Form Container */}
        <div className="bg-white/20 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-yellow-300/40 relative overflow-hidden">
          {/* Inner glow effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/15 via-yellow-500/5 to-purple-500/10 rounded-3xl"></div>
          <div className="relative z-10">
            {/* Demo Credentials */}
            {process.env.REACT_APP_MOCK_AUTH === 'true' && (
              <div className="bg-gradient-to-r from-purple-500/20 to-amber-500/20 border border-yellow-300/40 rounded-lg p-4 mb-6 shadow-lg">
                <h4 className="text-sm font-medium text-yellow-200 mb-2">🔑 Demo Credentials (2025):</h4>
                <div className="text-xs text-white space-y-1">
                  <div><strong>Admin:</strong> <EMAIL> / admin123</div>
                  <div><strong>Nurse:</strong> <EMAIL> / nurse123</div>
                  <div><strong>Caregiver:</strong> <EMAIL> / care123</div>
                </div>
              </div>
            )}

            <form onSubmit={formik.handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-white mb-1">
                  Email address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    type="email"
                    autoComplete="email"
                    className={`block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg bg-white/90 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent ${
                      formik.touched.email && formik.errors.email
                        ? 'border-red-300 focus:ring-red-500'
                        : ''
                    }`}
                    placeholder="Enter your email"
                    {...formik.getFieldProps('email')}
                  />
                </div>
                {formik.touched.email && formik.errors.email && (
                  <p className="mt-1 text-sm text-red-300">{formik.errors.email}</p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-white mb-1">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <LockClosedIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    className={`block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg bg-white/90 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent ${
                      formik.touched.password && formik.errors.password
                        ? 'border-red-300 focus:ring-red-500'
                        : ''
                    }`}
                    placeholder="Enter your password"
                    {...formik.getFieldProps('password')}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
                {formik.touched.password && formik.errors.password && (
                  <p className="mt-1 text-sm text-red-300">{formik.errors.password}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-white">
                    Remember me
                  </label>
                </div>

                <div className="text-sm">
                  <button
                    type="button"
                    className="font-medium text-yellow-300 hover:text-yellow-200 transition-colors"
                  >
                    Forgot your password?
                  </button>
                </div>
              </div>

              <div className="mt-6">
                <button
                  type="submit"
                  disabled={formik.isSubmitting}
                  className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-purple-600 to-amber-600 hover:from-purple-700 hover:to-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg"
                >
                  {formik.isSubmitting ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Signing in...
                    </div>
                  ) : (
                    'Sign in to Care-SolAI'
                  )}
                </button>
              </div>

              {/* Sign Up Link */}
              <div className="mt-6 text-center">
                <p className="text-sm text-white">
                  Don't have an account?{' '}
                  <Link
                    to="/signup"
                    className="font-medium text-yellow-300 hover:text-yellow-200 transition-colors"
                  >
                    Sign up here
                  </Link>
                </p>
              </div>
            </form>
          </div>
        </div>

        {/* Back to Landing */}
        <div className="text-center">
          <Link
            to="/"
            className="text-sm text-yellow-200 hover:text-white transition-colors"
          >
            ← Back to Home
          </Link>
        </div>

        {/* Footer Credit */}
        <div className="text-center mt-8">
          <p className="text-xs text-white/60">
            Created by <span className="text-amber-300 font-semibold">NYOHAKI</span> and <span className="text-purple-300 font-semibold">SAM INC.</span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
