import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';

const validationSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
});

const Login: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const { login } = useAuth();

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        await login(values.email, values.password);
      } catch (error) {
        // Error is handled by the auth context
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <div>
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900">Sign in to your account</h3>
        <p className="mt-1 text-sm text-gray-600">
          Access your CareSyncAI dashboard
        </p>
      </div>

      <form onSubmit={formik.handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="email" className="form-label">
            Email address
          </label>
          <input
            id="email"
            type="email"
            autoComplete="email"
            className={`form-input ${
              formik.touched.email && formik.errors.email
                ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500'
                : ''
            }`}
            placeholder="Enter your email"
            {...formik.getFieldProps('email')}
          />
          {formik.touched.email && formik.errors.email && (
            <p className="form-error">{formik.errors.email}</p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="form-label">
            Password
          </label>
          <div className="relative">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              autoComplete="current-password"
              className={`form-input pr-10 ${
                formik.touched.password && formik.errors.password
                  ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500'
                  : ''
              }`}
              placeholder="Enter your password"
              {...formik.getFieldProps('password')}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeSlashIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <EyeIcon className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
          {formik.touched.password && formik.errors.password && (
            <p className="form-error">{formik.errors.password}</p>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              id="remember-me"
              name="remember-me"
              type="checkbox"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
              Remember me
            </label>
          </div>

          <div className="text-sm">
            <button
              type="button"
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              Forgot your password?
            </button>
          </div>
        </div>

        <div>
          <button
            type="submit"
            disabled={formik.isSubmitting}
            className="btn-primary w-full flex justify-center"
          >
            {formik.isSubmitting ? (
              <div className="spinner h-5 w-5"></div>
            ) : (
              'Sign in'
            )}
          </button>
        </div>
      </form>

      <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Demo Credentials</span>
          </div>
        </div>

        <div className="mt-4 p-4 bg-gray-50 rounded-md">
          <p className="text-xs text-gray-600 mb-2">For testing purposes:</p>
          <div className="space-y-1 text-xs">
            <p><strong>Admin:</strong> <EMAIL> / password123</p>
            <p><strong>Caregiver:</strong> <EMAIL> / password123</p>
            <p><strong>Billing:</strong> <EMAIL> / password123</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
