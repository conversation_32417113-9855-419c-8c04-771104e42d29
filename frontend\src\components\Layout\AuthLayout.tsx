import React from 'react';

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="h-20 w-20 bg-gradient-to-r from-purple-600 to-amber-600 rounded-2xl flex items-center justify-center p-1 shadow-2xl border-2 border-yellow-300/50">
            <img
              src="/care-solai-logo.jpg"
              alt="Care-SolAI Logo"
              className="h-18 w-18 object-cover rounded-xl"
            />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          Care-SolAI
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          AI-Powered Homecare Management System
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {children}
        </div>
      </div>

      <div className="mt-8 text-center">
        <p className="text-xs text-gray-500">
          © 2025 Care-SolAI. All rights reserved.
        </p>
        <p className="text-xs text-gray-400 mt-2">
          Created by <span className="text-amber-500 font-semibold">NYOHAKI</span> and <span className="text-purple-500 font-semibold">SAM INC.</span>
        </p>
      </div>
    </div>
  );
};

export default AuthLayout;
