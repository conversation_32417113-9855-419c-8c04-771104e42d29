import { api } from './api';

// Types for Fax Service
export interface FaxRecipient {
  name: string;
  faxNumber: string;
  email?: string;
  phone?: string;
  organization?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country?: string;
  };
  type: 'doctor' | 'hospital' | 'pharmacy' | 'insurance' | 'family' | 'legal' | 'government' | 'other';
  department?: string;
  title?: string;
  notes?: string;
}

export interface FaxDocument {
  documentId: string;
  documentName: string;
  documentType: string;
  pages: number;
  fileUrl: string;
}

export interface FaxRequest {
  id?: string;
  recipients: FaxRecipient[];
  documents: FaxDocument[];
  coverPage?: {
    enabled: boolean;
    subject: string;
    message: string;
    urgent: boolean;
    confidential: boolean;
  };
  scheduledTime?: string;
  priority: 'normal' | 'high' | 'urgent';
  residentId?: string;
  residentName?: string;
  senderInfo: {
    name: string;
    organization: string;
    faxNumber: string;
    phone: string;
    email: string;
  };
}

export interface FaxStatus {
  id: string;
  status: 'queued' | 'sending' | 'sent' | 'failed' | 'cancelled';
  sentAt?: string;
  failureReason?: string;
  pages: number;
  cost?: number;
  attempts: number;
  recipientStatus: {
    faxNumber: string;
    status: 'pending' | 'sent' | 'failed';
    sentAt?: string;
    failureReason?: string;
  }[];
}

export interface FaxHistory {
  id: string;
  direction: 'outbound' | 'inbound';
  fromNumber: string;
  toNumber: string;
  subject: string;
  pages: number;
  status: string;
  timestamp: string;
  residentName?: string;
  documentType?: string;
  cost?: number;
}

export interface InboundFax {
  id: string;
  fromNumber: string;
  fromName?: string;
  receivedAt: string;
  pages: number;
  fileUrl: string;
  thumbnailUrl?: string;
  processed: boolean;
  aiAnalysis?: {
    documentType: string;
    confidence: number;
    extractedData: any;
    requiresAttention: boolean;
  };
}

class FaxService {
  // Send Fax
  async sendFax(faxRequest: FaxRequest): Promise<{ faxId: string; status: string; estimatedDelivery: string }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const faxId = `fax_${Date.now()}`;
          resolve({
            faxId,
            status: 'queued',
            estimatedDelivery: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5 minutes
          });
        }, 1500);
      });
    }

    // Real implementation would integrate with:
    // - RingCentral Fax API
    // - eFax API
    // - Twilio Fax
    // - SRFax API
    // - InterFAX API
    
    try {
      const response = await api.post('/fax/send', faxRequest);
      return response.data;
    } catch (error) {
      throw new Error('Failed to send fax');
    }
  }

  // Get Fax Status
  async getFaxStatus(faxId: string): Promise<FaxStatus> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const statuses = ['queued', 'sending', 'sent', 'failed'];
          const randomStatus = statuses[Math.floor(Math.random() * statuses.length)] as any;
          
          resolve({
            id: faxId,
            status: randomStatus,
            sentAt: randomStatus === 'sent' ? new Date().toISOString() : undefined,
            failureReason: randomStatus === 'failed' ? 'Busy signal - will retry' : undefined,
            pages: Math.floor(Math.random() * 5) + 1,
            cost: 0.12,
            attempts: randomStatus === 'failed' ? 2 : 1,
            recipientStatus: [
              {
                faxNumber: '******-0123',
                status: randomStatus === 'sent' ? 'sent' : randomStatus === 'failed' ? 'failed' : 'pending',
                sentAt: randomStatus === 'sent' ? new Date().toISOString() : undefined,
                failureReason: randomStatus === 'failed' ? 'Busy signal' : undefined
              }
            ]
          });
        }, 1000);
      });
    }

    try {
      const response = await api.get<FaxStatus>(`/fax/status/${faxId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to get fax status');
    }
  }

  // Get Fax History
  async getFaxHistory(filters?: {
    direction?: 'outbound' | 'inbound';
    dateRange?: { start: string; end: string };
    residentId?: string;
    status?: string;
  }): Promise<FaxHistory[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockHistory: FaxHistory[] = [
            {
              id: 'fax_001',
              direction: 'outbound',
              fromNumber: '******-0100',
              toNumber: '******-0123',
              subject: 'Care Plan Update - Margaret Thompson',
              pages: 3,
              status: 'sent',
              timestamp: '2024-01-20T10:30:00Z',
              residentName: 'Margaret Thompson',
              documentType: 'Care Plan',
              cost: 0.36
            },
            {
              id: 'fax_002',
              direction: 'inbound',
              fromNumber: '******-0456',
              toNumber: '******-0100',
              subject: 'Lab Results - William Anderson',
              pages: 2,
              status: 'received',
              timestamp: '2024-01-20T09:15:00Z',
              residentName: 'William Anderson',
              documentType: 'Lab Results'
            },
            {
              id: 'fax_003',
              direction: 'outbound',
              fromNumber: '******-0100',
              toNumber: '******-0789',
              subject: 'Insurance Authorization Request',
              pages: 4,
              status: 'failed',
              timestamp: '2024-01-19T16:45:00Z',
              residentName: 'Dorothy Garcia',
              documentType: 'Insurance Form',
              cost: 0.00
            }
          ];
          resolve(mockHistory);
        }, 1000);
      });
    }

    try {
      const response = await api.get<FaxHistory[]>('/fax/history', { params: filters });
      return response.data;
    } catch (error) {
      throw new Error('Failed to get fax history');
    }
  }

  // Get Inbound Faxes
  async getInboundFaxes(): Promise<InboundFax[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockInbound: InboundFax[] = [
            {
              id: 'inbound_001',
              fromNumber: '******-0456',
              fromName: 'Dr. Sarah Smith',
              receivedAt: '2024-01-20T09:15:00Z',
              pages: 2,
              fileUrl: '/mock-fax-1.pdf',
              thumbnailUrl: '/mock-fax-thumb-1.jpg',
              processed: true,
              aiAnalysis: {
                documentType: 'Lab Results',
                confidence: 0.94,
                extractedData: {
                  patientName: 'William Anderson',
                  testType: 'Blood Chemistry Panel',
                  results: 'Glucose elevated at 180 mg/dL'
                },
                requiresAttention: true
              }
            },
            {
              id: 'inbound_002',
              fromNumber: '******-0789',
              fromName: 'City General Hospital',
              receivedAt: '2024-01-19T14:30:00Z',
              pages: 1,
              fileUrl: '/mock-fax-2.pdf',
              processed: false,
              aiAnalysis: {
                documentType: 'Discharge Summary',
                confidence: 0.89,
                extractedData: {
                  patientName: 'Robert Davis',
                  dischargeDate: '2024-01-19',
                  diagnosis: 'Pneumonia, resolved'
                },
                requiresAttention: false
              }
            }
          ];
          resolve(mockInbound);
        }, 1200);
      });
    }

    try {
      const response = await api.get<InboundFax[]>('/fax/inbound');
      return response.data;
    } catch (error) {
      throw new Error('Failed to get inbound faxes');
    }
  }

  // Get Common Fax Recipients
  async getCommonRecipients(): Promise<FaxRecipient[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return [
        {
          name: 'Dr. Sarah Smith',
          faxNumber: '******-0123',
          email: '<EMAIL>',
          phone: '******-0124',
          organization: 'Family Medicine Associates',
          address: {
            street: '123 Medical Plaza Dr',
            city: 'Springfield',
            state: 'IL',
            zipCode: '62701'
          },
          type: 'doctor',
          department: 'Internal Medicine',
          title: 'MD, Internal Medicine'
        },
        {
          name: 'City General Hospital',
          faxNumber: '******-0456',
          email: '<EMAIL>',
          phone: '******-0457',
          organization: 'City General Hospital',
          address: {
            street: '456 Hospital Way',
            city: 'Springfield',
            state: 'IL',
            zipCode: '62702'
          },
          type: 'hospital',
          department: 'Medical Records',
          title: 'Medical Records Department'
        },
        {
          name: 'MediCare Pharmacy',
          faxNumber: '******-0789',
          email: '<EMAIL>',
          phone: '******-0790',
          organization: 'MediCare Pharmacy',
          address: {
            street: '789 Pharmacy Blvd',
            city: 'Springfield',
            state: 'IL',
            zipCode: '62703'
          },
          type: 'pharmacy',
          department: 'Prescription Services',
          title: 'Pharmacy Services'
        },
        {
          name: 'Blue Cross Insurance',
          faxNumber: '******-0321',
          email: '<EMAIL>',
          phone: '******-0322',
          organization: 'Blue Cross Blue Shield',
          address: {
            street: '321 Insurance Plaza',
            city: 'Springfield',
            state: 'IL',
            zipCode: '62704'
          },
          type: 'insurance',
          department: 'Claims Processing',
          title: 'Claims Department'
        }
      ];
    }

    try {
      const response = await api.get<FaxRecipient[]>('/fax/recipients');
      return response.data;
    } catch (error) {
      throw new Error('Failed to get fax recipients');
    }
  }

  // Cancel Fax
  async cancelFax(faxId: string): Promise<{ success: boolean; message: string }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            message: 'Fax cancelled successfully'
          });
        }, 500);
      });
    }

    try {
      const response = await api.post(`/fax/cancel/${faxId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to cancel fax');
    }
  }

  // Retry Failed Fax
  async retryFax(faxId: string): Promise<{ newFaxId: string; status: string }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            newFaxId: `fax_retry_${Date.now()}`,
            status: 'queued'
          });
        }, 800);
      });
    }

    try {
      const response = await api.post(`/fax/retry/${faxId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to retry fax');
    }
  }

  // Process Inbound Fax with AI
  async processInboundFax(faxId: string): Promise<{
    processed: boolean;
    aiAnalysis: {
      documentType: string;
      confidence: number;
      extractedData: any;
      recommendedActions: string[];
    };
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            processed: true,
            aiAnalysis: {
              documentType: 'Medical Report',
              confidence: 0.92,
              extractedData: {
                patientName: 'Margaret Thompson',
                reportType: 'Cardiology Consultation',
                findings: 'Mild mitral valve regurgitation'
              },
              recommendedActions: [
                'Add to resident medical records',
                'Schedule follow-up with primary physician',
                'Update care plan if necessary',
                'Notify family of results'
              ]
            }
          });
        }, 2000);
      });
    }

    try {
      const response = await api.post(`/fax/process/${faxId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to process inbound fax');
    }
  }
}

export const faxService = new FaxService();
