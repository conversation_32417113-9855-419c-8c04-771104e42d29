import React, { useState } from 'react';
import {
  Heart<PERSON><PERSON>,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ChartBarIcon,
  BeakerIcon,
  UserIcon,
  BellIcon,
  CakeIcon,
  ScaleIcon,
  EyeIcon,
  CogIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';

interface NutritionalProfile {
  residentId: string;
  residentName: string;
  dietaryRestrictions: string[];
  allergies: string[];
  medicalConditions: string[];
  currentMedications: string[];
  therapeuticDiet?: string;
  nutritionalGoals: string[];
  dailyCalorieTarget: number;
  fluidRestriction?: number;
  lastAssessment: string;
  bmi: number;
  weightTrend: 'gaining' | 'stable' | 'losing';
}

interface MedicationNutritionInteraction {
  medication: string;
  interactionType: 'avoid' | 'timing' | 'enhance' | 'monitor';
  foods: string[];
  description: string;
  recommendation: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface MealPlan {
  id: string;
  residentId: string;
  residentName: string;
  date: string;
  meals: {
    breakfast: MealItem[];
    lunch: MealItem[];
    dinner: MealItem[];
    snacks: MealItem[];
  };
  totalCalories: number;
  nutritionalBreakdown: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sodium: number;
  };
  medicationConsiderations: string[];
  aiOptimizations: string[];
}

interface MealItem {
  name: string;
  portion: string;
  calories: number;
  nutrients: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sodium: number;
  };
  medicationInteractions?: string[];
}

const NutritionalManagement: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'interactions' | 'meal-plans' | 'assessments'>('overview');

  // Modal states
  const [showViewPlanModal, setShowViewPlanModal] = useState(false);
  const [showOptimizeModal, setShowOptimizeModal] = useState(false);
  const [showAddInteractionModal, setShowAddInteractionModal] = useState(false);
  const [showGeneratePlanModal, setShowGeneratePlanModal] = useState(false);
  const [showNewAssessmentModal, setShowNewAssessmentModal] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<NutritionalProfile | null>(null);

  // Mock data for nutritional profiles
  const nutritionalProfiles: NutritionalProfile[] = [
    {
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      dietaryRestrictions: ['Low Sodium', 'Diabetic Diet'],
      allergies: ['Shellfish', 'Nuts'],
      medicalConditions: ['Type 2 Diabetes', 'Hypertension', 'Heart Disease'],
      currentMedications: ['Metformin', 'Lisinopril', 'Atorvastatin'],
      therapeuticDiet: 'Cardiac Diet',
      nutritionalGoals: ['Blood sugar control', 'Weight maintenance', 'Heart health'],
      dailyCalorieTarget: 1800,
      fluidRestriction: 2000,
      lastAssessment: '2024-01-15',
      bmi: 28.5,
      weightTrend: 'stable'
    },
    {
      residentId: 'R002',
      residentName: 'William Anderson',
      dietaryRestrictions: ['Soft Diet', 'Low Fat'],
      allergies: ['Dairy'],
      medicalConditions: ['Dysphagia', 'GERD', 'Osteoporosis'],
      currentMedications: ['Omeprazole', 'Calcium Carbonate', 'Vitamin D'],
      therapeuticDiet: 'Mechanical Soft Diet',
      nutritionalGoals: ['Safe swallowing', 'Adequate nutrition', 'Bone health'],
      dailyCalorieTarget: 2000,
      lastAssessment: '2024-01-18',
      bmi: 22.1,
      weightTrend: 'losing'
    }
  ];

  // Mock data for medication-nutrition interactions
  const medicationInteractions: MedicationNutritionInteraction[] = [
    {
      medication: 'Warfarin',
      interactionType: 'avoid',
      foods: ['Leafy greens', 'Broccoli', 'Brussels sprouts', 'Vitamin K supplements'],
      description: 'High vitamin K foods can interfere with anticoagulant effects',
      recommendation: 'Maintain consistent vitamin K intake, avoid large amounts',
      severity: 'high'
    },
    {
      medication: 'Metformin',
      interactionType: 'timing',
      foods: ['High-carb meals', 'Alcohol'],
      description: 'Take with meals to reduce GI side effects and improve absorption',
      recommendation: 'Take with breakfast and dinner, limit alcohol consumption',
      severity: 'medium'
    },
    {
      medication: 'Calcium Carbonate',
      interactionType: 'enhance',
      foods: ['Vitamin D rich foods', 'Dairy products'],
      description: 'Vitamin D enhances calcium absorption',
      recommendation: 'Take with vitamin D, consume with acidic foods for better absorption',
      severity: 'low'
    },
    {
      medication: 'Lisinopril',
      interactionType: 'monitor',
      foods: ['High potassium foods', 'Salt substitutes'],
      description: 'ACE inhibitors can increase potassium levels',
      recommendation: 'Monitor potassium intake, avoid excessive high-potassium foods',
      severity: 'medium'
    }
  ];

  // Mock meal plan data
  const mealPlans: MealPlan[] = [
    {
      id: 'mp_001',
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      date: '2024-01-20',
      meals: {
        breakfast: [
          {
            name: 'Oatmeal with berries',
            portion: '1 cup',
            calories: 150,
            nutrients: { protein: 5, carbs: 30, fat: 3, fiber: 4, sodium: 200 }
          },
          {
            name: 'Low-fat milk',
            portion: '8 oz',
            calories: 100,
            nutrients: { protein: 8, carbs: 12, fat: 2, fiber: 0, sodium: 120 }
          }
        ],
        lunch: [
          {
            name: 'Grilled chicken breast',
            portion: '4 oz',
            calories: 200,
            nutrients: { protein: 35, carbs: 0, fat: 4, fiber: 0, sodium: 300 }
          },
          {
            name: 'Steamed vegetables',
            portion: '1 cup',
            calories: 50,
            nutrients: { protein: 2, carbs: 10, fat: 0, fiber: 4, sodium: 150 }
          }
        ],
        dinner: [
          {
            name: 'Baked salmon',
            portion: '4 oz',
            calories: 250,
            nutrients: { protein: 30, carbs: 0, fat: 12, fiber: 0, sodium: 200 }
          }
        ],
        snacks: [
          {
            name: 'Apple slices',
            portion: '1 medium',
            calories: 80,
            nutrients: { protein: 0, carbs: 22, fat: 0, fiber: 4, sodium: 0 }
          }
        ]
      },
      totalCalories: 830,
      nutritionalBreakdown: {
        protein: 80,
        carbs: 74,
        fat: 21,
        fiber: 12,
        sodium: 970
      },
      medicationConsiderations: [
        'Metformin: Take with breakfast and dinner',
        'Low sodium content supports Lisinopril therapy'
      ],
      aiOptimizations: [
        'Increased fiber content for blood sugar control',
        'Omega-3 rich salmon supports heart health',
        'Portion sizes optimized for diabetic diet'
      ]
    }
  ];

  const getInteractionSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getInteractionIcon = (type: string) => {
    switch (type) {
      case 'avoid':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'timing':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'enhance':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      default:
        return <BellIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  // Handler functions for buttons
  const handleViewPlan = (profile: NutritionalProfile) => {
    setSelectedProfile(profile);
    setShowViewPlanModal(true);
  };

  const handleOptimize = (profile: NutritionalProfile) => {
    setSelectedProfile(profile);
    setShowOptimizeModal(true);
  };

  const handleAddInteraction = () => {
    setShowAddInteractionModal(true);
  };

  const handleGeneratePlan = () => {
    setShowGeneratePlanModal(true);
  };

  const handleNewAssessment = () => {
    setShowNewAssessmentModal(true);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'interactions', name: 'Med-Food Interactions', icon: BeakerIcon },
    { id: 'meal-plans', name: 'Meal Planning', icon: CakeIcon },
    { id: 'assessments', name: 'Assessments', icon: ScaleIcon },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Nutritional Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          AI-Enhanced nutrition planning with medication interactions for residential care
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            🍎 Smart Meal Planning
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            💊 Medication Interactions
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            🤖 AI Nutrition Optimization
          </span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Active Meal Plans</p>
              <p className="text-2xl font-bold">18</p>
            </div>
            <CakeIcon className="h-8 w-8 text-green-200" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-100 text-sm">Med Interactions</p>
              <p className="text-2xl font-bold">12</p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-yellow-200" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Dietary Restrictions</p>
              <p className="text-2xl font-bold">8</p>
            </div>
            <BellIcon className="h-8 w-8 text-blue-200" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">AI Optimizations</p>
              <p className="text-2xl font-bold">25</p>
            </div>
            <BeakerIcon className="h-8 w-8 text-purple-200" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {selectedTab === 'overview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Nutritional Profiles Overview</h3>
              {nutritionalProfiles.map((profile) => (
                <div key={profile.residentId} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <UserIcon className="h-5 w-5 text-blue-500" />
                        <div>
                          <h4 className="font-medium text-gray-900">{profile.residentName}</h4>
                          <p className="text-sm text-gray-600">BMI: {profile.bmi} • Weight: {profile.weightTrend}</p>
                        </div>
                      </div>
                      
                      <div className="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700">Dietary Restrictions</p>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {profile.dietaryRestrictions.map((restriction, index) => (
                              <span key={index} className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                                {restriction}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        <div>
                          <p className="text-sm font-medium text-gray-700">Current Medications</p>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {profile.currentMedications.slice(0, 2).map((med, index) => (
                              <span key={index} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                                {med}
                              </span>
                            ))}
                            {profile.currentMedications.length > 2 && (
                              <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                                +{profile.currentMedications.length - 2} more
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <div>
                          <p className="text-sm font-medium text-gray-700">Daily Calories</p>
                          <p className="text-sm text-gray-900">{profile.dailyCalorieTarget} kcal</p>
                          {profile.fluidRestriction && (
                            <p className="text-xs text-gray-600">Fluid: {profile.fluidRestriction}ml/day</p>
                          )}
                        </div>
                      </div>

                      {profile.therapeuticDiet && (
                        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
                          <p className="text-sm font-medium text-green-800">Therapeutic Diet: {profile.therapeuticDiet}</p>
                          <p className="text-xs text-green-600 mt-1">
                            Goals: {profile.nutritionalGoals.join(', ')}
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <div className="ml-4 flex flex-col space-y-2">
                      <button
                        onClick={() => handleViewPlan(profile)}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                      >
                        <EyeIcon className="h-4 w-4 inline mr-1" />
                        View Plan
                      </button>
                      <button
                        onClick={() => handleOptimize(profile)}
                        className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
                      >
                        <CogIcon className="h-4 w-4 inline mr-1" />
                        Optimize
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'interactions' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Medication-Food Interactions</h3>
                <button
                  onClick={handleAddInteraction}
                  className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 inline mr-1" />
                  Add Interaction
                </button>
              </div>
              
              {medicationInteractions.map((interaction, index) => (
                <div key={index} className={`border rounded-lg p-4 ${getInteractionSeverityColor(interaction.severity)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        {getInteractionIcon(interaction.interactionType)}
                        <div>
                          <h4 className="font-medium text-gray-900">{interaction.medication}</h4>
                          <p className="text-sm text-gray-600 capitalize">
                            {interaction.interactionType} interaction
                          </p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getInteractionSeverityColor(interaction.severity)}`}>
                          {interaction.severity.toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="mt-3">
                        <p className="text-sm text-gray-800">{interaction.description}</p>
                        <p className="text-sm font-medium text-gray-900 mt-2">
                          Affected Foods: {interaction.foods.join(', ')}
                        </p>
                        <div className="mt-2 p-2 bg-white bg-opacity-50 rounded-md">
                          <p className="text-sm font-medium text-gray-800">Recommendation:</p>
                          <p className="text-sm text-gray-700">{interaction.recommendation}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'meal-plans' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">AI-Optimized Meal Plans</h3>
                <button
                  onClick={handleGeneratePlan}
                  className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
                >
                  <PlusIcon className="h-4 w-4 inline mr-1" />
                  Generate Plan
                </button>
              </div>
              
              {mealPlans.map((plan) => (
                <div key={plan.id} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">{plan.residentName}</h4>
                      <p className="text-sm text-gray-600">Date: {plan.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-semibold text-gray-900">{plan.totalCalories} kcal</p>
                      <p className="text-sm text-gray-600">Daily Total</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    {Object.entries(plan.meals).map(([mealType, items]) => (
                      <div key={mealType} className="border border-gray-100 rounded-lg p-3">
                        <h5 className="font-medium text-gray-900 capitalize mb-2">{mealType}</h5>
                        <div className="space-y-1">
                          {items.map((item, index) => (
                            <div key={index} className="text-sm">
                              <p className="text-gray-900">{item.name}</p>
                              <p className="text-gray-600">{item.portion} • {item.calories} kcal</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-purple-50 border border-purple-200 rounded-md">
                      <h5 className="text-sm font-medium text-purple-800 mb-2">🤖 AI Optimizations</h5>
                      <ul className="text-sm text-purple-700 space-y-1">
                        {plan.aiOptimizations.map((optimization, index) => (
                          <li key={index}>• {optimization}</li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <h5 className="text-sm font-medium text-blue-800 mb-2">💊 Medication Considerations</h5>
                      <ul className="text-sm text-blue-700 space-y-1">
                        {plan.medicationConsiderations.map((consideration, index) => (
                          <li key={index}>• {consideration}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'assessments' && (
            <div className="text-center py-8">
              <ScaleIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Nutritional Assessments</h3>
              <p className="mt-1 text-sm text-gray-500">
                Track weight changes, nutritional intake, and health outcomes
              </p>
              <button
                onClick={handleNewAssessment}
                className="mt-4 px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                <PlusIcon className="h-4 w-4 inline mr-1" />
                New Assessment
              </button>
            </div>
          )}
        </div>
      </div>
      {/* View Plan Modal */}
      {showViewPlanModal && selectedProfile && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowViewPlanModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <EyeIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Nutritional Plan Details
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedProfile.residentName}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowViewPlanModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Current Plan Overview */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-900 mb-3">Current Nutritional Plan</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-blue-700">Daily Calorie Target:</p>
                        <p className="text-lg text-blue-600">{selectedProfile.dailyCalorieTarget} calories</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-blue-700">BMI:</p>
                        <p className="text-lg text-blue-600">{selectedProfile.bmi}</p>
                      </div>
                      {selectedProfile.therapeuticDiet && (
                        <div className="md:col-span-2">
                          <p className="text-sm font-medium text-blue-700">Therapeutic Diet:</p>
                          <p className="text-blue-600">{selectedProfile.therapeuticDiet}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Dietary Restrictions & Allergies */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <h4 className="text-md font-medium text-red-900 mb-3">Allergies</h4>
                      <ul className="space-y-1">
                        {selectedProfile.allergies.map((allergy, index) => (
                          <li key={index} className="text-sm text-red-700 flex items-center">
                            <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                            {allergy}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h4 className="text-md font-medium text-yellow-900 mb-3">Dietary Restrictions</h4>
                      <ul className="space-y-1">
                        {selectedProfile.dietaryRestrictions.map((restriction, index) => (
                          <li key={index} className="text-sm text-yellow-700 flex items-center">
                            <ClockIcon className="h-4 w-4 mr-2" />
                            {restriction}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Nutritional Goals */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-green-900 mb-3">Nutritional Goals</h4>
                    <ul className="space-y-2">
                      {selectedProfile.nutritionalGoals.map((goal, index) => (
                        <li key={index} className="text-sm text-green-700 flex items-center">
                          <CheckCircleIcon className="h-4 w-4 mr-2" />
                          {goal}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Medical Conditions */}
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-purple-900 mb-3">Medical Conditions</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-purple-700 mb-2">Conditions:</p>
                        <ul className="space-y-1">
                          {selectedProfile.medicalConditions.map((condition, index) => (
                            <li key={index} className="text-sm text-purple-600">• {condition}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-purple-700 mb-2">Current Medications:</p>
                        <ul className="space-y-1">
                          {selectedProfile.currentMedications.map((medication, index) => (
                            <li key={index} className="text-sm text-purple-600">• {medication}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => setShowViewPlanModal(false)}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Optimize Modal */}
      {showOptimizeModal && selectedProfile && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowOptimizeModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                      <CogIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Optimize Nutrition Plan
                      </h3>
                      <p className="text-sm text-gray-500">
                        AI-powered optimization for {selectedProfile.residentName}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowOptimizeModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* AI Optimization Options */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-green-900 mb-3 flex items-center">
                      <BeakerIcon className="h-5 w-5 mr-2" />
                      AI Optimization Parameters
                    </h4>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-green-700">Optimize for medical conditions</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-green-700">Consider medication interactions</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-green-700">Adjust for seasonal preferences</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-green-700">Optimize caloric distribution</span>
                      </label>
                    </div>
                  </div>

                  {/* Optimization Goals */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Primary Optimization Goal
                    </label>
                    <select className="block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm">
                      <option>Weight management</option>
                      <option>Blood sugar control</option>
                      <option>Heart health</option>
                      <option>Bone health</option>
                      <option>Overall wellness</option>
                    </select>
                  </div>

                  {/* AI Recommendations Preview */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-900 mb-3">AI Recommendations Preview</h4>
                    <ul className="space-y-2 text-sm text-blue-700">
                      <li>• Increase fiber intake by 15% for better blood sugar control</li>
                      <li>• Add omega-3 rich foods twice weekly for heart health</li>
                      <li>• Adjust meal timing to optimize medication absorption</li>
                      <li>• Include more calcium-rich foods for bone health</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Nutrition plan optimized successfully! New recommendations have been generated.');
                    setShowOptimizeModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Optimize Plan
                </button>
                <button
                  onClick={() => setShowOptimizeModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Interaction Modal */}
      {showAddInteractionModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddInteractionModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <PlusIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Add Med-Food Interaction
                      </h3>
                      <p className="text-sm text-gray-500">
                        Document a new medication-food interaction
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAddInteractionModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Medication *</label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Enter medication name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Food/Nutrient *</label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Enter food or nutrient"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Interaction Type</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                      <option value="avoid">Avoid - Do not consume together</option>
                      <option value="timing">Timing - Separate consumption</option>
                      <option value="enhance">Enhance - Improves absorption</option>
                      <option value="monitor">Monitor - Requires observation</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Severity</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="critical">Critical</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <textarea
                      rows={3}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Describe the interaction and its effects..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Recommendations</label>
                    <textarea
                      rows={2}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Recommended actions or precautions..."
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Med-food interaction added successfully!');
                    setShowAddInteractionModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Add Interaction
                </button>
                <button
                  onClick={() => setShowAddInteractionModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Generate Plan Modal */}
      {showGeneratePlanModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowGeneratePlanModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                      <CakeIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Generate AI Meal Plan
                      </h3>
                      <p className="text-sm text-gray-500">
                        Create an optimized meal plan using AI
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowGeneratePlanModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Select Resident</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm">
                      <option>Select a resident...</option>
                      {nutritionalProfiles.map((profile) => (
                        <option key={profile.residentId} value={profile.residentId}>
                          {profile.residentName}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Plan Duration</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm">
                      <option value="1">1 Day</option>
                      <option value="3">3 Days</option>
                      <option value="7">1 Week</option>
                      <option value="14">2 Weeks</option>
                      <option value="30">1 Month</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Meal Types</label>
                    <div className="mt-2 space-y-2">
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded" />
                        <span className="ml-2 text-sm text-gray-700">Breakfast</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded" />
                        <span className="ml-2 text-sm text-gray-700">Lunch</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded" />
                        <span className="ml-2 text-sm text-gray-700">Dinner</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded" />
                        <span className="ml-2 text-sm text-gray-700">Snacks</span>
                      </label>
                    </div>
                  </div>

                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <h4 className="text-sm font-medium text-green-800 mb-2">AI Generation Features</h4>
                    <ul className="text-xs text-green-700 space-y-1">
                      <li>• Considers all dietary restrictions and allergies</li>
                      <li>• Optimizes for medical conditions</li>
                      <li>• Balances nutritional requirements</li>
                      <li>• Includes variety and seasonal preferences</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('AI meal plan generated successfully! The plan has been added to the meal planning section.');
                    setShowGeneratePlanModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Generate Plan
                </button>
                <button
                  onClick={() => setShowGeneratePlanModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* New Assessment Modal */}
      {showNewAssessmentModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowNewAssessmentModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <ScaleIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        New Nutritional Assessment
                      </h3>
                      <p className="text-sm text-gray-500">
                        Create a comprehensive nutritional assessment
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowNewAssessmentModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Select Resident</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                      <option>Select a resident...</option>
                      {nutritionalProfiles.map((profile) => (
                        <option key={profile.residentId} value={profile.residentId}>
                          {profile.residentName}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Assessment Type</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                      <option>Comprehensive Nutritional Assessment</option>
                      <option>Weight Management Assessment</option>
                      <option>Diabetic Diet Assessment</option>
                      <option>Cardiac Diet Assessment</option>
                      <option>Swallowing Assessment</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Assessment Date</label>
                    <input
                      type="date"
                      defaultValue={new Date().toISOString().split('T')[0]}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notes</label>
                    <textarea
                      rows={3}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Initial observations or specific concerns..."
                    />
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <h4 className="text-sm font-medium text-blue-800 mb-2">Assessment Will Include</h4>
                    <ul className="text-xs text-blue-700 space-y-1">
                      <li>• Current weight and BMI calculation</li>
                      <li>• Dietary intake analysis</li>
                      <li>• Medical condition considerations</li>
                      <li>• Medication interaction review</li>
                      <li>• AI-powered recommendations</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('New nutritional assessment created successfully! You can now complete the detailed assessment form.');
                    setShowNewAssessmentModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Create Assessment
                </button>
                <button
                  onClick={() => setShowNewAssessmentModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NutritionalManagement;
