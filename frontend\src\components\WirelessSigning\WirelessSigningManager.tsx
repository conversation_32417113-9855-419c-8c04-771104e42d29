import React, { useState, useEffect, useRef } from 'react';
import {
  WifiIcon,
  DevicePhoneMobileIcon,
  QrCodeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  ClockIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import QRCodeGenerator from './QRCodeGenerator';

interface WirelessSigningManagerProps {
  documentId: string;
  signatureAreaId: string;
  onSignatureReceived: (signatureData: string) => void;
  onClose: () => void;
}

interface ConnectedDevice {
  id: string;
  name: string;
  type: 'mobile' | 'tablet' | 'desktop';
  status: 'connected' | 'signing' | 'completed';
}

const WirelessSigningManager: React.FC<WirelessSigningManagerProps> = ({
  documentId,
  signatureAreaId,
  onSignatureReceived,
  onClose
}) => {
  const [connectionMethod, setConnectionMethod] = useState<'wifi' | 'bluetooth' | 'qr'>('qr');
  const [connectedDevices, setConnectedDevices] = useState<ConnectedDevice[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [qrCode, setQrCode] = useState<string>('');
  const [signatureStatus, setSignatureStatus] = useState<'waiting' | 'receiving' | 'completed'>('waiting');
  const [wsConnectionStatus, setWsConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const wsRef = useRef<WebSocket | null>(null);
  const messageQueueRef = useRef<string[]>([]);

  useEffect(() => {
    // Initialize WebSocket connection for real-time communication
    initializeWebSocket();
    
    // Generate QR code for mobile connection
    if (connectionMethod === 'qr') {
      generateQRCode();
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [connectionMethod, documentId, signatureAreaId]);

  const sendMessage = (message: any) => {
    const messageStr = JSON.stringify(message);

    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(messageStr);
    } else {
      // Queue message for when connection is ready
      messageQueueRef.current.push(messageStr);
    }
  };

  const processMessageQueue = () => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      while (messageQueueRef.current.length > 0) {
        const message = messageQueueRef.current.shift();
        if (message) {
          wsRef.current.send(message);
        }
      }
    }
  };

  const initializeWebSocket = () => {
    if (wsConnectionStatus === 'connecting' || wsConnectionStatus === 'connected') {
      return; // Already connecting or connected
    }

    setWsConnectionStatus('connecting');

    try {
      const ws = new WebSocket('ws://localhost:8001/ws/signing');
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('Connected to wireless signing server');
        setWsConnectionStatus('connected');

        // Process any queued messages
        processMessageQueue();

        // Register as document viewer
        sendMessage({
          type: 'register_viewer',
          document_id: documentId,
          signature_area_id: signatureAreaId
        });
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      };

      ws.onclose = () => {
        console.log('Disconnected from wireless signing server');
        setWsConnectionStatus('disconnected');
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setWsConnectionStatus('disconnected');

        // Show user-friendly message
        setTimeout(() => {
          if (wsConnectionStatus === 'disconnected') {
            console.warn('WebSocket server not available. Wireless signing features will be limited.');
          }
        }, 2000);
      };
    } catch (error) {
      console.error('Failed to connect to wireless signing server:', error);
      setWsConnectionStatus('disconnected');
    }
  };

  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'device_connected':
        setConnectedDevices(prev => [...prev, {
          id: data.device_id,
          name: data.device_name,
          type: data.device_type,
          status: 'connected'
        }]);
        break;
      
      case 'device_disconnected':
        setConnectedDevices(prev => prev.filter(device => device.id !== data.device_id));
        break;
      
      case 'signature_started':
        setSignatureStatus('receiving');
        setConnectedDevices(prev => prev.map(device => 
          device.id === data.device_id 
            ? { ...device, status: 'signing' }
            : device
        ));
        break;
      
      case 'signature_completed':
        setSignatureStatus('completed');
        onSignatureReceived(data.signature_data);
        setConnectedDevices(prev => prev.map(device => 
          device.id === data.device_id 
            ? { ...device, status: 'completed' }
            : device
        ));
        break;
      
      case 'signature_cancelled':
        setSignatureStatus('waiting');
        setConnectedDevices(prev => prev.map(device => 
          device.id === data.device_id 
            ? { ...device, status: 'connected' }
            : device
        ));
        break;
    }
  };

  const generateQRCode = () => {
    const qrData = {
      documentId,
      signatureAreaId,
      serverUrl: 'ws://localhost:8001/ws/signing',
      mobileSigningUrl: `${window.location.origin}/mobile-signing`,
      timestamp: Date.now(),
    };
    
    setQrCode(JSON.stringify(qrData));
  };

  const scanForDevices = async () => {
    setIsScanning(true);

    if (connectionMethod === 'wifi') {
      // Scan for devices on WiFi network
      sendMessage({
        type: 'scan_wifi_devices'
      });
    } else if (connectionMethod === 'bluetooth') {
      // Scan for Bluetooth devices
      try {
        if ('bluetooth' in navigator) {
          const device = await (navigator as any).bluetooth.requestDevice({
            acceptAllDevices: true,
            optionalServices: ['battery_service']
          });
          
          setConnectedDevices(prev => [...prev, {
            id: device.id,
            name: device.name || 'Unknown Device',
            type: 'mobile',
            status: 'connected'
          }]);
        }
      } catch (error) {
        console.error('Bluetooth scanning failed:', error);
      }
    }
    
    setTimeout(() => setIsScanning(false), 3000);
  };

  const sendSignatureRequest = (deviceId: string) => {
    sendMessage({
      type: 'signature_request',
      device_id: deviceId,
      document_id: documentId,
      signature_area_id: signatureAreaId,
      document_title: `Document ${documentId}`,
      signature_area_label: `Signature Area ${signatureAreaId}`
    });
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 sm:mx-0 sm:h-10 sm:w-10">
                <DevicePhoneMobileIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Wireless Document Signing
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-500">
                    Connect a mobile device to sign this document wirelessly
                  </p>
                </div>
              </div>
            </div>

            {/* Connection Method Selection */}
            <div className="mt-6">
              <label className="text-sm font-medium text-gray-700">Connection Method</label>
              <div className="mt-2 flex space-x-4">
                <button
                  onClick={() => setConnectionMethod('wifi')}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    connectionMethod === 'wifi'
                      ? 'bg-purple-100 text-purple-700 border-purple-300'
                      : 'bg-gray-100 text-gray-700 border-gray-300'
                  } border`}
                >
                  <WifiIcon className="h-4 w-4 mr-2" />
                  WiFi Network
                </button>
                <button
                  onClick={() => setConnectionMethod('bluetooth')}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    connectionMethod === 'bluetooth'
                      ? 'bg-purple-100 text-purple-700 border-purple-300'
                      : 'bg-gray-100 text-gray-700 border-gray-300'
                  } border`}
                >
                  <DevicePhoneMobileIcon className="h-4 w-4 mr-2" />
                  Bluetooth
                </button>
                <button
                  onClick={() => setConnectionMethod('qr')}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    connectionMethod === 'qr'
                      ? 'bg-purple-100 text-purple-700 border-purple-300'
                      : 'bg-gray-100 text-gray-700 border-gray-300'
                  } border`}
                >
                  <QrCodeIcon className="h-4 w-4 mr-2" />
                  QR Code
                </button>
              </div>
            </div>

            {/* Connection Status Warning */}
            {wsConnectionStatus === 'disconnected' && (
              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2" />
                  <span className="text-sm text-yellow-800">
                    Wireless signing server is not available. Please start the WebSocket server or use traditional e-signing.
                  </span>
                </div>
              </div>
            )}

            {/* QR Code Display */}
            {connectionMethod === 'qr' && qrCode && wsConnectionStatus === 'connected' && (
              <div className="border border-gray-200 rounded-lg p-4 text-center mb-4">
                <QRCodeGenerator
                  data={qrCode}
                  size={200}
                  className="mx-auto"
                />
                <p className="text-sm text-gray-600 mt-2">
                  Scan this QR code with your mobile device to sign the document
                </p>
                <div className="mt-3 p-2 bg-blue-50 rounded text-xs text-blue-800">
                  <div className="mb-1">
                    <strong>Manual URL:</strong>
                  </div>
                  <div className="break-all">
                    {window.location.origin}/mobile-signing?documentId={documentId}&signatureAreaId={signatureAreaId}&server={encodeURIComponent('ws://localhost:8001/ws/signing')}
                  </div>
                </div>
                <div className="mt-2 p-2 bg-gray-50 rounded text-xs text-gray-600">
                  <strong>Instructions:</strong> Open the URL above on your mobile device or scan the QR code
                </div>
              </div>
            )}

            {/* QR Code Placeholder when disconnected */}
            {connectionMethod === 'qr' && wsConnectionStatus !== 'connected' && (
              <div className="border border-gray-200 rounded-lg p-4 text-center mb-4 bg-gray-50">
                <div className="w-48 h-48 mx-auto bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500 text-sm">QR Code unavailable</span>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  WebSocket server required for QR code generation
                </p>
              </div>
            )}

            {/* Device Scanning */}
            {(connectionMethod === 'wifi' || connectionMethod === 'bluetooth') && (
              <div className="mt-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-700">Available Devices</span>
                  <button
                    onClick={scanForDevices}
                    disabled={isScanning}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-purple-700 bg-purple-100 hover:bg-purple-200 disabled:opacity-50"
                  >
                    {isScanning ? 'Scanning...' : 'Scan for Devices'}
                  </button>
                </div>
                
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {connectedDevices.map((device) => (
                    <div key={device.id} className="flex items-center justify-between p-2 border border-gray-200 rounded">
                      <div className="flex items-center">
                        <DevicePhoneMobileIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-900">{device.name}</span>
                        <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          device.status === 'connected' ? 'bg-green-100 text-green-800' :
                          device.status === 'signing' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {device.status}
                        </span>
                      </div>
                      {device.status === 'connected' && (
                        <button
                          onClick={() => sendSignatureRequest(device.id)}
                          className="text-xs text-purple-600 hover:text-purple-800"
                        >
                          Request Signature
                        </button>
                      )}
                    </div>
                  ))}
                  {connectedDevices.length === 0 && (
                    <p className="text-sm text-gray-500 text-center py-4">
                      No devices found. Click "Scan for Devices" to search.
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Status Display */}
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  {signatureStatus === 'waiting' && (
                    <>
                      <ClockIcon className="h-5 w-5 text-yellow-500 mr-2" />
                      <span className="text-sm text-gray-700">Waiting for device connection...</span>
                    </>
                  )}
                  {signatureStatus === 'receiving' && (
                    <>
                      <ArrowPathIcon className="h-5 w-5 text-blue-500 mr-2 animate-spin" />
                      <span className="text-sm text-gray-700">Receiving signature...</span>
                    </>
                  )}
                  {signatureStatus === 'completed' && (
                    <>
                      <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-sm text-gray-700">Signature received successfully!</span>
                    </>
                  )}
                </div>

                {/* WebSocket Connection Status */}
                <div className="flex items-center">
                  <div className={`h-2 w-2 rounded-full mr-2 ${
                    wsConnectionStatus === 'connected' ? 'bg-green-500' :
                    wsConnectionStatus === 'connecting' ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`} />
                  <span className="text-xs text-gray-500">
                    {wsConnectionStatus === 'connected' ? 'Connected' :
                     wsConnectionStatus === 'connecting' ? 'Connecting...' :
                     'Disconnected'}
                  </span>
                </div>
              </div>
              <div className="text-xs text-gray-500">
                Connected Devices: {connectedDevices.length}
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gray-600 text-base font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WirelessSigningManager;
