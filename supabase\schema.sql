-- CareSyncAI Database Schema
-- Optimized for homecare facilities with HIPAA compliance

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'caregiver', 'supervisor', 'billing');
CREATE TYPE patient_status AS ENUM ('active', 'inactive', 'discharged', 'deceased');
CREATE TYPE record_type AS ENUM ('care_plan', 'medical_history', 'insurance', 'consent', 'assessment');
CREATE TYPE billing_status AS ENUM ('draft', 'sent', 'paid', 'overdue', 'cancelled');
CREATE TYPE inventory_status AS ENUM ('in_stock', 'low_stock', 'out_of_stock', 'discontinued');

-- Caregivers table
CREATE TABLE caregivers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    role user_role DEFAULT 'caregiver',
    license_number VARCHAR(50),
    license_expiry DATE,
    is_active BOOLEAN DEFAULT true,
    hire_date DATE DEFAULT CURRENT_DATE,
    address JSONB,
    emergency_contact JSONB,
    certifications TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Patients table
CREATE TABLE patients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_number VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    gender VARCHAR(20),
    ssn_encrypted TEXT, -- Encrypted SSN for HIPAA compliance
    phone VARCHAR(20),
    email VARCHAR(255),
    address JSONB NOT NULL,
    emergency_contacts JSONB,
    insurance_info JSONB,
    primary_caregiver_id UUID REFERENCES caregivers(id),
    backup_caregiver_id UUID REFERENCES caregivers(id),
    status patient_status DEFAULT 'active',
    admission_date DATE DEFAULT CURRENT_DATE,
    discharge_date DATE,
    medical_conditions TEXT[],
    allergies TEXT[],
    medications JSONB,
    care_level INTEGER DEFAULT 1, -- 1-5 scale
    mobility_score INTEGER DEFAULT 5, -- 1-10 scale
    cognitive_score INTEGER DEFAULT 10, -- 1-10 scale
    fall_risk_score DECIMAL(3,2) DEFAULT 0.0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Medical records table
CREATE TABLE medical_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    caregiver_id UUID REFERENCES caregivers(id),
    record_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    vitals JSONB, -- heart_rate, blood_pressure, temperature, oxygen_saturation, weight
    symptoms TEXT[],
    medications_given JSONB,
    activities_performed TEXT[],
    mood_assessment VARCHAR(50),
    pain_level INTEGER CHECK (pain_level >= 0 AND pain_level <= 10),
    notes TEXT,
    incident_report BOOLEAN DEFAULT false,
    requires_attention BOOLEAN DEFAULT false,
    ai_analysis JSONB, -- AI-generated insights
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Records/Documents table
CREATE TABLE records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    uploaded_by UUID REFERENCES caregivers(id),
    document_name VARCHAR(255) NOT NULL,
    document_type record_type NOT NULL,
    file_url TEXT NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    is_encrypted BOOLEAN DEFAULT true,
    access_level INTEGER DEFAULT 1, -- 1=all, 2=supervisors, 3=admin only
    expiry_date DATE,
    tags TEXT[],
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Shop inventory table
CREATE TABLE shop_inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    item_name VARCHAR(255) NOT NULL,
    item_code VARCHAR(50) UNIQUE,
    category VARCHAR(100),
    description TEXT,
    current_quantity INTEGER DEFAULT 0,
    reorder_level INTEGER DEFAULT 10,
    max_quantity INTEGER DEFAULT 100,
    unit_cost DECIMAL(10,2),
    supplier_info JSONB,
    expiry_date DATE,
    status inventory_status DEFAULT 'in_stock',
    location VARCHAR(100),
    last_restocked DATE,
    usage_rate DECIMAL(5,2), -- items per day
    ai_reorder_prediction JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Billing table
CREATE TABLE billing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    caregiver_id UUID REFERENCES caregivers(id),
    invoice_number VARCHAR(50) UNIQUE,
    zoho_invoice_id VARCHAR(100),
    billing_period_start DATE NOT NULL,
    billing_period_end DATE NOT NULL,
    services_provided JSONB, -- detailed breakdown of services
    hours_worked DECIMAL(5,2),
    hourly_rate DECIMAL(8,2),
    subtotal DECIMAL(10,2),
    tax_amount DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    status billing_status DEFAULT 'draft',
    due_date DATE,
    paid_date DATE,
    payment_method VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI predictions table
CREATE TABLE ai_predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    prediction_type VARCHAR(100) NOT NULL, -- 'fall_risk', 'health_decline', 'medication_adherence'
    prediction_value DECIMAL(5,4), -- probability score 0-1
    confidence_score DECIMAL(5,4),
    factors JSONB, -- contributing factors
    recommendation TEXT,
    model_version VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Care schedules table
CREATE TABLE care_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    caregiver_id UUID REFERENCES caregivers(id),
    scheduled_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    service_type VARCHAR(100),
    status VARCHAR(50) DEFAULT 'scheduled',
    actual_start_time TIMESTAMP WITH TIME ZONE,
    actual_end_time TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit log for HIPAA compliance
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_patients_caregiver ON patients(primary_caregiver_id);
CREATE INDEX idx_patients_status ON patients(status);
CREATE INDEX idx_medical_records_patient ON medical_records(patient_id);
CREATE INDEX idx_medical_records_date ON medical_records(record_date);
CREATE INDEX idx_records_patient ON records(patient_id);
CREATE INDEX idx_billing_patient ON billing(patient_id);
CREATE INDEX idx_billing_status ON billing(status);
CREATE INDEX idx_inventory_status ON shop_inventory(status);
CREATE INDEX idx_ai_predictions_patient ON ai_predictions(patient_id);
CREATE INDEX idx_care_schedules_date ON care_schedules(scheduled_date);
CREATE INDEX idx_audit_log_user ON audit_log(user_id);
CREATE INDEX idx_audit_log_created ON audit_log(created_at);

-- Row Level Security (RLS) policies
ALTER TABLE caregivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE records ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE care_schedules ENABLE ROW LEVEL SECURITY;
