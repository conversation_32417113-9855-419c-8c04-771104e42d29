"""
Zoho Books API integration service for CareSyncAI
"""

import httpx
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, date
from core.config import settings

logger = logging.getLogger(__name__)

class ZohoAuthError(Exception):
    """Zoho authentication error"""
    pass

class <PERSON>oh<PERSON><PERSON>IError(Exception):
    """Zoho API error"""
    pass

class ZohoBooksService:
    """Service for Zoho Books API integration"""
    
    def __init__(self):
        self.base_url = settings.ZOHO_BASE_URL
        self.client_id = settings.ZOHO_CLIENT_ID
        self.client_secret = settings.ZOHO_CLIENT_SECRET
        self.refresh_token = settings.ZOHO_REFRESH_TOKEN
        self.organization_id = settings.ZOHO_ORGANIZATION_ID
        self.access_token = None
        self.token_expires_at = None
    
    async def _get_access_token(self) -> str:
        """Get or refresh access token"""
        if (self.access_token and self.token_expires_at and 
            datetime.now() < self.token_expires_at):
            return self.access_token
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://accounts.zoho.com/oauth/v2/token",
                    data={
                        "refresh_token": self.refresh_token,
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "grant_type": "refresh_token"
                    }
                )
                
                if response.status_code != 200:
                    raise ZohoAuthError(f"Failed to refresh token: {response.text}")
                
                token_data = response.json()
                self.access_token = token_data["access_token"]
                # Set expiry time (usually 1 hour, subtract 5 minutes for safety)
                self.token_expires_at = datetime.now().timestamp() + token_data.get("expires_in", 3600) - 300
                
                return self.access_token
        except Exception as e:
            logger.error(f"Error refreshing Zoho token: {e}")
            raise ZohoAuthError(f"Token refresh failed: {str(e)}")
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make authenticated request to Zoho Books API"""
        try:
            access_token = await self._get_access_token()
            
            headers = {
                "Authorization": f"Zoho-oauthtoken {access_token}",
                "Content-Type": "application/json"
            }
            
            url = f"{self.base_url}/{endpoint}"
            
            # Add organization ID to params
            if params is None:
                params = {}
            params["organization_id"] = self.organization_id
            
            async with httpx.AsyncClient() as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, params=params, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=headers, params=params, json=data)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers, params=params)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                if response.status_code not in [200, 201]:
                    error_msg = f"Zoho API error: {response.status_code} - {response.text}"
                    logger.error(error_msg)
                    raise ZohoAPIError(error_msg)
                
                return response.json()
        except ZohoAPIError:
            raise
        except Exception as e:
            logger.error(f"Error making Zoho request: {e}")
            raise ZohoAPIError(f"Request failed: {str(e)}")
    
    async def create_customer(self, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a customer in Zoho Books"""
        try:
            zoho_customer = {
                "contact_name": f"{customer_data['first_name']} {customer_data['last_name']}",
                "contact_type": "customer",
                "customer_name": f"{customer_data['first_name']} {customer_data['last_name']}",
                "email": customer_data.get("email"),
                "phone": customer_data.get("phone"),
                "billing_address": self._format_address(customer_data.get("address", {})),
                "shipping_address": self._format_address(customer_data.get("address", {})),
                "notes": f"Patient ID: {customer_data.get('patient_id', '')}"
            }
            
            response = await self._make_request("POST", "contacts", zoho_customer)
            
            if response.get("code") == 0:
                logger.info(f"Customer created in Zoho: {response['contact']['contact_id']}")
                return response["contact"]
            else:
                raise ZohoAPIError(f"Failed to create customer: {response.get('message', 'Unknown error')}")
        except Exception as e:
            logger.error(f"Error creating Zoho customer: {e}")
            raise
    
    async def create_invoice(self, invoice_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create an invoice in Zoho Books"""
        try:
            # Ensure customer exists
            customer_id = await self._ensure_customer_exists(invoice_data["customer_data"])
            
            zoho_invoice = {
                "customer_id": customer_id,
                "date": invoice_data.get("date", date.today().isoformat()),
                "due_date": invoice_data.get("due_date"),
                "invoice_number": invoice_data.get("invoice_number"),
                "reference_number": invoice_data.get("reference_number"),
                "line_items": self._format_line_items(invoice_data["services"]),
                "notes": invoice_data.get("notes", ""),
                "terms": invoice_data.get("terms", "Payment due within 30 days"),
                "custom_fields": [
                    {
                        "customfield_id": "patient_id",
                        "value": invoice_data.get("patient_id", "")
                    },
                    {
                        "customfield_id": "caregiver_id", 
                        "value": invoice_data.get("caregiver_id", "")
                    }
                ]
            }
            
            response = await self._make_request("POST", "invoices", zoho_invoice)
            
            if response.get("code") == 0:
                logger.info(f"Invoice created in Zoho: {response['invoice']['invoice_id']}")
                return response["invoice"]
            else:
                raise ZohoAPIError(f"Failed to create invoice: {response.get('message', 'Unknown error')}")
        except Exception as e:
            logger.error(f"Error creating Zoho invoice: {e}")
            raise
    
    async def send_invoice(self, invoice_id: str, email_data: Optional[Dict[str, Any]] = None) -> bool:
        """Send invoice via email"""
        try:
            endpoint = f"invoices/{invoice_id}/email"
            
            email_payload = {
                "send_from_org_email_id": True,
                "to_mail_ids": email_data.get("to_emails", []) if email_data else [],
                "cc_mail_ids": email_data.get("cc_emails", []) if email_data else [],
                "subject": email_data.get("subject", "Invoice from CareSyncAI") if email_data else "Invoice from CareSyncAI",
                "body": email_data.get("body", "Please find attached invoice.") if email_data else "Please find attached invoice."
            }
            
            response = await self._make_request("POST", endpoint, email_payload)
            
            if response.get("code") == 0:
                logger.info(f"Invoice {invoice_id} sent successfully")
                return True
            else:
                logger.error(f"Failed to send invoice: {response.get('message', 'Unknown error')}")
                return False
        except Exception as e:
            logger.error(f"Error sending invoice: {e}")
            return False
    
    async def get_invoice(self, invoice_id: str) -> Optional[Dict[str, Any]]:
        """Get invoice details"""
        try:
            response = await self._make_request("GET", f"invoices/{invoice_id}")
            
            if response.get("code") == 0:
                return response["invoice"]
            else:
                logger.error(f"Failed to get invoice: {response.get('message', 'Unknown error')}")
                return None
        except Exception as e:
            logger.error(f"Error getting invoice: {e}")
            return None
    
    async def get_invoices(
        self, 
        customer_id: Optional[str] = None,
        status: Optional[str] = None,
        date_start: Optional[str] = None,
        date_end: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get list of invoices with filters"""
        try:
            params = {}
            if customer_id:
                params["customer_id"] = customer_id
            if status:
                params["status"] = status
            if date_start:
                params["date_start"] = date_start
            if date_end:
                params["date_end"] = date_end
            
            response = await self._make_request("GET", "invoices", params=params)
            
            if response.get("code") == 0:
                return response.get("invoices", [])
            else:
                logger.error(f"Failed to get invoices: {response.get('message', 'Unknown error')}")
                return []
        except Exception as e:
            logger.error(f"Error getting invoices: {e}")
            return []
    
    async def update_invoice_status(self, invoice_id: str, status: str) -> bool:
        """Update invoice status (mark as paid, etc.)"""
        try:
            if status.lower() == "paid":
                endpoint = f"invoices/{invoice_id}/status/paid"
                response = await self._make_request("POST", endpoint)
            else:
                # For other status updates, use the general update endpoint
                update_data = {"status": status}
                response = await self._make_request("PUT", f"invoices/{invoice_id}", update_data)
            
            if response.get("code") == 0:
                logger.info(f"Invoice {invoice_id} status updated to {status}")
                return True
            else:
                logger.error(f"Failed to update invoice status: {response.get('message', 'Unknown error')}")
                return False
        except Exception as e:
            logger.error(f"Error updating invoice status: {e}")
            return False
    
    async def _ensure_customer_exists(self, customer_data: Dict[str, Any]) -> str:
        """Ensure customer exists in Zoho Books, create if not"""
        try:
            # Search for existing customer by email
            if customer_data.get("email"):
                params = {"email": customer_data["email"]}
                response = await self._make_request("GET", "contacts", params=params)
                
                if response.get("code") == 0 and response.get("contacts"):
                    return response["contacts"][0]["contact_id"]
            
            # Customer doesn't exist, create new one
            customer = await self.create_customer(customer_data)
            return customer["contact_id"]
        except Exception as e:
            logger.error(f"Error ensuring customer exists: {e}")
            raise
    
    def _format_address(self, address: Dict[str, Any]) -> Dict[str, str]:
        """Format address for Zoho Books"""
        return {
            "address": address.get("street", ""),
            "city": address.get("city", ""),
            "state": address.get("state", ""),
            "zip": address.get("zip", ""),
            "country": "USA"
        }
    
    def _format_line_items(self, services: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format line items for Zoho Books invoice"""
        line_items = []
        
        for service in services:
            line_items.append({
                "name": service.get("service", "Homecare Service"),
                "description": service.get("description", ""),
                "rate": float(service.get("rate", 0)),
                "quantity": float(service.get("hours", 1)),
                "unit": "hours"
            })
        
        return line_items
