import React, { useState } from 'react';
import {
  UserGroupIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CalendarDaysIcon,
  AcademicCapIcon,
  ChartBarIcon,
  BellIcon,
  UserIcon,
  PlusIcon,
  EyeIcon,
  CogIcon,
  PhoneIcon,
  EnvelopeIcon,
  StarIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
} from '@heroicons/react/24/outline';

interface StaffMember {
  id: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  role: 'administrator' | 'nurse' | 'caregiver' | 'medication_aide' | 'activities_coordinator' | 'maintenance';
  department: string;
  hireDate: string;
  status: 'active' | 'inactive' | 'on_leave' | 'terminated';
  contactInfo: {
    phone: string;
    email: string;
    emergencyContact: {
      name: string;
      phone: string;
      relationship: string;
    };
  };
  credentials: {
    licenses: Array<{
      type: string;
      number: string;
      issueDate: string;
      expiryDate: string;
      status: 'active' | 'expired' | 'pending_renewal';
    }>;
    certifications: Array<{
      name: string;
      issuer: string;
      issueDate: string;
      expiryDate: string;
      status: 'active' | 'expired' | 'pending_renewal';
    }>;
    trainings: Array<{
      name: string;
      completedDate: string;
      nextDueDate?: string;
      status: 'completed' | 'overdue' | 'upcoming';
    }>;
  };
  schedule: {
    shiftType: 'day' | 'evening' | 'night' | 'rotating';
    hoursPerWeek: number;
    preferredDays: string[];
    availability: string[];
  };
  performance: {
    overallRating: number; // 1-5
    residentSatisfaction: number; // 1-5
    punctuality: number; // 1-5
    teamwork: number; // 1-5
    lastReviewDate: string;
    goals: string[];
    strengths: string[];
    improvementAreas: string[];
  };
  workload: {
    assignedResidents: string[];
    currentTasks: number;
    avgTasksPerShift: number;
    workloadScore: number; // 1-100
  };
}

interface Shift {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  shiftType: 'day' | 'evening' | 'night' | 'day_extended' | 'night_extended';
  staffMemberId: string;
  staffName: string;
  role: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'missed' | 'called_off';
  residents: string[];
  tasks: Array<{
    id: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
    status: 'pending' | 'in_progress' | 'completed';
    estimatedTime: number; // minutes
  }>;
  notes?: string;
}

const StaffManagement: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'scheduling' | 'credentials' | 'performance'>('overview');

  // Modal states
  const [showAddStaffModal, setShowAddStaffModal] = useState(false);
  const [showViewProfileModal, setShowViewProfileModal] = useState(false);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddShiftModal, setShowAddShiftModal] = useState(false);
  const [showViewCalendarModal, setShowViewCalendarModal] = useState(false);
  const [showAddCredentialModal, setShowAddCredentialModal] = useState(false);
  const [showPerformanceModal, setShowPerformanceModal] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);

  // Calendar view state
  const [calendarView, setCalendarView] = useState<'day' | 'week' | 'month'>('month');
  const [currentCalendarDate, setCurrentCalendarDate] = useState(new Date());
  const [newStaffMember, setNewStaffMember] = useState<Partial<StaffMember>>({
    firstName: '',
    lastName: '',
    role: 'caregiver',
    department: '',
    contactInfo: {
      phone: '',
      email: '',
      emergencyContact: {
        name: '',
        phone: '',
        relationship: ''
      }
    }
  });

  const [newCredential, setNewCredential] = useState({
    type: 'certification', // certification, license, training
    name: '',
    issuingOrganization: '',
    issueDate: '',
    expiryDate: '',
    credentialNumber: '',
    status: 'active'
  });

  const [performanceReview, setPerformanceReview] = useState({
    overallRating: 0,
    residentSatisfaction: 0,
    punctuality: 0,
    teamwork: 0,
    clinicalSkills: 0,
    communication: 0,
    strengths: '',
    improvementAreas: '',
    goals: '',
    reviewDate: new Date().toISOString().split('T')[0],
    reviewerName: '',
    comments: ''
  });

  // Mock data for staff members
  const staffMembers: StaffMember[] = [
    {
      id: 'S001',
      employeeId: 'EMP001',
      firstName: 'Sarah',
      lastName: 'Johnson',
      role: 'nurse',
      department: 'Nursing',
      hireDate: '2023-02-15',
      status: 'active',
      contactInfo: {
        phone: '******-0123',
        email: '<EMAIL>',
        emergencyContact: {
          name: 'Mike Johnson',
          phone: '******-0124',
          relationship: 'Spouse'
        }
      },
      credentials: {
        licenses: [
          {
            type: 'RN License',
            number: 'RN123456',
            issueDate: '2022-01-15',
            expiryDate: '2025-01-15',
            status: 'active'
          }
        ],
        certifications: [
          {
            name: 'CPR Certification',
            issuer: 'American Heart Association',
            issueDate: '2023-06-01',
            expiryDate: '2025-06-01',
            status: 'active'
          },
          {
            name: 'Medication Administration',
            issuer: 'State Board of Nursing',
            issueDate: '2023-03-15',
            expiryDate: '2024-03-15',
            status: 'pending_renewal'
          }
        ],
        trainings: [
          {
            name: 'HIPAA Training',
            completedDate: '2024-01-10',
            nextDueDate: '2025-01-10',
            status: 'completed'
          },
          {
            name: 'Fire Safety Training',
            completedDate: '2023-12-01',
            nextDueDate: '2024-12-01',
            status: 'upcoming'
          }
        ]
      },
      schedule: {
        shiftType: 'day',
        hoursPerWeek: 40,
        preferredDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        availability: ['6:00 AM - 6:00 PM']
      },
      performance: {
        overallRating: 4.5,
        residentSatisfaction: 4.8,
        punctuality: 4.2,
        teamwork: 4.6,
        lastReviewDate: '2024-01-15',
        goals: ['Complete advanced wound care training', 'Mentor new staff'],
        strengths: ['Excellent clinical skills', 'Great with residents', 'Team player'],
        improvementAreas: ['Time management', 'Documentation efficiency']
      },
      workload: {
        assignedResidents: ['R001', 'R002', 'R003', 'R004', 'R005'],
        currentTasks: 12,
        avgTasksPerShift: 15,
        workloadScore: 75
      }
    },
    {
      id: 'S002',
      employeeId: 'EMP002',
      firstName: 'John',
      lastName: 'Smith',
      role: 'caregiver',
      department: 'Direct Care',
      hireDate: '2023-03-01',
      status: 'active',
      contactInfo: {
        phone: '******-0125',
        email: '<EMAIL>',
        emergencyContact: {
          name: 'Mary Smith',
          phone: '******-0126',
          relationship: 'Mother'
        }
      },
      credentials: {
        licenses: [
          {
            type: 'CNA License',
            number: 'CNA789012',
            issueDate: '2023-01-01',
            expiryDate: '2024-12-31',
            status: 'active'
          }
        ],
        certifications: [
          {
            name: 'First Aid/CPR',
            issuer: 'Red Cross',
            issueDate: '2023-05-15',
            expiryDate: '2025-05-15',
            status: 'active'
          }
        ],
        trainings: [
          {
            name: 'Dementia Care Training',
            completedDate: '2023-11-20',
            nextDueDate: '2024-11-20',
            status: 'completed'
          },
          {
            name: 'Infection Control',
            completedDate: '2023-08-15',
            nextDueDate: '2024-08-15',
            status: 'overdue'
          }
        ]
      },
      schedule: {
        shiftType: 'evening',
        hoursPerWeek: 32,
        preferredDays: ['Monday', 'Wednesday', 'Friday', 'Saturday'],
        availability: ['2:00 PM - 10:00 PM']
      },
      performance: {
        overallRating: 4.2,
        residentSatisfaction: 4.5,
        punctuality: 4.0,
        teamwork: 4.3,
        lastReviewDate: '2024-01-10',
        goals: ['Improve documentation skills', 'Complete medication aide training'],
        strengths: ['Compassionate care', 'Reliable', 'Good with families'],
        improvementAreas: ['Computer skills', 'Assertiveness in emergencies']
      },
      workload: {
        assignedResidents: ['R006', 'R007', 'R008', 'R009'],
        currentTasks: 8,
        avgTasksPerShift: 10,
        workloadScore: 60
      }
    }
  ];

  // Mock shift data
  const todaysShifts: Shift[] = [
    {
      id: 'SH001',
      date: '2024-01-20',
      startTime: '06:00',
      endTime: '14:00',
      shiftType: 'day',
      staffMemberId: 'S001',
      staffName: 'Sarah Johnson',
      role: 'nurse',
      status: 'in_progress',
      residents: ['R001', 'R002', 'R003'],
      tasks: [
        {
          id: 'T001',
          description: 'Morning medication round',
          priority: 'high',
          status: 'completed',
          estimatedTime: 60
        },
        {
          id: 'T002',
          description: 'Vital signs check',
          priority: 'medium',
          status: 'in_progress',
          estimatedTime: 45
        }
      ]
    },
    {
      id: 'SH002',
      date: '2024-01-20',
      startTime: '14:00',
      endTime: '22:00',
      shiftType: 'evening',
      staffMemberId: 'S002',
      staffName: 'John Smith',
      role: 'caregiver',
      status: 'scheduled',
      residents: ['R004', 'R005', 'R006'],
      tasks: [
        {
          id: 'T003',
          description: 'Dinner assistance',
          priority: 'high',
          status: 'pending',
          estimatedTime: 90
        },
        {
          id: 'T004',
          description: 'Evening activities',
          priority: 'medium',
          status: 'pending',
          estimatedTime: 60
        }
      ]
    },
    {
      id: 'SH003',
      date: '2024-01-20',
      startTime: '07:00',
      endTime: '19:30',
      shiftType: 'day_extended',
      staffMemberId: 'S003',
      staffName: 'Maria Garcia',
      role: 'nurse',
      status: 'scheduled',
      residents: ['R001', 'R002', 'R003', 'R007', 'R008'],
      tasks: [
        {
          id: 'T005',
          description: 'Extended day medication rounds',
          priority: 'high',
          status: 'pending',
          estimatedTime: 120
        },
        {
          id: 'T006',
          description: 'Patient assessments',
          priority: 'high',
          status: 'pending',
          estimatedTime: 180
        }
      ]
    },
    {
      id: 'SH004',
      date: '2024-01-20',
      startTime: '19:00',
      endTime: '07:30',
      shiftType: 'night_extended',
      staffMemberId: 'S004',
      staffName: 'David Wilson',
      role: 'nurse',
      status: 'scheduled',
      residents: ['R001', 'R002', 'R003', 'R004', 'R005', 'R006'],
      tasks: [
        {
          id: 'T007',
          description: 'Night medication administration',
          priority: 'high',
          status: 'pending',
          estimatedTime: 90
        },
        {
          id: 'T008',
          description: 'Overnight monitoring',
          priority: 'medium',
          status: 'pending',
          estimatedTime: 480
        }
      ]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending_renewal':
      case 'upcoming':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'administrator':
        return 'bg-purple-100 text-purple-800';
      case 'nurse':
        return 'bg-blue-100 text-blue-800';
      case 'caregiver':
        return 'bg-green-100 text-green-800';
      case 'medication_aide':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getWorkloadColor = (score: number) => {
    if (score >= 80) return 'bg-red-100 text-red-800';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const getShiftTypeDisplay = (shiftType: string) => {
    switch (shiftType) {
      case 'day':
        return 'Day Shift';
      case 'evening':
        return 'Evening Shift';
      case 'night':
        return 'Night Shift';
      case 'day_extended':
        return 'Extended Day (12.5h)';
      case 'night_extended':
        return 'Extended Night (12.5h)';
      default:
        return 'Unknown Shift';
    }
  };

  const getShiftTypeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'day':
        return 'bg-yellow-100 text-yellow-800';
      case 'evening':
        return 'bg-orange-100 text-orange-800';
      case 'night':
        return 'bg-blue-100 text-blue-800';
      case 'day_extended':
        return 'bg-green-100 text-green-800';
      case 'night_extended':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const tabs = [
    { id: 'overview', name: 'Staff Overview', icon: UserGroupIcon },
    { id: 'scheduling', name: 'Scheduling', icon: CalendarDaysIcon },
    { id: 'credentials', name: 'Credentials', icon: AcademicCapIcon },
    { id: 'performance', name: 'Performance', icon: ChartBarIcon },
  ];

  // Handler functions
  const handleAddStaffMember = () => {
    setNewStaffMember({
      firstName: '',
      lastName: '',
      role: 'caregiver',
      department: '',
      contactInfo: {
        phone: '',
        email: '',
        emergencyContact: {
          name: '',
          phone: '',
          relationship: ''
        }
      }
    });
    setShowAddStaffModal(true);
  };

  const handleViewProfile = (staff: StaffMember) => {
    setSelectedStaff(staff);
    setShowViewProfileModal(true);
  };

  const handleSchedule = (staff: StaffMember) => {
    setSelectedStaff(staff);
    setShowScheduleModal(true);
  };

  const handleEdit = (staff: StaffMember) => {
    setSelectedStaff(staff);
    setShowEditModal(true);
  };

  const handleAddShift = () => {
    setShowAddShiftModal(true);
  };

  const handleViewCalendar = () => {
    setShowViewCalendarModal(true);
  };

  // Calendar navigation functions
  const navigateCalendarDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentCalendarDate);
    if (calendarView === 'day') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else if (calendarView === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    } else {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    }
    setCurrentCalendarDate(newDate);
  };

  const goToToday = () => {
    setCurrentCalendarDate(new Date());
  };

  const formatCalendarDate = (date: Date) => {
    if (calendarView === 'day') {
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } else if (calendarView === 'week') {
      const startOfWeek = new Date(date);
      startOfWeek.setDate(date.getDate() - date.getDay());
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      return `${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
    } else {
      return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
    }
  };

  const handleAddCredential = (staff: StaffMember) => {
    setSelectedStaff(staff);
    setNewCredential({
      type: 'certification',
      name: '',
      issuingOrganization: '',
      issueDate: '',
      expiryDate: '',
      credentialNumber: '',
      status: 'active'
    });
    setShowAddCredentialModal(true);
  };

  const handlePerformanceReview = (staff: StaffMember) => {
    setSelectedStaff(staff);
    setPerformanceReview({
      overallRating: staff.performance?.overallRating || 0,
      residentSatisfaction: staff.performance?.residentSatisfaction || 0,
      punctuality: staff.performance?.punctuality || 0,
      teamwork: staff.performance?.teamwork || 0,
      clinicalSkills: 0,
      communication: 0,
      strengths: staff.performance?.strengths?.join(', ') || '',
      improvementAreas: staff.performance?.improvementAreas?.join(', ') || '',
      goals: staff.performance?.goals?.join(', ') || '',
      reviewDate: new Date().toISOString().split('T')[0],
      reviewerName: '',
      comments: ''
    });
    setShowPerformanceModal(true);
  };

  const saveStaffMember = () => {
    if (!newStaffMember.firstName || !newStaffMember.lastName) {
      alert('Please fill in all required fields');
      return;
    }

    alert('Staff member added successfully!');
    setShowAddStaffModal(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Staff Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          Comprehensive workforce management for residential care staff
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            👥 Workforce Optimization
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            📅 Smart Scheduling
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            🎓 Credential Tracking
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
            📊 Performance Analytics
          </span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Active Staff</p>
              <p className="text-2xl font-bold">24</p>
            </div>
            <UserGroupIcon className="h-8 w-8 text-blue-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">On Duty Now</p>
              <p className="text-2xl font-bold">8</p>
            </div>
            <ClockIcon className="h-8 w-8 text-green-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-100 text-sm">Credentials Expiring</p>
              <p className="text-2xl font-bold">3</p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-yellow-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Avg Performance</p>
              <p className="text-2xl font-bold">4.3</p>
            </div>
            <StarIcon className="h-8 w-8 text-purple-200" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {selectedTab === 'overview' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Staff Directory</h3>
                <button
                  onClick={handleAddStaffMember}
                  className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 inline mr-1" />
                  Add Staff Member
                </button>
              </div>

              {staffMembers.map((staff) => (
                <div key={staff.id} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <UserIcon className="h-6 w-6 text-gray-500" />
                        </div>
                        <div>
                          <h4 className="text-lg font-medium text-gray-900">
                            {staff.firstName} {staff.lastName}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {staff.role.replace('_', ' ').toUpperCase()} • Employee ID: {staff.employeeId}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleColor(staff.role)}`}>
                              {staff.role.replace('_', ' ').toUpperCase()}
                            </span>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(staff.status)}`}>
                              {staff.status.replace('_', ' ').toUpperCase()}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700">Contact</p>
                          <div className="mt-1 space-y-1">
                            <div className="flex items-center text-sm text-gray-600">
                              <PhoneIcon className="h-4 w-4 mr-2" />
                              {staff.contactInfo.phone}
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <EnvelopeIcon className="h-4 w-4 mr-2" />
                              {staff.contactInfo.email}
                            </div>
                          </div>
                        </div>

                        <div>
                          <p className="text-sm font-medium text-gray-700">Schedule</p>
                          <p className="text-sm text-gray-600">
                            {staff.schedule.shiftType.charAt(0).toUpperCase() + staff.schedule.shiftType.slice(1)} shift
                          </p>
                          <p className="text-sm text-gray-600">{staff.schedule.hoursPerWeek} hours/week</p>
                        </div>

                        <div>
                          <p className="text-sm font-medium text-gray-700">Performance</p>
                          <div className="flex items-center space-x-1">
                            {renderStars(staff.performance.overallRating)}
                            <span className="text-sm text-gray-600 ml-2">
                              {staff.performance.overallRating}/5
                            </span>
                          </div>
                          <p className="text-sm text-gray-600">
                            Workload: <span className={`px-1 py-0.5 text-xs rounded ${getWorkloadColor(staff.workload.workloadScore)}`}>
                              {staff.workload.workloadScore}%
                            </span>
                          </p>
                        </div>
                      </div>

                      {/* Credential Alerts */}
                      {staff.credentials.certifications.some(cert => cert.status === 'pending_renewal') && (
                        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                          <div className="flex items-start">
                            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
                            <div className="ml-3">
                              <h5 className="text-sm font-medium text-yellow-800">Credential Renewal Required</h5>
                              <p className="text-sm text-yellow-700">
                                {staff.credentials.certifications
                                  .filter(cert => cert.status === 'pending_renewal')
                                  .map(cert => cert.name)
                                  .join(', ')} expiring soon
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="ml-6 flex flex-col space-y-2">
                      <button
                        onClick={() => handleViewProfile(staff)}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                      >
                        <EyeIcon className="h-4 w-4 inline mr-1" />
                        View Profile
                      </button>
                      <button
                        onClick={() => handleSchedule(staff)}
                        className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
                      >
                        <CalendarDaysIcon className="h-4 w-4 inline mr-1" />
                        Schedule
                      </button>
                      <button
                        onClick={() => handleEdit(staff)}
                        className="px-3 py-1 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700"
                      >
                        <CogIcon className="h-4 w-4 inline mr-1" />
                        Edit
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'scheduling' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Today's Schedule</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={handleAddShift}
                    className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
                  >
                    <PlusIcon className="h-4 w-4 inline mr-1" />
                    Add Shift
                  </button>
                  <button
                    onClick={handleViewCalendar}
                    className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                  >
                    <CalendarDaysIcon className="h-4 w-4 inline mr-1" />
                    View Calendar
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {['day', 'evening', 'night', 'day_extended', 'night_extended'].map((shiftType) => (
                  <div key={shiftType} className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-4">
                      <span className={`px-2 py-1 text-sm font-medium rounded-full ${getShiftTypeColor(shiftType)} mr-2`}>
                        {getShiftTypeDisplay(shiftType)}
                      </span>
                      <span className="text-sm text-gray-500">
                        {shiftType === 'day' ? '(6 AM - 2 PM)' :
                         shiftType === 'evening' ? '(2 PM - 10 PM)' :
                         shiftType === 'night' ? '(10 PM - 6 AM)' :
                         shiftType === 'day_extended' ? '(7 AM - 7:30 PM)' :
                         shiftType === 'night_extended' ? '(7 PM - 7:30 AM)' : ''}
                      </span>
                    </h4>

                    <div className="space-y-3">
                      {todaysShifts
                        .filter(shift => shift.shiftType === shiftType)
                        .map((shift) => (
                          <div key={shift.id} className="border border-gray-100 rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div>
                                <p className="font-medium text-gray-900">{shift.staffName}</p>
                                <p className="text-sm text-gray-600 capitalize">{shift.role}</p>
                              </div>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(shift.status)}`}>
                                {shift.status.replace('_', ' ').toUpperCase()}
                              </span>
                            </div>

                            <div className="text-sm text-gray-600">
                              <p>Residents: {shift.residents.length}</p>
                              <p>Tasks: {shift.tasks.filter(t => t.status === 'completed').length}/{shift.tasks.length} completed</p>
                            </div>

                            <div className="mt-2 flex space-x-2">
                              <button className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded hover:bg-blue-200">
                                View Tasks
                              </button>
                              <button className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded hover:bg-gray-200">
                                Contact
                              </button>
                            </div>
                          </div>
                        ))}

                      {todaysShifts.filter(shift => shift.shiftType === shiftType).length === 0 && (
                        <div className="text-center py-4 text-gray-500">
                          <ClockIcon className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                          <p className="text-sm">No shifts scheduled</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedTab === 'credentials' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Credential Management</h3>
                <button
                  onClick={() => setShowAddCredentialModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 inline mr-1" />
                  Add Credential
                </button>
              </div>

              {/* Staff Credentials List */}
              <div className="space-y-4">
                {staffMembers.map((staff) => (
                  <div key={staff.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <UserIcon className="h-6 w-6 text-gray-500" />
                        </div>
                        <div>
                          <h4 className="text-lg font-medium text-gray-900">
                            {staff.firstName} {staff.lastName}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {staff.role.replace('_', ' ').toUpperCase()} • Employee ID: {staff.employeeId}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleAddCredential(staff)}
                        className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
                      >
                        <PlusIcon className="h-4 w-4 inline mr-1" />
                        Add Credential
                      </button>
                    </div>

                    {/* Credentials Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {/* Certifications */}
                      <div className="bg-blue-50 rounded-lg p-4">
                        <h5 className="font-medium text-blue-900 mb-3">Certifications</h5>
                        <div className="space-y-2">
                          {staff.credentials.certifications.map((cert, index) => (
                            <div key={index} className="bg-white rounded p-2">
                              <p className="font-medium text-sm">{cert.name}</p>
                              <p className="text-xs text-gray-600">{cert.issuer}</p>
                              <div className="flex justify-between items-center mt-1">
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(cert.status)}`}>
                                  {cert.status.replace('_', ' ').toUpperCase()}
                                </span>
                                <span className="text-xs text-gray-500">
                                  Exp: {cert.expiryDate}
                                </span>
                              </div>
                            </div>
                          ))}
                          {staff.credentials.certifications.length === 0 && (
                            <p className="text-sm text-gray-500 italic">No certifications added</p>
                          )}
                        </div>
                      </div>

                      {/* Licenses */}
                      <div className="bg-green-50 rounded-lg p-4">
                        <h5 className="font-medium text-green-900 mb-3">Licenses</h5>
                        <div className="space-y-2">
                          {staff.credentials.licenses.map((license, index) => (
                            <div key={index} className="bg-white rounded p-2">
                              <p className="font-medium text-sm">{license.type}</p>
                              <p className="text-xs text-gray-600">License #{license.number}</p>
                              <div className="flex justify-between items-center mt-1">
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(license.status)}`}>
                                  {license.status.replace('_', ' ').toUpperCase()}
                                </span>
                                <span className="text-xs text-gray-500">
                                  Exp: {license.expiryDate}
                                </span>
                              </div>
                            </div>
                          ))}
                          {staff.credentials.licenses.length === 0 && (
                            <p className="text-sm text-gray-500 italic">No licenses added</p>
                          )}
                        </div>
                      </div>

                      {/* Training */}
                      <div className="bg-purple-50 rounded-lg p-4">
                        <h5 className="font-medium text-purple-900 mb-3">Training</h5>
                        <div className="space-y-2">
                          {staff.credentials.trainings.map((training, index) => (
                            <div key={index} className="bg-white rounded p-2">
                              <p className="font-medium text-sm">{training.name}</p>
                              <p className="text-xs text-gray-600">Training Program</p>
                              <div className="flex justify-between items-center mt-1">
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(training.status)}`}>
                                  {training.status.replace('_', ' ').toUpperCase()}
                                </span>
                                <span className="text-xs text-gray-500">
                                  {training.completedDate}
                                </span>
                              </div>
                            </div>
                          ))}
                          {staff.credentials.trainings.length === 0 && (
                            <p className="text-sm text-gray-500 italic">No training records</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Summary Cards */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
                <div className="border border-red-200 rounded-lg p-4 bg-red-50">
                  <h4 className="font-medium text-red-900 mb-3">⚠️ Expiring Soon</h4>
                  <div className="space-y-2">
                    {staffMembers.flatMap(staff =>
                      staff.credentials.certifications
                        .filter(cert => cert.status === 'pending_renewal')
                        .map(cert => (
                          <div key={`${staff.id}-${cert.name}`} className="flex justify-between items-center p-2 bg-white rounded">
                            <div>
                              <p className="font-medium text-sm text-gray-900">{staff.firstName} {staff.lastName}</p>
                              <p className="text-xs text-gray-600">{cert.name}</p>
                            </div>
                            <div className="text-right">
                              <p className="text-xs text-red-600">Expires: {cert.expiryDate}</p>
                              <button className="text-xs text-blue-600 hover:text-blue-800">Renew</button>
                            </div>
                          </div>
                        ))
                    )}
                    {staffMembers.flatMap(staff => staff.credentials.certifications.filter(cert => cert.status === 'pending_renewal')).length === 0 && (
                      <p className="text-sm text-gray-500 italic">No credentials expiring soon</p>
                    )}
                  </div>
                </div>

                <div className="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                  <h4 className="font-medium text-yellow-900 mb-3">📚 Training Due</h4>
                  <div className="space-y-2">
                    {staffMembers.flatMap(staff =>
                      staff.credentials.trainings
                        .filter(training => training.status === 'overdue')
                        .map(training => (
                          <div key={`${staff.id}-${training.name}`} className="flex justify-between items-center p-2 bg-white rounded">
                            <div>
                              <p className="font-medium text-sm text-gray-900">{staff.firstName} {staff.lastName}</p>
                              <p className="text-xs text-gray-600">{training.name}</p>
                            </div>
                            <div className="text-right">
                              <p className="text-xs text-yellow-600">Overdue</p>
                              <button className="text-xs text-blue-600 hover:text-blue-800">Schedule</button>
                            </div>
                          </div>
                        ))
                    )}
                    {staffMembers.flatMap(staff => staff.credentials.trainings.filter(training => training.status === 'overdue')).length === 0 && (
                      <p className="text-sm text-gray-500 italic">No overdue training</p>
                    )}
                  </div>
                </div>

                <div className="border border-green-200 rounded-lg p-4 bg-green-50">
                  <h4 className="font-medium text-green-900 mb-3">✅ Compliance Status</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Overall Compliance</span>
                        <span>92%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Active Certifications</span>
                        <span>85%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Training Current</span>
                        <span>78%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div className="bg-purple-600 h-2 rounded-full" style={{ width: '78%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'performance' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Performance Management</h3>
                <button
                  onClick={() => setShowPerformanceModal(true)}
                  className="px-4 py-2 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700"
                >
                  <StarIcon className="h-4 w-4 inline mr-1" />
                  Add Performance Review
                </button>
              </div>

              {/* Individual Staff Performance Cards */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {staffMembers.map((staff) => (
                  <div key={staff.id} className="border border-gray-200 rounded-lg p-6 bg-white shadow-sm">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <UserIcon className="h-6 w-6 text-gray-500" />
                        </div>
                        <div>
                          <h4 className="text-lg font-medium text-gray-900">
                            {staff.firstName} {staff.lastName}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {staff.role.replace('_', ' ').toUpperCase()} • {staff.department}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => handlePerformanceReview(staff)}
                        className="px-3 py-1 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700"
                      >
                        <StarIcon className="h-4 w-4 inline mr-1" />
                        Review
                      </button>
                    </div>

                    {/* Overall Rating */}
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">Overall Rating</span>
                        <span className="text-lg font-bold text-gray-900">{staff.performance.overallRating}/5</span>
                      </div>
                      <div className="flex items-center space-x-1 mb-2">
                        {renderStars(staff.performance.overallRating)}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full"
                          style={{ width: `${(staff.performance.overallRating / 5) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Performance Metrics */}
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm">
                          <span>Resident Satisfaction</span>
                          <span>{staff.performance.residentSatisfaction}/5</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                          <div
                            className="bg-green-600 h-1.5 rounded-full"
                            style={{ width: `${(staff.performance.residentSatisfaction / 5) * 100}%` }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm">
                          <span>Punctuality</span>
                          <span>{staff.performance.punctuality}/5</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                          <div
                            className="bg-blue-600 h-1.5 rounded-full"
                            style={{ width: `${(staff.performance.punctuality / 5) * 100}%` }}
                          ></div>
                        </div>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm">
                          <span>Teamwork</span>
                          <span>{staff.performance.teamwork}/5</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                          <div
                            className="bg-purple-600 h-1.5 rounded-full"
                            style={{ width: `${(staff.performance.teamwork / 5) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    {/* Recent Performance Notes */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Recent Highlights</h5>
                      <div className="space-y-1">
                        {staff.performance.strengths.slice(0, 2).map((strength, index) => (
                          <div key={index} className="flex items-center text-sm text-green-700">
                            <CheckCircleIcon className="h-4 w-4 mr-2" />
                            {strength}
                          </div>
                        ))}
                      </div>
                      {staff.performance.improvementAreas.length > 0 && (
                        <div className="mt-2">
                          <div className="flex items-center text-sm text-amber-700">
                            <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                            Focus: {staff.performance.improvementAreas[0]}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Workload Indicator */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">Current Workload</span>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getWorkloadColor(staff.workload.workloadScore)}`}>
                          {staff.workload.workloadScore}% Capacity
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {staff.workload.assignedResidents.length} residents • {staff.workload.currentTasks} active tasks
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Performance Summary */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-4">🏆 Top Performers</h4>
                  <div className="space-y-3">
                    {staffMembers
                      .sort((a, b) => b.performance.overallRating - a.performance.overallRating)
                      .slice(0, 3)
                      .map((staff, index) => (
                        <div key={staff.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                          <div className="flex items-center space-x-3">
                            <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                              index === 0 ? 'bg-yellow-400 text-yellow-900' :
                              index === 1 ? 'bg-gray-400 text-gray-900' :
                              'bg-orange-400 text-orange-900'
                            }`}>
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{staff.firstName} {staff.lastName}</p>
                              <p className="text-sm text-gray-600 capitalize">{staff.role.replace('_', ' ')}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-1">
                            {renderStars(staff.performance.overallRating)}
                            <span className="text-sm text-gray-600 ml-2">
                              {staff.performance.overallRating}
                            </span>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-4">📊 Team Metrics</h4>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Average Performance</span>
                        <span>4.3/5</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '86%' }}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Resident Satisfaction</span>
                        <span>4.6/5</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Team Collaboration</span>
                        <span>4.5/5</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div className="bg-purple-600 h-2 rounded-full" style={{ width: '90%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-4">🎯 Goals & Development</h4>
                  <div className="space-y-3">
                    <div className="bg-blue-50 rounded p-3">
                      <p className="text-sm font-medium text-blue-900">Active Goals</p>
                      <p className="text-xs text-blue-700 mt-1">
                        {staffMembers.reduce((total, staff) => total + staff.performance.goals.length, 0)} goals in progress
                      </p>
                    </div>
                    <div className="bg-green-50 rounded p-3">
                      <p className="text-sm font-medium text-green-900">Training Completed</p>
                      <p className="text-xs text-green-700 mt-1">
                        85% completion rate this quarter
                      </p>
                    </div>
                    <div className="bg-purple-50 rounded p-3">
                      <p className="text-sm font-medium text-purple-900">Reviews Due</p>
                      <p className="text-xs text-purple-700 mt-1">
                        3 annual reviews scheduled
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add Staff Member Modal */}
      {showAddStaffModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddStaffModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <PlusIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Add New Staff Member
                      </h3>
                      <p className="text-sm text-gray-500">
                        Enter the staff member's information
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAddStaffModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">First Name *</label>
                      <input
                        type="text"
                        value={newStaffMember.firstName || ''}
                        onChange={(e) => setNewStaffMember(prev => ({ ...prev, firstName: e.target.value }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Last Name *</label>
                      <input
                        type="text"
                        value={newStaffMember.lastName || ''}
                        onChange={(e) => setNewStaffMember(prev => ({ ...prev, lastName: e.target.value }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="Doe"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Role</label>
                      <select
                        value={newStaffMember.role || 'caregiver'}
                        onChange={(e) => setNewStaffMember(prev => ({ ...prev, role: e.target.value as any }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      >
                        <option value="caregiver">Caregiver</option>
                        <option value="nurse">Nurse</option>
                        <option value="administrator">Administrator</option>
                        <option value="maintenance">Maintenance</option>
                        <option value="kitchen">Kitchen Staff</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Department</label>
                      <input
                        type="text"
                        value={newStaffMember.department || ''}
                        onChange={(e) => setNewStaffMember(prev => ({ ...prev, department: e.target.value }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="Nursing"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Phone</label>
                      <input
                        type="tel"
                        value={newStaffMember.contactInfo?.phone || ''}
                        onChange={(e) => setNewStaffMember(prev => ({
                          ...prev,
                          contactInfo: { ...prev.contactInfo!, phone: e.target.value }
                        }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="******-0123"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email</label>
                      <input
                        type="email"
                        value={newStaffMember.contactInfo?.email || ''}
                        onChange={(e) => setNewStaffMember(prev => ({
                          ...prev,
                          contactInfo: { ...prev.contactInfo!, email: e.target.value }
                        }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="border-t border-gray-200 pt-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Emergency Contact</h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Contact Name</label>
                        <input
                          type="text"
                          value={newStaffMember.contactInfo?.emergencyContact?.name || ''}
                          onChange={(e) => setNewStaffMember(prev => ({
                            ...prev,
                            contactInfo: {
                              ...prev.contactInfo!,
                              emergencyContact: { ...prev.contactInfo!.emergencyContact!, name: e.target.value }
                            }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Jane Doe"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Phone</label>
                          <input
                            type="tel"
                            value={newStaffMember.contactInfo?.emergencyContact?.phone || ''}
                            onChange={(e) => setNewStaffMember(prev => ({
                              ...prev,
                              contactInfo: {
                                ...prev.contactInfo!,
                                emergencyContact: { ...prev.contactInfo!.emergencyContact!, phone: e.target.value }
                              }
                            }))}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                            placeholder="******-0124"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Relationship</label>
                          <input
                            type="text"
                            value={newStaffMember.contactInfo?.emergencyContact?.relationship || ''}
                            onChange={(e) => setNewStaffMember(prev => ({
                              ...prev,
                              contactInfo: {
                                ...prev.contactInfo!,
                                emergencyContact: { ...prev.contactInfo!.emergencyContact!, relationship: e.target.value }
                              }
                            }))}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                            placeholder="Spouse"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={saveStaffMember}
                  disabled={!newStaffMember.firstName || !newStaffMember.lastName}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Add Staff Member
                </button>
                <button
                  onClick={() => setShowAddStaffModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Profile Modal */}
      {showViewProfileModal && selectedStaff && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowViewProfileModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <EyeIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Staff Profile
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedStaff.firstName} {selectedStaff.lastName}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowViewProfileModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Basic Information */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Basic Information</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Full Name:</p>
                        <p className="text-gray-600">{selectedStaff.firstName} {selectedStaff.lastName}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Employee ID:</p>
                        <p className="text-gray-600">{selectedStaff.id}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Role:</p>
                        <p className="text-gray-600">{selectedStaff.role.charAt(0).toUpperCase() + selectedStaff.role.slice(1)}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Department:</p>
                        <p className="text-gray-600">{selectedStaff.department}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Status:</p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          selectedStaff.status === 'active' ? 'bg-green-100 text-green-800' :
                          selectedStaff.status === 'on_leave' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {selectedStaff.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Hire Date:</p>
                        <p className="text-gray-600">{selectedStaff.hireDate}</p>
                      </div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-900 mb-3">Contact Information</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-blue-700">Phone:</p>
                        <p className="text-blue-600">{selectedStaff.contactInfo.phone}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-blue-700">Email:</p>
                        <p className="text-blue-600">{selectedStaff.contactInfo.email}</p>
                      </div>
                      <div className="col-span-2">
                        <p className="text-sm font-medium text-blue-700">Emergency Contact:</p>
                        <p className="text-blue-600">
                          {selectedStaff.contactInfo.emergencyContact.name} ({selectedStaff.contactInfo.emergencyContact.relationship}) - {selectedStaff.contactInfo.emergencyContact.phone}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Performance & Certifications */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-green-50 rounded-lg p-4">
                      <h4 className="text-md font-medium text-green-900 mb-3">Performance</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-green-700">Overall Rating:</span>
                          <div className="flex items-center">
                            <div className="w-20 bg-green-200 rounded-full h-2">
                              <div
                                className="h-2 rounded-full bg-green-600"
                                style={{ width: `${(selectedStaff.performance.overallRating / 5) * 100}%` }}
                              ></div>
                            </div>
                            <span className="ml-2 text-sm text-green-600">{selectedStaff.performance.overallRating}/5</span>
                          </div>
                        </div>
                        <div>
                          <span className="text-sm text-green-700">Completed Trainings:</span>
                          <span className="ml-2 text-green-600">{selectedStaff.credentials.trainings.filter(t => t.status === 'completed').length}</span>
                        </div>
                        <div>
                          <span className="text-sm text-green-700">Last Review:</span>
                          <span className="ml-2 text-green-600">{selectedStaff.performance.lastReviewDate}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-purple-50 rounded-lg p-4">
                      <h4 className="text-md font-medium text-purple-900 mb-3">Certifications</h4>
                      <ul className="space-y-1">
                        {selectedStaff.credentials.certifications.map((cert, index) => (
                          <li key={index} className="text-sm text-purple-700 flex items-center">
                            <CheckCircleIcon className="h-4 w-4 mr-2" />
                            {cert.name} ({cert.status})
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => setShowViewProfileModal(false)}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Schedule Modal */}
      {showScheduleModal && selectedStaff && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowScheduleModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                      <CalendarDaysIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Schedule Management
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedStaff.firstName} {selectedStaff.lastName}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowScheduleModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-green-900 mb-3">Current Week Schedule</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-green-700">Monday:</span>
                        <span className="text-green-600">6:00 AM - 2:00 PM (Day Shift)</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-green-700">Tuesday:</span>
                        <span className="text-green-600">6:00 AM - 2:00 PM (Day Shift)</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-green-700">Wednesday:</span>
                        <span className="text-gray-500">Off</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-green-700">Thursday:</span>
                        <span className="text-green-600">2:00 PM - 10:00 PM (Evening Shift)</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-green-700">Friday:</span>
                        <span className="text-green-600">2:00 PM - 10:00 PM (Evening Shift)</span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-green-700">Weekend:</span>
                        <span className="text-gray-500">Off</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Schedule Actions
                    </label>
                    <div className="space-y-2">
                      <button className="w-full text-left px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                        📅 View Full Calendar
                      </button>
                      <button className="w-full text-left px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                        ➕ Add New Shift
                      </button>
                      <button className="w-full text-left px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                        🔄 Request Shift Change
                      </button>
                      <button className="w-full text-left px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                        📊 View Time Reports
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => setShowScheduleModal(false)}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && selectedStaff && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowEditModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 sm:mx-0 sm:h-10 sm:w-10">
                      <CogIcon className="h-6 w-6 text-gray-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Edit Staff Member
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedStaff.firstName} {selectedStaff.lastName}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowEditModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <select
                      defaultValue={selectedStaff.status}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm"
                    >
                      <option value="active">Active</option>
                      <option value="on_leave">On Leave</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Role</label>
                    <select
                      defaultValue={selectedStaff.role}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm"
                    >
                      <option value="caregiver">Caregiver</option>
                      <option value="nurse">Nurse</option>
                      <option value="administrator">Administrator</option>
                      <option value="maintenance">Maintenance</option>
                      <option value="kitchen">Kitchen Staff</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Department</label>
                    <input
                      type="text"
                      defaultValue={selectedStaff.department}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                    <input
                      type="tel"
                      defaultValue={selectedStaff.contactInfo.phone}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <input
                      type="email"
                      defaultValue={selectedStaff.contactInfo.email}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Staff member updated successfully!');
                    setShowEditModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gray-600 text-base font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Save Changes
                </button>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Shift Modal */}
      {showAddShiftModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddShiftModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                      <PlusIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Add New Shift
                      </h3>
                      <p className="text-sm text-gray-500">
                        Schedule a new shift for staff
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAddShiftModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Staff Member</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm">
                      <option>Select staff member...</option>
                      {staffMembers.map((staff) => (
                        <option key={staff.id} value={staff.id}>
                          {staff.firstName} {staff.lastName} - {staff.role}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Date</label>
                    <input
                      type="date"
                      defaultValue={new Date().toISOString().split('T')[0]}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Shift Type</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm">
                      <option value="day">Day Shift (6 AM - 2 PM)</option>
                      <option value="evening">Evening Shift (2 PM - 10 PM)</option>
                      <option value="night">Night Shift (10 PM - 6 AM)</option>
                      <option value="day_extended">Extended Day Shift (7 AM - 7:30 PM)</option>
                      <option value="night_extended">Extended Night Shift (7 PM - 7:30 AM)</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Start Time</label>
                      <input
                        type="time"
                        defaultValue="06:00"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">End Time</label>
                      <input
                        type="time"
                        defaultValue="14:00"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notes</label>
                    <textarea
                      rows={3}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                      placeholder="Any special instructions or notes for this shift..."
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Shift added successfully!');
                    setShowAddShiftModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Add Shift
                </button>
                <button
                  onClick={() => setShowAddShiftModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Calendar Modal */}
      {showViewCalendarModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowViewCalendarModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <CalendarDaysIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Staff Calendar View
                      </h3>
                      <p className="text-sm text-gray-500">
                        Complete schedule overview for all staff
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowViewCalendarModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Calendar Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => navigateCalendarDate('prev')}
                        className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors duration-200"
                      >
                        <ArrowLeftIcon className="h-5 w-5" />
                      </button>
                      <h4 className="text-lg font-medium text-gray-900 min-w-[200px] text-center">
                        {formatCalendarDate(currentCalendarDate)}
                      </h4>
                      <button
                        onClick={() => navigateCalendarDate('next')}
                        className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors duration-200"
                      >
                        <ArrowRightIcon className="h-5 w-5" />
                      </button>
                    </div>

                    <div className="flex items-center space-x-2">
                      <select
                        value={calendarView}
                        onChange={(e) => setCalendarView(e.target.value as 'day' | 'week' | 'month')}
                        className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="day">Day View</option>
                        <option value="week">Week View</option>
                        <option value="month">Month View</option>
                      </select>
                      <button
                        onClick={goToToday}
                        className="px-3 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors duration-200"
                      >
                        Today
                      </button>
                    </div>
                  </div>

                  {/* Calendar Grid */}
                  <div className="bg-white border border-gray-200 rounded-lg">
                    {calendarView === 'month' && (
                      <>
                        <div className="grid grid-cols-7 gap-px bg-gray-200">
                          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                            <div key={day} className="bg-gray-50 p-3 text-center text-sm font-medium text-gray-700">
                              {day}
                            </div>
                          ))}
                        </div>
                        <div className="grid grid-cols-7 gap-px bg-gray-200">
                          {Array.from({ length: 35 }, (_, i) => {
                            const dayNumber = ((i % 31) + 1);
                            // Current date is July 25, 2025
                            const today = new Date(2025, 6, 25); // Month is 0-indexed, so 6 = July
                            const isToday = dayNumber === today.getDate() &&
                                          currentCalendarDate.getMonth() === today.getMonth() &&
                                          currentCalendarDate.getFullYear() === today.getFullYear();
                            return (
                              <div key={i} className={`bg-white p-2 h-28 text-sm border-r border-b border-gray-100 ${isToday ? 'bg-blue-50' : ''}`}>
                                <div className={`font-medium ${isToday ? 'text-blue-600' : 'text-gray-900'}`}>
                                  {dayNumber}
                                </div>
                                {i % 7 !== 0 && i % 7 !== 6 && (
                                  <div className="mt-1 space-y-1">
                                    <div className="text-xs bg-blue-100 text-blue-800 px-1 rounded">Day: 3</div>
                                    <div className="text-xs bg-green-100 text-green-800 px-1 rounded">Eve: 2</div>
                                    <div className="text-xs bg-purple-100 text-purple-800 px-1 rounded">Night: 1</div>
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </>
                    )}

                    {calendarView === 'week' && (
                      <>
                        <div className="grid grid-cols-8 gap-px bg-gray-200">
                          <div className="bg-gray-50 p-3 text-center text-sm font-medium text-gray-700">Time</div>
                          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => {
                            const weekStart = new Date(currentCalendarDate);
                            weekStart.setDate(currentCalendarDate.getDate() - currentCalendarDate.getDay());
                            const dayDate = new Date(weekStart);
                            dayDate.setDate(weekStart.getDate() + index);
                            // Current date is July 25, 2025
                            const today = new Date(2025, 6, 25); // Month is 0-indexed, so 6 = July
                            const isToday = dayDate.toDateString() === today.toDateString();
                            return (
                              <div key={day} className={`bg-gray-50 p-3 text-center text-sm font-medium ${isToday ? 'text-blue-600 bg-blue-50' : 'text-gray-700'}`}>
                                <div>{day}</div>
                                <div className="text-xs">{dayDate.getDate()}</div>
                              </div>
                            );
                          })}
                        </div>
                        <div className="grid grid-cols-8 gap-px bg-gray-200">
                          {['6 AM', '7 AM', '8 AM', '2 PM', '3 PM', '10 PM', '11 PM'].map((time, timeIndex) => (
                            <React.Fragment key={time}>
                              <div className="bg-gray-50 p-2 text-xs text-gray-600 text-center">{time}</div>
                              {Array.from({ length: 7 }, (_, dayIndex) => (
                                <div key={`${timeIndex}-${dayIndex}`} className="bg-white p-2 h-16 text-xs border-r border-b border-gray-100">
                                  {timeIndex < 3 && dayIndex % 2 === 0 && (
                                    <div className="bg-blue-100 text-blue-800 px-1 rounded mb-1">Sarah J.</div>
                                  )}
                                  {timeIndex >= 3 && timeIndex < 5 && dayIndex % 3 === 0 && (
                                    <div className="bg-green-100 text-green-800 px-1 rounded mb-1">Mike C.</div>
                                  )}
                                  {timeIndex >= 5 && dayIndex % 2 === 1 && (
                                    <div className="bg-purple-100 text-purple-800 px-1 rounded mb-1">Emily R.</div>
                                  )}
                                </div>
                              ))}
                            </React.Fragment>
                          ))}
                        </div>
                      </>
                    )}

                    {calendarView === 'day' && (
                      <>
                        <div className="bg-gray-50 p-4 border-b border-gray-200">
                          <h5 className="text-lg font-medium text-gray-900">
                            {currentCalendarDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })}
                          </h5>
                        </div>
                        <div className="p-4 space-y-4">
                          {[
                            { time: '6:00 AM - 2:00 PM', shift: 'Day Shift', staff: ['Sarah Johnson (RN)', 'Michael Chen (CNA)', 'Lisa Park (CNA)'], color: 'blue' },
                            { time: '2:00 PM - 10:00 PM', shift: 'Evening Shift', staff: ['Emily Rodriguez (RN)', 'David Wilson (LPN)'], color: 'green' },
                            { time: '10:00 PM - 6:00 AM', shift: 'Night Shift', staff: ['Maria Garcia (RN)'], color: 'purple' }
                          ].map((shift, index) => (
                            <div key={index} className={`border-l-4 border-${shift.color}-500 bg-${shift.color}-50 p-4 rounded-r-lg`}>
                              <div className="flex items-center justify-between mb-2">
                                <h6 className={`font-medium text-${shift.color}-900`}>{shift.shift}</h6>
                                <span className={`text-sm text-${shift.color}-700`}>{shift.time}</span>
                              </div>
                              <div className="space-y-1">
                                {shift.staff.map((person, personIndex) => (
                                  <div key={personIndex} className={`text-sm text-${shift.color}-800 flex items-center justify-between`}>
                                    <span>{person}</span>
                                    <div className="flex space-x-1">
                                      <button className={`px-2 py-1 bg-${shift.color}-200 text-${shift.color}-800 text-xs rounded hover:bg-${shift.color}-300`}>
                                        View
                                      </button>
                                      <button className={`px-2 py-1 bg-${shift.color}-200 text-${shift.color}-800 text-xs rounded hover:bg-${shift.color}-300`}>
                                        Contact
                                      </button>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </>
                    )}
                  </div>

                  {/* Legend */}
                  <div className="flex items-center justify-center space-x-6 text-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-100 rounded mr-2"></div>
                      <span>Day Shift</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-100 rounded mr-2"></div>
                      <span>Evening Shift</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-purple-100 rounded mr-2"></div>
                      <span>Night Shift</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => setShowViewCalendarModal(false)}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Credential Modal */}
      {showAddCredentialModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddCredentialModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <AcademicCapIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Add Credential
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedStaff ? `Add credential for ${selectedStaff.firstName} ${selectedStaff.lastName}` : 'Add new credential'}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAddCredentialModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  {!selectedStaff && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Staff Member</label>
                      <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option>Select staff member...</option>
                        {staffMembers.map((staff) => (
                          <option key={staff.id} value={staff.id}>
                            {staff.firstName} {staff.lastName} - {staff.role}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Credential Type</label>
                    <select
                      value={newCredential.type}
                      onChange={(e) => setNewCredential({...newCredential, type: e.target.value})}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="certification">Certification</option>
                      <option value="license">License</option>
                      <option value="training">Training</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Credential Name</label>
                    <input
                      type="text"
                      value={newCredential.name}
                      onChange={(e) => setNewCredential({...newCredential, name: e.target.value})}
                      placeholder="e.g., CPR Certification, RN License, HIPAA Training"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Issuing Organization</label>
                    <input
                      type="text"
                      value={newCredential.issuingOrganization}
                      onChange={(e) => setNewCredential({...newCredential, issuingOrganization: e.target.value})}
                      placeholder="e.g., American Red Cross, State Board of Nursing"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Issue Date</label>
                      <input
                        type="date"
                        value={newCredential.issueDate}
                        onChange={(e) => setNewCredential({...newCredential, issueDate: e.target.value})}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Expiry Date</label>
                      <input
                        type="date"
                        value={newCredential.expiryDate}
                        onChange={(e) => setNewCredential({...newCredential, expiryDate: e.target.value})}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Credential Number (Optional)</label>
                    <input
                      type="text"
                      value={newCredential.credentialNumber}
                      onChange={(e) => setNewCredential({...newCredential, credentialNumber: e.target.value})}
                      placeholder="License/Certificate number"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Credential added successfully!');
                    setShowAddCredentialModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Add Credential
                </button>
                <button
                  onClick={() => setShowAddCredentialModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Performance Review Modal */}
      {showPerformanceModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowPerformanceModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 sm:mx-0 sm:h-10 sm:w-10">
                      <StarIcon className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Performance Review
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedStaff ? `Review for ${selectedStaff.firstName} ${selectedStaff.lastName}` : 'Add performance review'}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowPerformanceModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6 max-h-96 overflow-y-auto">
                  {!selectedStaff && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Staff Member</label>
                      <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm">
                        <option>Select staff member...</option>
                        {staffMembers.map((staff) => (
                          <option key={staff.id} value={staff.id}>
                            {staff.firstName} {staff.lastName} - {staff.role}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  {/* Rating Sections */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Overall Rating</label>
                      <div className="flex items-center space-x-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <button
                            key={star}
                            onClick={() => setPerformanceReview({...performanceReview, overallRating: star})}
                            className={`h-6 w-6 ${star <= performanceReview.overallRating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                          >
                            <StarIcon />
                          </button>
                        ))}
                        <span className="ml-2 text-sm text-gray-600">{performanceReview.overallRating}/5</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Resident Satisfaction</label>
                      <div className="flex items-center space-x-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <button
                            key={star}
                            onClick={() => setPerformanceReview({...performanceReview, residentSatisfaction: star})}
                            className={`h-6 w-6 ${star <= performanceReview.residentSatisfaction ? 'text-green-400 fill-current' : 'text-gray-300'}`}
                          >
                            <StarIcon />
                          </button>
                        ))}
                        <span className="ml-2 text-sm text-gray-600">{performanceReview.residentSatisfaction}/5</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Punctuality</label>
                      <div className="flex items-center space-x-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <button
                            key={star}
                            onClick={() => setPerformanceReview({...performanceReview, punctuality: star})}
                            className={`h-6 w-6 ${star <= performanceReview.punctuality ? 'text-blue-400 fill-current' : 'text-gray-300'}`}
                          >
                            <StarIcon />
                          </button>
                        ))}
                        <span className="ml-2 text-sm text-gray-600">{performanceReview.punctuality}/5</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Teamwork</label>
                      <div className="flex items-center space-x-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <button
                            key={star}
                            onClick={() => setPerformanceReview({...performanceReview, teamwork: star})}
                            className={`h-6 w-6 ${star <= performanceReview.teamwork ? 'text-purple-400 fill-current' : 'text-gray-300'}`}
                          >
                            <StarIcon />
                          </button>
                        ))}
                        <span className="ml-2 text-sm text-gray-600">{performanceReview.teamwork}/5</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Strengths</label>
                    <textarea
                      rows={3}
                      value={performanceReview.strengths}
                      onChange={(e) => setPerformanceReview({...performanceReview, strengths: e.target.value})}
                      placeholder="List key strengths and positive contributions..."
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Areas for Improvement</label>
                    <textarea
                      rows={3}
                      value={performanceReview.improvementAreas}
                      onChange={(e) => setPerformanceReview({...performanceReview, improvementAreas: e.target.value})}
                      placeholder="Areas where growth and development are needed..."
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Goals for Next Period</label>
                    <textarea
                      rows={3}
                      value={performanceReview.goals}
                      onChange={(e) => setPerformanceReview({...performanceReview, goals: e.target.value})}
                      placeholder="Specific goals and objectives for the next review period..."
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Review Date</label>
                      <input
                        type="date"
                        value={performanceReview.reviewDate}
                        onChange={(e) => setPerformanceReview({...performanceReview, reviewDate: e.target.value})}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Reviewer Name</label>
                      <input
                        type="text"
                        value={performanceReview.reviewerName}
                        onChange={(e) => setPerformanceReview({...performanceReview, reviewerName: e.target.value})}
                        placeholder="Name of reviewing supervisor"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Additional Comments</label>
                    <textarea
                      rows={3}
                      value={performanceReview.comments}
                      onChange={(e) => setPerformanceReview({...performanceReview, comments: e.target.value})}
                      placeholder="Any additional feedback or notes..."
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Performance review saved successfully!');
                    setShowPerformanceModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Save Review
                </button>
                <button
                  onClick={() => setShowPerformanceModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StaffManagement;