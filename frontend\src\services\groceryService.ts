import { api } from './api';

// Types for Grocery Service
export interface GroceryItem {
  id: string;
  name: string;
  category: 'produce' | 'dairy' | 'meat' | 'pantry' | 'frozen' | 'beverages' | 'snacks' | 'condiments' | 'cleaning' | 'medical_supplies';
  subcategory?: string;
  brand?: string;
  description?: string;
  barcode?: string;
  unit: 'lbs' | 'oz' | 'gallons' | 'quarts' | 'pieces' | 'boxes' | 'cans' | 'bags' | 'packages';
  unitSize: string;
  packSize: number; // items per package
  currentStock: number;
  minimumStock: number;
  maximumStock: number;
  reorderPoint: number;
  averageConsumption: number; // per week
  seasonalVariation: {
    spring: number;
    summer: number;
    fall: number;
    winter: number;
  };
  lastOrderDate: string;
  lastOrderQuantity: number;
  lastReceivedDate?: string;
  lastReceivedQuantity?: number;
  expirationDate?: string;
  shelfLife: number; // days
  cost: number;
  averageCost: number;
  lastCostUpdate: string;
  vendor: string;
  alternativeVendors: string[];
  nutritionalInfo?: {
    servingSize: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sodium: number;
    sugar: number;
    allergens: string[];
    vitamins: Record<string, number>;
    minerals: Record<string, number>;
  };
  dietaryRestrictions: string[];
  certifications: string[]; // organic, kosher, halal, etc.
  storageRequirements: 'refrigerated' | 'frozen' | 'dry' | 'room_temperature';
  storageLocation: string;
  isEssential: boolean;
  autoReorder: boolean;
  qualityGrade: 'A' | 'B' | 'C';
  supplierRating: number; // 1-5
  residentPreference: number; // 1-5
  wastePercentage: number;
  tags: string[];
}

export interface GroceryOrder {
  id: string;
  orderNumber: string;
  vendor: string;
  vendorOrderId?: string;
  orderDate: string;
  requestedDelivery: string;
  expectedDelivery: string;
  actualDelivery?: string;
  status: 'draft' | 'submitted' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled' | 'partially_delivered';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  orderType: 'regular' | 'emergency' | 'bulk' | 'special_request';
  items: Array<{
    itemId: string;
    itemName: string;
    quantity: number;
    unitCost: number;
    totalCost: number;
    urgency: 'low' | 'medium' | 'high' | 'critical';
    substitutionAllowed: boolean;
    specialInstructions?: string;
    receivedQuantity?: number;
    qualityRating?: number;
    expirationDate?: string;
  }>;
  subtotal: number;
  tax: number;
  deliveryFee: number;
  discount: number;
  totalAmount: number;
  paymentMethod: 'credit_card' | 'net_30' | 'net_15' | 'cod' | 'account_credit';
  deliveryInstructions?: string;
  specialRequests?: string;
  orderedBy: string;
  approvedBy?: string;
  approvalDate?: string;
  receivedBy?: string;
  receivedDate?: string;
  invoiceNumber?: string;
  trackingNumber?: string;
  discrepancies?: Array<{
    itemId: string;
    itemName: string;
    expected: number;
    received: number;
    reason: string;
    action: 'credit' | 'replacement' | 'accepted';
  }>;
  qualityIssues?: Array<{
    itemId: string;
    issue: string;
    severity: 'minor' | 'major' | 'critical';
    action: 'return' | 'credit' | 'replacement';
  }>;
}

export interface Vendor {
  id: string;
  name: string;
  type: 'grocery_chain' | 'wholesale' | 'specialty' | 'local_farm' | 'medical_supply' | 'restaurant_supply';
  businessLicense: string;
  taxId: string;
  contactInfo: {
    primaryContact: string;
    phone: string;
    email: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
    website?: string;
    orderingPortal?: string;
  };
  deliverySchedule: {
    days: string[];
    timeSlots: Array<{
      start: string;
      end: string;
      capacity: number;
    }>;
    minimumOrder: number;
    deliveryFee: number;
    freeDeliveryThreshold: number;
    deliveryRadius: number; // miles
    emergencyDelivery: boolean;
    emergencyFee: number;
  };
  paymentTerms: {
    method: 'credit_card' | 'net_30' | 'net_15' | 'cod' | 'prepaid';
    creditLimit?: number;
    discounts: Array<{
      condition: string;
      percentage: number;
      minimumAmount?: number;
    }>;
    lateFee: number;
    earlyPaymentDiscount?: number;
  };
  performance: {
    onTimeDelivery: number; // percentage
    orderAccuracy: number; // percentage
    qualityRating: number; // 1-5
    responsiveness: number; // 1-5
    priceCompetitiveness: number; // 1-5
    totalOrders: number;
    totalSpent: number;
    averageOrderValue: number;
    lastOrderDate: string;
  };
  capabilities: {
    categories: string[];
    specialties: string[];
    certifications: string[];
    organicProducts: boolean;
    localProducts: boolean;
    bulkOrders: boolean;
    customOrders: boolean;
    nutritionalLabeling: boolean;
    temperatureControlled: boolean;
  };
  contracts: Array<{
    id: string;
    type: 'standard' | 'volume_discount' | 'exclusive';
    startDate: string;
    endDate: string;
    terms: string[];
    discountTiers: Array<{
      minimumVolume: number;
      discountPercentage: number;
    }>;
  }>;
  isPreferred: boolean;
  isActive: boolean;
  riskLevel: 'low' | 'medium' | 'high';
  insuranceInfo: {
    liability: number;
    productLiability: number;
    expiryDate: string;
  };
}

export interface InventoryAlert {
  id: string;
  type: 'low_stock' | 'expiring_soon' | 'expired' | 'overstock' | 'missing_item' | 'quality_issue' | 'price_change';
  severity: 'low' | 'medium' | 'high' | 'critical';
  itemId: string;
  itemName: string;
  category: string;
  message: string;
  details: {
    currentStock?: number;
    minimumStock?: number;
    maximumStock?: number;
    expirationDate?: string;
    daysUntilExpiry?: number;
    priceChange?: {
      oldPrice: number;
      newPrice: number;
      changePercentage: number;
    };
    qualityIssue?: {
      description: string;
      affectedQuantity: number;
      reportedBy: string;
    };
  };
  suggestedAction: string;
  automatedAction?: string;
  createdDate: string;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedDate?: string;
  resolvedDate?: string;
  resolutionNotes?: string;
  escalated: boolean;
  escalatedTo?: string;
}

export interface BudgetAnalysis {
  period: {
    start: string;
    end: string;
  };
  totalBudget: number;
  totalSpent: number;
  remainingBudget: number;
  budgetUtilization: number; // percentage
  categoryBreakdown: Array<{
    category: string;
    budgeted: number;
    spent: number;
    remaining: number;
    variance: number;
    variancePercentage: number;
  }>;
  vendorBreakdown: Array<{
    vendorId: string;
    vendorName: string;
    totalSpent: number;
    orderCount: number;
    averageOrderValue: number;
    percentageOfTotal: number;
  }>;
  trends: {
    monthlySpending: Array<{
      month: string;
      amount: number;
      orderCount: number;
    }>;
    seasonalPatterns: Record<string, number>;
    priceInflation: number; // percentage
  };
  costSavingOpportunities: Array<{
    opportunity: string;
    potentialSavings: number;
    implementation: string;
    priority: 'low' | 'medium' | 'high';
  }>;
  recommendations: string[];
}

class GroceryService {
  // Get All Grocery Items
  async getGroceryItems(filters?: {
    category?: string;
    lowStock?: boolean;
    expiringSoon?: boolean;
    vendor?: string;
    search?: string;
  }): Promise<GroceryItem[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // Mock grocery items would be returned here
          resolve([]);
        }, 1000);
      });
    }

    try {
      const response = await api.get<GroceryItem[]>('/grocery/items', { params: filters });
      return response.data;
    } catch (error) {
      throw new Error('Failed to get grocery items');
    }
  }

  // Create Grocery Order
  async createGroceryOrder(orderData: Partial<GroceryOrder>): Promise<GroceryOrder> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ...orderData,
            id: `order_${Date.now()}`,
            orderNumber: `ORD-${Date.now()}`,
            status: 'draft'
          } as GroceryOrder);
        }, 1500);
      });
    }

    try {
      const response = await api.post<GroceryOrder>('/grocery/orders', orderData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to create grocery order');
    }
  }

  // Get AI Reorder Suggestions
  async getAIReorderSuggestions(options?: {
    urgencyLevel?: 'all' | 'critical' | 'high' | 'medium';
    category?: string;
    budget?: number;
    deliveryDate?: string;
  }): Promise<Array<{
    item: GroceryItem;
    suggestedQuantity: number;
    reasoning: string;
    urgency: 'low' | 'medium' | 'high' | 'critical';
    costImpact: number;
    stockoutRisk: number; // percentage
    bulkDiscountAvailable: boolean;
    alternativeOptions: Array<{
      itemId: string;
      itemName: string;
      costSavings: number;
      qualityDifference: string;
    }>;
  }>> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([
            {
              item: {} as GroceryItem,
              suggestedQuantity: 20,
              reasoning: 'Current stock below minimum threshold, high consumption rate',
              urgency: 'high',
              costImpact: 79.80,
              stockoutRisk: 85,
              bulkDiscountAvailable: true,
              alternativeOptions: []
            }
          ]);
        }, 2000);
      });
    }

    try {
      const response = await api.post('/grocery/ai-suggestions', options);
      return response.data;
    } catch (error) {
      throw new Error('Failed to get AI reorder suggestions');
    }
  }

  // Get Inventory Alerts
  async getInventoryAlerts(filters?: {
    severity?: string;
    type?: string;
    acknowledged?: boolean;
  }): Promise<InventoryAlert[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([]);
        }, 800);
      });
    }

    try {
      const response = await api.get<InventoryAlert[]>('/grocery/alerts', { params: filters });
      return response.data;
    } catch (error) {
      throw new Error('Failed to get inventory alerts');
    }
  }

  // Get Budget Analysis
  async getBudgetAnalysis(period: { start: string; end: string }): Promise<BudgetAnalysis> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            period,
            totalBudget: 5000,
            totalSpent: 3247,
            remainingBudget: 1753,
            budgetUtilization: 64.94,
            categoryBreakdown: [],
            vendorBreakdown: [],
            trends: {
              monthlySpending: [],
              seasonalPatterns: {},
              priceInflation: 3.2
            },
            costSavingOpportunities: [],
            recommendations: []
          });
        }, 1500);
      });
    }

    try {
      const response = await api.post<BudgetAnalysis>('/grocery/budget-analysis', { period });
      return response.data;
    } catch (error) {
      throw new Error('Failed to get budget analysis');
    }
  }

  // Optimize Meal Plan Grocery List
  async optimizeMealPlanGroceries(mealPlanId: string, options?: {
    budget?: number;
    preferredVendors?: string[];
    dietaryRestrictions?: string[];
    bulkDiscounts?: boolean;
  }): Promise<{
    optimizedList: Array<{
      itemId: string;
      itemName: string;
      quantity: number;
      vendor: string;
      cost: number;
      mealUsage: string[];
    }>;
    totalCost: number;
    savings: number;
    nutritionalBalance: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
      fiber: number;
      sodium: number;
    };
    recommendations: string[];
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            optimizedList: [],
            totalCost: 247.85,
            savings: 32.15,
            nutritionalBalance: {
              calories: 1850,
              protein: 85,
              carbs: 220,
              fat: 65,
              fiber: 28,
              sodium: 1950
            },
            recommendations: [
              'Consider bulk purchase of rice for 12% savings',
              'Switch to seasonal vegetables for better pricing',
              'Combine orders from FreshMart for free delivery'
            ]
          });
        }, 2500);
      });
    }

    try {
      const response = await api.post('/grocery/optimize-meal-plan', { mealPlanId, options });
      return response.data;
    } catch (error) {
      throw new Error('Failed to optimize meal plan groceries');
    }
  }

  // Track Delivery
  async trackDelivery(orderId: string): Promise<{
    status: string;
    estimatedArrival: string;
    currentLocation?: string;
    trackingEvents: Array<{
      timestamp: string;
      status: string;
      location: string;
      description: string;
    }>;
    driverInfo?: {
      name: string;
      phone: string;
      vehicle: string;
    };
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            status: 'in_transit',
            estimatedArrival: '2024-01-22T10:30:00Z',
            currentLocation: '5 miles away',
            trackingEvents: [
              {
                timestamp: '2024-01-22T08:00:00Z',
                status: 'departed',
                location: 'Distribution Center',
                description: 'Package departed from distribution center'
              }
            ],
            driverInfo: {
              name: 'John Delivery',
              phone: '******-0199',
              vehicle: 'White Van - ABC123'
            }
          });
        }, 1000);
      });
    }

    try {
      const response = await api.get(`/grocery/orders/${orderId}/tracking`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to track delivery');
    }
  }
}

export const groceryService = new GroceryService();
