{"ast": null, "code": "export const version = '2.11.15';", "map": {"version": 3, "names": ["version"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@supabase\\realtime-js\\src\\lib\\version.ts"], "sourcesContent": ["export const version = '2.11.15'\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}