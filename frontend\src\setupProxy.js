// Setup proxy for development server
// This handles client-side routing and API proxying

module.exports = function(app) {
  // Handle client-side routing - serve index.html for all non-API routes
  // This fixes 404 errors when refreshing pages or accessing URLs directly
  app.use((req, res, next) => {
    // Only handle GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Skip API routes
    if (req.path.startsWith('/api')) {
      return next();
    }

    // Skip static files (files with extensions)
    if (req.path.includes('.')) {
      return next();
    }

    // For all other routes, serve index.html to let React Router handle it
    req.url = '/';
    next();
  });
};
