#!/usr/bin/env python3
"""
Wireless Document Signing WebSocket Server
Handles real-time communication between document viewers and mobile signing devices
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Set, Optional
import websockets

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SigningDevice:
    def __init__(self, websocket, device_id: str, device_name: str, device_type: str):
        self.websocket = websocket
        self.device_id = device_id
        self.device_name = device_name
        self.device_type = device_type
        self.connected_at = datetime.now()
        self.status = 'connected'

class DocumentViewer:
    def __init__(self, websocket, viewer_id: str, document_id: str, signature_area_id: str):
        self.websocket = websocket
        self.viewer_id = viewer_id
        self.document_id = document_id
        self.signature_area_id = signature_area_id
        self.connected_at = datetime.now()

class WirelessSigningServer:
    def __init__(self):
        self.signing_devices: Dict[str, SigningDevice] = {}
        self.document_viewers: Dict[str, DocumentViewer] = {}
        self.active_requests: Dict[str, dict] = {}
        
    async def register_device(self, websocket, data: dict):
        """Register a new signing device"""
        device_id = str(uuid.uuid4())
        device_name = data.get('device_name', 'Unknown Device')
        device_type = data.get('device_type', 'mobile')
        
        device = SigningDevice(websocket, device_id, device_name, device_type)
        self.signing_devices[device_id] = device
        
        logger.info(f"Signing device registered: {device_name} ({device_id})")
        
        # Confirm registration
        await websocket.send(json.dumps({
            'type': 'registration_confirmed',
            'device_id': device_id,
            'device_name': device_name,
            'device_type': device_type
        }))
        
        return device_id
    
    async def register_viewer(self, websocket, data: dict):
        """Register a document viewer"""
        viewer_id = str(uuid.uuid4())
        document_id = data.get('document_id')
        signature_area_id = data.get('signature_area_id')
        
        viewer = DocumentViewer(websocket, viewer_id, document_id, signature_area_id)
        self.document_viewers[viewer_id] = viewer
        
        logger.info(f"Document viewer registered: {document_id} ({viewer_id})")
        
        # Send list of available devices
        devices = [
            {
                'device_id': device.device_id,
                'device_name': device.device_name,
                'device_type': device.device_type,
                'status': device.status
            }
            for device in self.signing_devices.values()
        ]
        
        await websocket.send(json.dumps({
            'type': 'available_devices',
            'devices': devices
        }))
        
        return viewer_id
    
    async def send_signature_request(self, data: dict):
        """Send signature request to a specific device"""
        device_id = data.get('device_id')
        document_id = data.get('document_id')
        signature_area_id = data.get('signature_area_id')
        document_title = data.get('document_title', 'Document')
        signature_area_label = data.get('signature_area_label', 'Signature Area')
        
        if device_id not in self.signing_devices:
            logger.error(f"Device not found: {device_id}")
            return False
        
        device = self.signing_devices[device_id]
        request_id = str(uuid.uuid4())
        
        # Store the request
        self.active_requests[request_id] = {
            'device_id': device_id,
            'document_id': document_id,
            'signature_area_id': signature_area_id,
            'created_at': datetime.now(),
            'status': 'pending'
        }
        
        # Send request to device
        try:
            await device.websocket.send(json.dumps({
                'type': 'signature_request_received',
                'request_id': request_id,
                'document_id': document_id,
                'signature_area_id': signature_area_id,
                'document_title': document_title,
                'signature_area_label': signature_area_label,
                'viewer_device': 'Document Viewer'
            }))
            
            device.status = 'signing'
            logger.info(f"Signature request sent to device {device_id}: {request_id}")
            return True
            
        except websockets.exceptions.ConnectionClosed:
            logger.error(f"Device {device_id} connection closed")
            await self.remove_device(device_id)
            return False
    
    async def handle_signature_response(self, websocket, data: dict):
        """Handle signature response from device"""
        request_id = data.get('request_id')
        response_type = data.get('response_type')  # 'completed', 'rejected', 'cancelled'
        signature_data = data.get('signature_data')
        
        if request_id not in self.active_requests:
            logger.error(f"Request not found: {request_id}")
            return
        
        request = self.active_requests[request_id]
        device_id = request['device_id']
        
        # Update device status
        if device_id in self.signing_devices:
            self.signing_devices[device_id].status = 'connected'
        
        # Notify all viewers of this document
        document_id = request['document_id']
        for viewer in self.document_viewers.values():
            if viewer.document_id == document_id:
                try:
                    if response_type == 'completed':
                        await viewer.websocket.send(json.dumps({
                            'type': 'signature_completed',
                            'request_id': request_id,
                            'device_id': device_id,
                            'signature_data': signature_data
                        }))
                    elif response_type == 'rejected':
                        await viewer.websocket.send(json.dumps({
                            'type': 'signature_cancelled',
                            'request_id': request_id,
                            'device_id': device_id,
                            'reason': 'rejected'
                        }))
                except websockets.exceptions.ConnectionClosed:
                    logger.error(f"Viewer connection closed")
        
        # Clean up request
        del self.active_requests[request_id]
        logger.info(f"Signature request {request_id} {response_type}")
    
    async def remove_device(self, device_id: str):
        """Remove a signing device"""
        if device_id in self.signing_devices:
            device = self.signing_devices[device_id]
            del self.signing_devices[device_id]
            logger.info(f"Device removed: {device.device_name} ({device_id})")
            
            # Notify viewers
            for viewer in self.document_viewers.values():
                try:
                    await viewer.websocket.send(json.dumps({
                        'type': 'device_disconnected',
                        'device_id': device_id
                    }))
                except websockets.exceptions.ConnectionClosed:
                    pass
    
    async def remove_viewer(self, viewer_id: str):
        """Remove a document viewer"""
        if viewer_id in self.document_viewers:
            viewer = self.document_viewers[viewer_id]
            del self.document_viewers[viewer_id]
            logger.info(f"Viewer removed: {viewer.document_id} ({viewer_id})")
    
    async def handle_message(self, websocket, message: str):
        """Handle incoming WebSocket message"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'register_device':
                await self.register_device(websocket, data)
            
            elif message_type == 'register_viewer':
                await self.register_viewer(websocket, data)
            
            elif message_type == 'signature_request':
                await self.send_signature_request(data)
            
            elif message_type == 'signature_response':
                await self.handle_signature_response(websocket, data)
            
            elif message_type == 'ping':
                await websocket.send(json.dumps({'type': 'pong'}))
            
            elif message_type == 'pong':
                pass  # Keep-alive response
            
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON message: {message}")
        except Exception as e:
            logger.error(f"Error handling message: {e}")
    
    async def handle_disconnect(self, websocket):
        """Handle client disconnect"""
        # Find and remove the disconnected client
        device_to_remove = None
        viewer_to_remove = None
        
        for device_id, device in self.signing_devices.items():
            if device.websocket == websocket:
                device_to_remove = device_id
                break
        
        for viewer_id, viewer in self.document_viewers.items():
            if viewer.websocket == websocket:
                viewer_to_remove = viewer_id
                break
        
        if device_to_remove:
            await self.remove_device(device_to_remove)
        
        if viewer_to_remove:
            await self.remove_viewer(viewer_to_remove)

# Global server instance
signing_server = WirelessSigningServer()

async def handle_client(websocket):
    """Handle new WebSocket client connection"""
    client_ip = websocket.remote_address[0] if websocket.remote_address else 'unknown'
    logger.info(f"New client connected from {client_ip}")

    try:
        async for message in websocket:
            await signing_server.handle_message(websocket, message)

    except websockets.exceptions.ConnectionClosed:
        logger.info(f"Client {client_ip} disconnected")

    except Exception as e:
        logger.error(f"Error handling client {client_ip}: {e}")

    finally:
        await signing_server.handle_disconnect(websocket)

async def main():
    """Start the WebSocket server"""
    host = "localhost"
    port = 8001
    
    logger.info(f"Starting Wireless Signing Server on {host}:{port}")
    
    async with websockets.serve(handle_client, host, port):
        logger.info("Wireless Signing Server is running...")
        logger.info("Waiting for connections...")
        await asyncio.Future()  # Run forever

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
