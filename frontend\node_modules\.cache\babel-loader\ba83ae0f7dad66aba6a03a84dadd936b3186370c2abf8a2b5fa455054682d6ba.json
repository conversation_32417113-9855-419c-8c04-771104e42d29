{"ast": null, "code": "import GoTrueAdminApi from './GoTrueAdminApi';\nconst AuthAdminApi = GoTrueAdminApi;\nexport default AuthAdminApi;", "map": {"version": 3, "names": ["GoTrueAdminApi", "AuthAdminApi"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@supabase\\auth-js\\src\\AuthAdminApi.ts"], "sourcesContent": ["import GoTrueAdminApi from './GoTrueAdminApi'\n\nconst AuthAdminApi = GoTrueAdminApi\n\nexport default AuthAdminApi\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAE7C,MAAMC,YAAY,GAAGD,cAAc;AAEnC,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}