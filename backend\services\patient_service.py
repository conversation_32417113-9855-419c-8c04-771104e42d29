"""
Patient service layer for business logic
"""

from typing import Dict, Any, List, Optional
from datetime import date, datetime
import uuid
import logging

from core.database import DatabaseService, get_db
from core.config import settings

logger = logging.getLogger(__name__)

class PatientService:
    """Service class for patient-related operations"""
    
    def __init__(self):
        self.db_service = DatabaseService("patients")
        self.client = get_db()
    
    async def create_patient(self, patient_data: Dict[str, Any], created_by: str) -> Dict[str, Any]:
        """Create a new patient"""
        try:
            # Set default values
            patient_data["admission_date"] = patient_data.get("admission_date", date.today())
            patient_data["status"] = "active"
            
            # Create patient record
            patient = await self.db_service.create(patient_data)
            
            # Log creation
            logger.info(f"Patient created: {patient['id']} by caregiver {created_by}")
            
            return await self._enrich_patient_data(patient)
        except Exception as e:
            logger.error(f"Error creating patient: {e}")
            raise
    
    async def get_patient_by_id(self, patient_id: str) -> Optional[Dict[str, Any]]:
        """Get patient by ID with enriched data"""
        try:
            patient = await self.db_service.get_by_id(patient_id)
            if patient:
                return await self._enrich_patient_data(patient)
            return None
        except Exception as e:
            logger.error(f"Error getting patient {patient_id}: {e}")
            raise
    
    async def get_patients(
        self, 
        filters: Optional[Dict[str, Any]] = None,
        search_term: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get patients with filtering and search"""
        try:
            query = self.client.table("patients").select("""
                id, patient_number, first_name, last_name, date_of_birth,
                status, care_level, fall_risk_score, primary_caregiver_id,
                created_at, updated_at
            """)
            
            # Apply filters
            if filters:
                if "status" in filters:
                    query = query.eq("status", filters["status"])
                
                if "caregiver_filter" in filters:
                    query = query.or_(
                        f"primary_caregiver_id.eq.{filters['caregiver_filter']},"
                        f"backup_caregiver_id.eq.{filters['caregiver_filter']}"
                    )
            
            # Apply search
            if search_term:
                query = query.or_(
                    f"first_name.ilike.%{search_term}%,"
                    f"last_name.ilike.%{search_term}%,"
                    f"patient_number.ilike.%{search_term}%"
                )
            
            # Apply pagination and ordering
            query = query.order("created_at", desc=True).range(offset, offset + limit - 1)
            
            result = query.execute()
            patients = result.data or []
            
            # Enrich with computed fields
            enriched_patients = []
            for patient in patients:
                enriched_patient = await self._enrich_patient_summary(patient)
                enriched_patients.append(enriched_patient)
            
            return enriched_patients
        except Exception as e:
            logger.error(f"Error getting patients: {e}")
            raise
    
    async def update_patient(self, patient_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update patient information"""
        try:
            patient = await self.db_service.update(patient_id, update_data)
            return await self._enrich_patient_data(patient)
        except Exception as e:
            logger.error(f"Error updating patient {patient_id}: {e}")
            raise
    
    async def delete_patient(self, patient_id: str) -> bool:
        """Soft delete patient (set status to inactive)"""
        try:
            await self.db_service.update(patient_id, {
                "status": "inactive",
                "discharge_date": date.today()
            })
            logger.info(f"Patient {patient_id} marked as inactive")
            return True
        except Exception as e:
            logger.error(f"Error deleting patient {patient_id}: {e}")
            raise
    
    async def count_patients(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count patients with filters"""
        try:
            query = self.client.table("patients").select("id", count="exact")
            
            if filters:
                if "status" in filters:
                    query = query.eq("status", filters["status"])
                
                if "caregiver_filter" in filters:
                    query = query.or_(
                        f"primary_caregiver_id.eq.{filters['caregiver_filter']},"
                        f"backup_caregiver_id.eq.{filters['caregiver_filter']}"
                    )
            
            result = query.execute()
            return result.count or 0
        except Exception as e:
            logger.error(f"Error counting patients: {e}")
            raise
    
    async def assign_caregiver(
        self, 
        patient_id: str, 
        caregiver_id: str, 
        is_primary: bool = True
    ) -> bool:
        """Assign caregiver to patient"""
        try:
            field = "primary_caregiver_id" if is_primary else "backup_caregiver_id"
            await self.db_service.update(patient_id, {field: caregiver_id})
            
            logger.info(f"Caregiver {caregiver_id} assigned to patient {patient_id} as {'primary' if is_primary else 'backup'}")
            return True
        except Exception as e:
            logger.error(f"Error assigning caregiver: {e}")
            raise
    
    async def get_patient_ai_summary(self, patient_id: str) -> Optional[Dict[str, Any]]:
        """Get patient summary for AI analysis"""
        try:
            # Use the database function for AI summary
            result = self.client.rpc("get_patient_ai_summary", {"patient_uuid": patient_id}).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting AI summary for patient {patient_id}: {e}")
            raise
    
    async def _enrich_patient_data(self, patient: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich patient data with computed fields and related data"""
        try:
            # Calculate age
            if patient.get("date_of_birth"):
                birth_date = datetime.strptime(patient["date_of_birth"], "%Y-%m-%d").date()
                today = date.today()
                patient["age"] = today.year - birth_date.year - (
                    (today.month, today.day) < (birth_date.month, birth_date.day)
                )
            
            # Get caregiver names
            if patient.get("primary_caregiver_id"):
                caregiver_result = self.client.table("caregivers").select(
                    "first_name, last_name"
                ).eq("id", patient["primary_caregiver_id"]).execute()
                
                if caregiver_result.data:
                    caregiver = caregiver_result.data[0]
                    patient["primary_caregiver_name"] = f"{caregiver['first_name']} {caregiver['last_name']}"
            
            if patient.get("backup_caregiver_id"):
                caregiver_result = self.client.table("caregivers").select(
                    "first_name, last_name"
                ).eq("id", patient["backup_caregiver_id"]).execute()
                
                if caregiver_result.data:
                    caregiver = caregiver_result.data[0]
                    patient["backup_caregiver_name"] = f"{caregiver['first_name']} {caregiver['last_name']}"
            
            return patient
        except Exception as e:
            logger.error(f"Error enriching patient data: {e}")
            return patient
    
    async def _enrich_patient_summary(self, patient: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich patient summary with minimal computed fields"""
        try:
            # Calculate age
            if patient.get("date_of_birth"):
                birth_date = datetime.strptime(patient["date_of_birth"], "%Y-%m-%d").date()
                today = date.today()
                patient["age"] = today.year - birth_date.year - (
                    (today.month, today.day) < (birth_date.month, birth_date.day)
                )
            
            # Get primary caregiver name
            if patient.get("primary_caregiver_id"):
                caregiver_result = self.client.table("caregivers").select(
                    "first_name, last_name"
                ).eq("id", patient["primary_caregiver_id"]).execute()
                
                if caregiver_result.data:
                    caregiver = caregiver_result.data[0]
                    patient["primary_caregiver_name"] = f"{caregiver['first_name']} {caregiver['last_name']}"
            
            # Get last visit date
            visit_result = self.client.table("medical_records").select(
                "record_date"
            ).eq("patient_id", patient["id"]).order(
                "record_date", desc=True
            ).limit(1).execute()
            
            if visit_result.data:
                patient["last_visit_date"] = visit_result.data[0]["record_date"]
            
            return patient
        except Exception as e:
            logger.error(f"Error enriching patient summary: {e}")
            return patient
