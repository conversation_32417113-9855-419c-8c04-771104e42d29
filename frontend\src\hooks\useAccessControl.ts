import { useState, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { hasPermission, canAccessModule, UserRole } from '../utils/roleBasedAccess';

interface AccessDeniedInfo {
  module: string;
  requiredAction?: string;
  userRole: UserRole;
}

export const useAccessControl = () => {
  const { user } = useAuth();
  const [accessDeniedModal, setAccessDeniedModal] = useState<{
    isOpen: boolean;
    info: AccessDeniedInfo | null;
  }>({
    isOpen: false,
    info: null,
  });

  const userRole = (user?.role as UserRole) || 'caregiver';

  const checkAccess = useCallback((
    module: keyof import('../utils/roleBasedAccess').RolePermissions,
    action: keyof import('../utils/roleBasedAccess').Permission = 'read'
  ): boolean => {
    if (!user) return false;
    return hasPermission(userRole, module, action);
  }, [user, userRole]);

  const checkModuleAccess = useCallback((
    module: keyof import('../utils/roleBasedAccess').RolePermissions
  ): boolean => {
    if (!user) return false;
    return canAccessModule(userRole, module);
  }, [user, userRole]);

  const showAccessDenied = useCallback((
    module: string,
    requiredAction?: string
  ) => {
    setAccessDeniedModal({
      isOpen: true,
      info: {
        module,
        requiredAction,
        userRole,
      },
    });
  }, [userRole]);

  const hideAccessDenied = useCallback(() => {
    setAccessDeniedModal({
      isOpen: false,
      info: null,
    });
  }, []);

  const requireAccess = useCallback((
    module: keyof import('../utils/roleBasedAccess').RolePermissions,
    action: keyof import('../utils/roleBasedAccess').Permission = 'read',
    onDenied?: () => void
  ): boolean => {
    const hasAccess = checkAccess(module, action);
    
    if (!hasAccess) {
      showAccessDenied(module, action);
      onDenied?.();
    }
    
    return hasAccess;
  }, [checkAccess, showAccessDenied]);

  const requireModuleAccess = useCallback((
    module: keyof import('../utils/roleBasedAccess').RolePermissions,
    onDenied?: () => void
  ): boolean => {
    const hasAccess = checkModuleAccess(module);
    
    if (!hasAccess) {
      showAccessDenied(module);
      onDenied?.();
    }
    
    return hasAccess;
  }, [checkModuleAccess, showAccessDenied]);

  return {
    userRole,
    checkAccess,
    checkModuleAccess,
    requireAccess,
    requireModuleAccess,
    showAccessDenied,
    hideAccessDenied,
    accessDeniedModal,
  };
};
