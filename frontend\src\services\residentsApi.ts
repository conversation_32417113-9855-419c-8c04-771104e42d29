import api from './api';

// Resident Types
export interface Resident {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  age: number;
  gender: 'male' | 'female' | 'other';
  roomNumber: string;
  admissionDate: string;
  status: 'active' | 'inactive' | 'discharged';
  careLevel: 'independent' | 'assisted_living' | 'memory_care' | 'skilled_nursing';
  primaryCaregiver: string;
  imageUrl?: string;
  medicalInfo: {
    height: { feet: number; inches: number };
    weight: number;
    dietType?: string;
    allergies: {
      drug: string[];
      food: string[];
    };
    diagnosis: string[];
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
  };
  primaryCareProvider: {
    name: string;
    specialty: string;
    phone: string;
    email?: string;
  };
  powerOfAttorney?: {
    name: string;
    type: 'healthcare' | 'financial' | 'general';
    phone: string;
    email?: string;
  };
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateResidentRequest {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  roomNumber: string;
  admissionDate: string;
  careLevel: 'independent' | 'assisted_living' | 'memory_care' | 'skilled_nursing';
  primaryCaregiver: string;
  imageUrl?: string;
  medicalInfo: {
    height: { feet: number; inches: number };
    weight: number;
    dietType?: string;
    allergies: {
      drug: string[];
      food: string[];
    };
    diagnosis: string[];
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
  };
  primaryCareProvider: {
    name: string;
    specialty: string;
    phone: string;
    email?: string;
  };
  powerOfAttorney?: {
    name: string;
    type: 'healthcare' | 'financial' | 'general';
    phone: string;
    email?: string;
  };
  notes?: string;
}

export interface UpdateResidentRequest extends Partial<CreateResidentRequest> {
  id: string;
}

// Mock Data for Development
const MOCK_RESIDENTS: Resident[] = [
  {
    id: 'R001',
    firstName: 'John',
    lastName: 'Smith',
    dateOfBirth: '1944-03-15',
    age: 79,
    gender: 'male',
    roomNumber: '101A',
    admissionDate: '2023-01-15',
    status: 'active',
    careLevel: 'assisted_living',
    primaryCaregiver: 'Sarah Johnson',
    imageUrl: undefined,
    medicalInfo: {
      height: { feet: 5, inches: 9 },
      weight: 165,
      dietType: 'Regular',
      allergies: {
        drug: ['Penicillin'],
        food: ['Shellfish']
      },
      diagnosis: ['Hypertension', 'Type 2 Diabetes']
    },
    emergencyContact: {
      name: 'Mary Smith',
      relationship: 'Daughter',
      phone: '(*************',
      email: '<EMAIL>'
    },
    primaryCareProvider: {
      name: 'Dr. Robert Johnson',
      specialty: 'Internal Medicine',
      phone: '(*************',
      email: '<EMAIL>'
    },
    powerOfAttorney: {
      name: 'Mary Smith',
      type: 'healthcare',
      phone: '(*************',
      email: '<EMAIL>'
    },
    notes: 'Patient is cooperative and follows medication schedule well.',
    createdAt: '2023-01-15T10:00:00Z',
    updatedAt: '2025-01-15T10:00:00Z'
  },
  {
    id: 'R002',
    firstName: 'Mary',
    lastName: 'Johnson',
    dateOfBirth: '1938-07-22',
    age: 85,
    gender: 'female',
    roomNumber: '102B',
    admissionDate: '2023-02-01',
    status: 'active',
    careLevel: 'memory_care',
    primaryCaregiver: 'Michael Chen',
    imageUrl: undefined,
    medicalInfo: {
      height: { feet: 5, inches: 4 },
      weight: 140,
      dietType: 'Low Sodium',
      allergies: {
        drug: ['Aspirin'],
        food: []
      },
      diagnosis: ['Alzheimer\'s Disease', 'Osteoporosis']
    },
    emergencyContact: {
      name: 'Robert Johnson',
      relationship: 'Son',
      phone: '(*************',
      email: '<EMAIL>'
    },
    primaryCareProvider: {
      name: 'Dr. Emily Davis',
      specialty: 'Geriatrics',
      phone: '(*************',
      email: '<EMAIL>'
    },
    notes: 'Requires assistance with daily activities. Family visits regularly.',
    createdAt: '2023-02-01T10:00:00Z',
    updatedAt: '2025-01-15T10:00:00Z'
  }
];

// API Service Class
class ResidentsApiService {
  private mockMode = process.env.REACT_APP_MOCK_MODE === 'true';

  async getAllResidents(): Promise<Resident[]> {
    if (this.mockMode) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      return MOCK_RESIDENTS;
    }

    try {
      const response = await api.get('/residents');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch residents:', error);
      throw error;
    }
  }

  async getResidentById(id: string): Promise<Resident> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 300));
      const resident = MOCK_RESIDENTS.find(r => r.id === id);
      if (!resident) {
        throw new Error('Resident not found');
      }
      return resident;
    }

    try {
      const response = await api.get(`/residents/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch resident ${id}:`, error);
      throw error;
    }
  }

  async createResident(residentData: CreateResidentRequest): Promise<Resident> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 800));
      const newResident: Resident = {
        ...residentData,
        id: `R${String(MOCK_RESIDENTS.length + 1).padStart(3, '0')}`,
        age: new Date().getFullYear() - new Date(residentData.dateOfBirth).getFullYear(),
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      MOCK_RESIDENTS.push(newResident);
      return newResident;
    }

    try {
      const response = await api.post('/residents', residentData);
      return response.data;
    } catch (error) {
      console.error('Failed to create resident:', error);
      throw error;
    }
  }

  async updateResident(id: string, residentData: Partial<UpdateResidentRequest>): Promise<Resident> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 600));
      const index = MOCK_RESIDENTS.findIndex(r => r.id === id);
      if (index === -1) {
        throw new Error('Resident not found');
      }
      
      MOCK_RESIDENTS[index] = {
        ...MOCK_RESIDENTS[index],
        ...residentData,
        updatedAt: new Date().toISOString()
      };
      return MOCK_RESIDENTS[index];
    }

    try {
      const response = await api.put(`/residents/${id}`, residentData);
      return response.data;
    } catch (error) {
      console.error(`Failed to update resident ${id}:`, error);
      throw error;
    }
  }

  async deleteResident(id: string): Promise<void> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 400));
      const index = MOCK_RESIDENTS.findIndex(r => r.id === id);
      if (index === -1) {
        throw new Error('Resident not found');
      }
      MOCK_RESIDENTS.splice(index, 1);
      return;
    }

    try {
      await api.delete(`/residents/${id}`);
    } catch (error) {
      console.error(`Failed to delete resident ${id}:`, error);
      throw error;
    }
  }

  async searchResidents(query: string): Promise<Resident[]> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 300));
      const lowercaseQuery = query.toLowerCase();
      return MOCK_RESIDENTS.filter(resident =>
        resident.firstName.toLowerCase().includes(lowercaseQuery) ||
        resident.lastName.toLowerCase().includes(lowercaseQuery) ||
        resident.roomNumber.toLowerCase().includes(lowercaseQuery)
      );
    }

    try {
      const response = await api.get(`/residents/search?q=${encodeURIComponent(query)}`);
      return response.data;
    } catch (error) {
      console.error('Failed to search residents:', error);
      throw error;
    }
  }

  async uploadResidentImage(id: string, imageFile: File): Promise<string> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Simulate image upload and return mock URL
      const mockImageUrl = `data:image/jpeg;base64,${btoa('mock-image-data')}`;
      const resident = MOCK_RESIDENTS.find(r => r.id === id);
      if (resident) {
        resident.imageUrl = mockImageUrl;
        resident.updatedAt = new Date().toISOString();
      }
      return mockImageUrl;
    }

    try {
      const formData = new FormData();
      formData.append('image', imageFile);
      
      const response = await api.post(`/residents/${id}/image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data.imageUrl;
    } catch (error) {
      console.error(`Failed to upload image for resident ${id}:`, error);
      throw error;
    }
  }
}

// Export singleton instance
export const residentsApi = new ResidentsApiService();
export default residentsApi;
