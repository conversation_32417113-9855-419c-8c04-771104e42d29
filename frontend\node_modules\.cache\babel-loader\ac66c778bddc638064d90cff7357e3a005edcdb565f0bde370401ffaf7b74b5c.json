{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\medisyn\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { UserGroupIcon, DocumentTextIcon, CubeIcon, CurrencyDollarIcon, ExclamationTriangleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n\n  // Mock data - in real app, this would come from API\n  const stats = [{\n    name: 'Total Patients',\n    value: '24',\n    change: '+2 this week',\n    changeType: 'positive',\n    icon: UserGroupIcon\n  }, {\n    name: 'Pending Records',\n    value: '8',\n    change: '3 urgent',\n    changeType: 'warning',\n    icon: DocumentTextIcon\n  }, {\n    name: 'Low Stock Items',\n    value: '5',\n    change: 'Reorder needed',\n    changeType: 'negative',\n    icon: CubeIcon\n  }, {\n    name: 'Outstanding Bills',\n    value: '$12,450',\n    change: '7 overdue',\n    changeType: 'negative',\n    icon: CurrencyDollarIcon\n  }];\n  const recentActivities = [{\n    id: 1,\n    type: 'patient_visit',\n    message: 'Completed visit for Margaret Thompson',\n    time: '2 hours ago',\n    icon: CheckCircleIcon,\n    iconColor: 'text-secondary-500'\n  }, {\n    id: 2,\n    type: 'alert',\n    message: 'High fall risk alert for William Anderson',\n    time: '4 hours ago',\n    icon: ExclamationTriangleIcon,\n    iconColor: 'text-warning-500'\n  }, {\n    id: 3,\n    type: 'inventory',\n    message: 'Disposable gloves running low',\n    time: '6 hours ago',\n    icon: CubeIcon,\n    iconColor: 'text-danger-500'\n  }, {\n    id: 4,\n    type: 'billing',\n    message: 'Invoice sent to Dorothy Garcia',\n    time: '1 day ago',\n    icon: CurrencyDollarIcon,\n    iconColor: 'text-primary-500'\n  }];\n  const upcomingTasks = [{\n    id: 1,\n    task: 'Morning medication for Margaret Thompson',\n    time: '9:00 AM',\n    priority: 'high'\n  }, {\n    id: 2,\n    task: 'Physical therapy session with William Anderson',\n    time: '11:00 AM',\n    priority: 'medium'\n  }, {\n    id: 3,\n    task: 'Weekly assessment for Dorothy Garcia',\n    time: '2:00 PM',\n    priority: 'medium'\n  }, {\n    id: 4,\n    task: 'Inventory restock - Medical supplies',\n    time: '4:00 PM',\n    priority: 'low'\n  }];\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-danger-600 bg-danger-100';\n      case 'medium':\n        return 'text-warning-600 bg-warning-100';\n      case 'low':\n        return 'text-secondary-600 bg-secondary-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.first_name, \"!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-600\",\n        children: \"Here's what's happening with your patients today.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-grid\",\n      children: stats.map(stat => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                className: \"h-6 w-6 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4 flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-500\",\n                children: stat.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-semibold text-gray-900\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-sm ${stat.changeType === 'positive' ? 'text-secondary-600' : stat.changeType === 'warning' ? 'text-warning-600' : 'text-danger-600'}`,\n              children: stat.change\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this)\n      }, stat.name, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Recent Activities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: recentActivities.map(activity => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(activity.icon, {\n                  className: `h-5 w-5 ${activity.iconColor}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900\",\n                  children: activity.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: activity.time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)]\n            }, activity.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Today's Schedule\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: upcomingTasks.map(task => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-900\",\n                  children: task.task\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: task.time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `badge ${getPriorityColor(task.priority)} capitalize`,\n                children: task.priority\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)]\n            }, task.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4 sm:grid-cols-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-outline flex flex-col items-center p-4\",\n            children: [/*#__PURE__*/_jsxDEV(UserGroupIcon, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Add Patient\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-outline flex flex-col items-center p-4\",\n            children: [/*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Log Visit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-outline flex flex-col items-center p-4\",\n            children: [/*#__PURE__*/_jsxDEV(CubeIcon, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Update Inventory\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-outline flex flex-col items-center p-4\",\n            children: [/*#__PURE__*/_jsxDEV(CurrencyDollarIcon, {\n              className: \"h-6 w-6 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Create Invoice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "UserGroupIcon", "DocumentTextIcon", "CubeIcon", "CurrencyDollarIcon", "ExclamationTriangleIcon", "CheckCircleIcon", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "stats", "name", "value", "change", "changeType", "icon", "recentActivities", "id", "type", "message", "time", "iconColor", "upcomingTasks", "task", "priority", "getPriorityColor", "className", "children", "first_name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "stat", "activity", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  UserGroupIcon,\n  DocumentTextIcon,\n  CubeIcon,\n  CurrencyDollarIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n} from '@heroicons/react/24/outline';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n\n  // Mock data - in real app, this would come from API\n  const stats = [\n    {\n      name: 'Total Patients',\n      value: '24',\n      change: '+2 this week',\n      changeType: 'positive',\n      icon: UserGroupIcon,\n    },\n    {\n      name: 'Pending Records',\n      value: '8',\n      change: '3 urgent',\n      changeType: 'warning',\n      icon: DocumentTextIcon,\n    },\n    {\n      name: 'Low Stock Items',\n      value: '5',\n      change: 'Reorder needed',\n      changeType: 'negative',\n      icon: CubeIcon,\n    },\n    {\n      name: 'Outstanding Bills',\n      value: '$12,450',\n      change: '7 overdue',\n      changeType: 'negative',\n      icon: CurrencyDollarIcon,\n    },\n  ];\n\n  const recentActivities = [\n    {\n      id: 1,\n      type: 'patient_visit',\n      message: 'Completed visit for <PERSON>',\n      time: '2 hours ago',\n      icon: CheckCircleIcon,\n      iconColor: 'text-secondary-500',\n    },\n    {\n      id: 2,\n      type: 'alert',\n      message: 'High fall risk alert for William Anderson',\n      time: '4 hours ago',\n      icon: ExclamationTriangleIcon,\n      iconColor: 'text-warning-500',\n    },\n    {\n      id: 3,\n      type: 'inventory',\n      message: 'Disposable gloves running low',\n      time: '6 hours ago',\n      icon: CubeIcon,\n      iconColor: 'text-danger-500',\n    },\n    {\n      id: 4,\n      type: 'billing',\n      message: 'Invoice sent to Dorothy Garcia',\n      time: '1 day ago',\n      icon: CurrencyDollarIcon,\n      iconColor: 'text-primary-500',\n    },\n  ];\n\n  const upcomingTasks = [\n    {\n      id: 1,\n      task: 'Morning medication for Margaret Thompson',\n      time: '9:00 AM',\n      priority: 'high',\n    },\n    {\n      id: 2,\n      task: 'Physical therapy session with William Anderson',\n      time: '11:00 AM',\n      priority: 'medium',\n    },\n    {\n      id: 3,\n      task: 'Weekly assessment for Dorothy Garcia',\n      time: '2:00 PM',\n      priority: 'medium',\n    },\n    {\n      id: 4,\n      task: 'Inventory restock - Medical supplies',\n      time: '4:00 PM',\n      priority: 'low',\n    },\n  ];\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'text-danger-600 bg-danger-100';\n      case 'medium':\n        return 'text-warning-600 bg-warning-100';\n      case 'low':\n        return 'text-secondary-600 bg-secondary-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">\n          Welcome back, {user?.first_name}!\n        </h1>\n        <p className=\"mt-1 text-sm text-gray-600\">\n          Here's what's happening with your patients today.\n        </p>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"mobile-grid\">\n        {stats.map((stat) => (\n          <div key={stat.name} className=\"card\">\n            <div className=\"card-body\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <stat.icon className=\"h-6 w-6 text-gray-400\" />\n                </div>\n                <div className=\"ml-4 flex-1\">\n                  <p className=\"text-sm font-medium text-gray-500\">{stat.name}</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">{stat.value}</p>\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <p\n                  className={`text-sm ${\n                    stat.changeType === 'positive'\n                      ? 'text-secondary-600'\n                      : stat.changeType === 'warning'\n                      ? 'text-warning-600'\n                      : 'text-danger-600'\n                  }`}\n                >\n                  {stat.change}\n                </p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Activities */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Recent Activities</h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"space-y-4\">\n              {recentActivities.map((activity) => (\n                <div key={activity.id} className=\"flex items-start space-x-3\">\n                  <div className=\"flex-shrink-0\">\n                    <activity.icon className={`h-5 w-5 ${activity.iconColor}`} />\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm text-gray-900\">{activity.message}</p>\n                    <p className=\"text-xs text-gray-500\">{activity.time}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Upcoming Tasks */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Today's Schedule</h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"space-y-4\">\n              {upcomingTasks.map((task) => (\n                <div key={task.id} className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm text-gray-900\">{task.task}</p>\n                    <p className=\"text-xs text-gray-500\">{task.time}</p>\n                  </div>\n                  <span\n                    className={`badge ${getPriorityColor(task.priority)} capitalize`}\n                  >\n                    {task.priority}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Quick Actions</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"grid grid-cols-2 gap-4 sm:grid-cols-4\">\n            <button className=\"btn-outline flex flex-col items-center p-4\">\n              <UserGroupIcon className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Add Patient</span>\n            </button>\n            <button className=\"btn-outline flex flex-col items-center p-4\">\n              <DocumentTextIcon className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Log Visit</span>\n            </button>\n            <button className=\"btn-outline flex flex-col items-center p-4\">\n              <CubeIcon className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Update Inventory</span>\n            </button>\n            <button className=\"btn-outline flex flex-col items-center p-4\">\n              <CurrencyDollarIcon className=\"h-6 w-6 mb-2\" />\n              <span className=\"text-sm\">Create Invoice</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,aAAa,EACbC,gBAAgB,EAChBC,QAAQ,EACRC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,QACV,6BAA6B;AACpC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAMM,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,cAAc;IACtBC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEjB;EACR,CAAC,EACD;IACEa,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,UAAU;IAClBC,UAAU,EAAE,SAAS;IACrBC,IAAI,EAAEhB;EACR,CAAC,EACD;IACEY,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,gBAAgB;IACxBC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEf;EACR,CAAC,EACD;IACEW,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,WAAW;IACnBC,UAAU,EAAE,UAAU;IACtBC,IAAI,EAAEd;EACR,CAAC,CACF;EAED,MAAMe,gBAAgB,GAAG,CACvB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBC,OAAO,EAAE,uCAAuC;IAChDC,IAAI,EAAE,aAAa;IACnBL,IAAI,EAAEZ,eAAe;IACrBkB,SAAS,EAAE;EACb,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,2CAA2C;IACpDC,IAAI,EAAE,aAAa;IACnBL,IAAI,EAAEb,uBAAuB;IAC7BmB,SAAS,EAAE;EACb,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE,+BAA+B;IACxCC,IAAI,EAAE,aAAa;IACnBL,IAAI,EAAEf,QAAQ;IACdqB,SAAS,EAAE;EACb,CAAC,EACD;IACEJ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,gCAAgC;IACzCC,IAAI,EAAE,WAAW;IACjBL,IAAI,EAAEd,kBAAkB;IACxBoB,SAAS,EAAE;EACb,CAAC,CACF;EAED,MAAMC,aAAa,GAAG,CACpB;IACEL,EAAE,EAAE,CAAC;IACLM,IAAI,EAAE,0CAA0C;IAChDH,IAAI,EAAE,SAAS;IACfI,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLM,IAAI,EAAE,gDAAgD;IACtDH,IAAI,EAAE,UAAU;IAChBI,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLM,IAAI,EAAE,sCAAsC;IAC5CH,IAAI,EAAE,SAAS;IACfI,QAAQ,EAAE;EACZ,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLM,IAAI,EAAE,sCAAsC;IAC5CH,IAAI,EAAE,SAAS;IACfI,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAID,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,MAAM;QACT,OAAO,+BAA+B;MACxC,KAAK,QAAQ;QACX,OAAO,iCAAiC;MAC1C,KAAK,KAAK;QACR,OAAO,qCAAqC;MAC9C;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,oBACElB,OAAA;IAAKoB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBrB,OAAA;MAAAqB,QAAA,gBACErB,OAAA;QAAIoB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,GAAC,gBACjC,EAAClB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,UAAU,EAAC,GAClC;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL1B,OAAA;QAAGoB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN1B,OAAA;MAAKoB,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBjB,KAAK,CAACuB,GAAG,CAAEC,IAAI,iBACd5B,OAAA;QAAqBoB,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnCrB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BrB,OAAA,CAAC4B,IAAI,CAACnB,IAAI;gBAACW,SAAS,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN1B,OAAA;cAAKoB,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrB,OAAA;gBAAGoB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEO,IAAI,CAACvB;cAAI;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChE1B,OAAA;gBAAGoB,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAEO,IAAI,CAACtB;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1B,OAAA;YAAKoB,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBrB,OAAA;cACEoB,SAAS,EAAE,WACTQ,IAAI,CAACpB,UAAU,KAAK,UAAU,GAC1B,oBAAoB,GACpBoB,IAAI,CAACpB,UAAU,KAAK,SAAS,GAC7B,kBAAkB,GAClB,iBAAiB,EACpB;cAAAa,QAAA,EAEFO,IAAI,CAACrB;YAAM;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAxBEE,IAAI,CAACvB,IAAI;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAyBd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN1B,OAAA;MAAKoB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDrB,OAAA;QAAKoB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BrB,OAAA;YAAIoB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACN1B,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBrB,OAAA;YAAKoB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBX,gBAAgB,CAACiB,GAAG,CAAEE,QAAQ,iBAC7B7B,OAAA;cAAuBoB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBAC3DrB,OAAA;gBAAKoB,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BrB,OAAA,CAAC6B,QAAQ,CAACpB,IAAI;kBAACW,SAAS,EAAE,WAAWS,QAAQ,CAACd,SAAS;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACN1B,OAAA;gBAAKoB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BrB,OAAA;kBAAGoB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEQ,QAAQ,CAAChB;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3D1B,OAAA;kBAAGoB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEQ,QAAQ,CAACf;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA,GAPEG,QAAQ,CAAClB,EAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQhB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1B,OAAA;QAAKoB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBrB,OAAA;UAAKoB,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BrB,OAAA;YAAIoB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACN1B,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBrB,OAAA;YAAKoB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBL,aAAa,CAACW,GAAG,CAAEV,IAAI,iBACtBjB,OAAA;cAAmBoB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAC9DrB,OAAA;gBAAKoB,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBrB,OAAA;kBAAGoB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEJ,IAAI,CAACA;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpD1B,OAAA;kBAAGoB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEJ,IAAI,CAACH;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN1B,OAAA;gBACEoB,SAAS,EAAE,SAASD,gBAAgB,CAACF,IAAI,CAACC,QAAQ,CAAC,aAAc;gBAAAG,QAAA,EAEhEJ,IAAI,CAACC;cAAQ;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA,GATCT,IAAI,CAACN,EAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKoB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBrB,OAAA;QAAKoB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BrB,OAAA;UAAIoB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACN1B,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBrB,OAAA;UAAKoB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDrB,OAAA;YAAQoB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC5DrB,OAAA,CAACR,aAAa;cAAC4B,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1C1B,OAAA;cAAMoB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACT1B,OAAA;YAAQoB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC5DrB,OAAA,CAACP,gBAAgB;cAAC2B,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C1B,OAAA;cAAMoB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACT1B,OAAA;YAAQoB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC5DrB,OAAA,CAACN,QAAQ;cAAC0B,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrC1B,OAAA;cAAMoB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACT1B,OAAA;YAAQoB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC5DrB,OAAA,CAACL,kBAAkB;cAACyB,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/C1B,OAAA;cAAMoB,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CAtOID,SAAmB;EAAA,QACNH,OAAO;AAAA;AAAAgC,EAAA,GADpB7B,SAAmB;AAwOzB,eAAeA,SAAS;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}