// Role-Based Access Control (RBAC) System for CareSyncAI
// Defines permissions and access levels for different user roles

export type UserRole = 'admin' | 'supervisor' | 'nurse' | 'caregiver' | 'billing';

export interface Permission {
  read: boolean;
  write: boolean;
  delete: boolean;
  admin: boolean;
}

export interface RolePermissions {
  dashboard: Permission;
  residents: Permission;
  medicalRecords: Permission;
  medications: Permission;
  scheduling: Permission;
  inventory: Permission;
  purchasing: Permission;
  billing: Permission;
  reports: Permission;
  userManagement: Permission;
  systemSettings: Permission;
  auditLogs: Permission;
  hipaaCompliance: Permission;
  faxManagement: Permission;
  documentManagement: Permission;
}

// Define permissions for each role
export const ROLE_PERMISSIONS: Record<UserRole, RolePermissions> = {
  admin: {
    dashboard: { read: true, write: true, delete: true, admin: true },
    residents: { read: true, write: true, delete: true, admin: true },
    medicalRecords: { read: true, write: true, delete: true, admin: true },
    medications: { read: true, write: true, delete: true, admin: true },
    scheduling: { read: true, write: true, delete: true, admin: true },
    inventory: { read: true, write: true, delete: true, admin: true },
    purchasing: { read: true, write: true, delete: true, admin: true },
    billing: { read: true, write: true, delete: true, admin: true },
    reports: { read: true, write: true, delete: true, admin: true },
    userManagement: { read: true, write: true, delete: true, admin: true },
    systemSettings: { read: true, write: true, delete: true, admin: true },
    auditLogs: { read: true, write: true, delete: true, admin: true }, // Full access for admin
    hipaaCompliance: { read: true, write: true, delete: true, admin: true }, // Full access for admin
    faxManagement: { read: true, write: true, delete: true, admin: true },
    documentManagement: { read: true, write: true, delete: true, admin: true },
  },
  supervisor: {
    dashboard: { read: true, write: true, delete: true, admin: false },
    residents: { read: true, write: true, delete: true, admin: false },
    medicalRecords: { read: true, write: true, delete: true, admin: false },
    medications: { read: true, write: true, delete: true, admin: false },
    scheduling: { read: true, write: true, delete: true, admin: false },
    inventory: { read: true, write: true, delete: true, admin: false },
    purchasing: { read: true, write: true, delete: true, admin: false },
    billing: { read: false, write: false, delete: false, admin: false }, // No access to billing
    reports: { read: false, write: false, delete: false, admin: false }, // No access to reports
    userManagement: { read: true, write: true, delete: false, admin: false }, // Can manage staff but not delete users
    systemSettings: { read: false, write: false, delete: false, admin: false }, // No access to system settings
    auditLogs: { read: true, write: true, delete: false, admin: false }, // Can view and add notes to logs
    hipaaCompliance: { read: true, write: true, delete: false, admin: false }, // Can manage compliance
    faxManagement: { read: true, write: true, delete: true, admin: false },
    documentManagement: { read: true, write: true, delete: true, admin: false },
  },
  nurse: {
    dashboard: { read: true, write: true, delete: true, admin: false },
    residents: { read: true, write: true, delete: true, admin: false },
    medicalRecords: { read: true, write: true, delete: true, admin: false },
    medications: { read: true, write: true, delete: true, admin: false },
    scheduling: { read: true, write: true, delete: true, admin: false },
    inventory: { read: true, write: true, delete: true, admin: false },
    purchasing: { read: true, write: true, delete: true, admin: false },
    billing: { read: false, write: false, delete: false, admin: false }, // No access to billing
    reports: { read: false, write: false, delete: false, admin: false }, // No access to reports
    userManagement: { read: true, write: true, delete: false, admin: false },
    systemSettings: { read: false, write: false, delete: false, admin: false }, // No access to system settings
    auditLogs: { read: true, write: true, delete: false, admin: false },
    hipaaCompliance: { read: true, write: true, delete: false, admin: false },
    faxManagement: { read: true, write: true, delete: true, admin: false },
    documentManagement: { read: true, write: true, delete: true, admin: false },
  },
  caregiver: {
    dashboard: { read: true, write: false, delete: false, admin: false },
    residents: { read: true, write: true, delete: false, admin: false }, // Only assigned residents
    medicalRecords: { read: true, write: true, delete: false, admin: false }, // Only assigned residents
    medications: { read: true, write: true, delete: false, admin: false }, // AIMAR system
    scheduling: { read: true, write: true, delete: false, admin: false }, // Own schedule + assigned residents
    inventory: { read: true, write: false, delete: false, admin: false }, // View only
    purchasing: { read: false, write: false, delete: false, admin: false }, // No access
    billing: { read: false, write: false, delete: false, admin: false }, // No access
    reports: { read: true, write: false, delete: false, admin: false }, // Limited reports
    userManagement: { read: false, write: false, delete: false, admin: false }, // No access
    systemSettings: { read: false, write: false, delete: false, admin: false }, // No access
    auditLogs: { read: false, write: false, delete: false, admin: false }, // No access
    hipaaCompliance: { read: true, write: false, delete: false, admin: false }, // View only
    faxManagement: { read: true, write: false, delete: false, admin: false }, // View only
    documentManagement: { read: true, write: true, delete: false, admin: false }, // Limited access
  },
  billing: {
    dashboard: { read: true, write: false, delete: false, admin: false },
    residents: { read: true, write: false, delete: false, admin: false }, // Basic info only
    medicalRecords: { read: false, write: false, delete: false, admin: false }, // No access
    medications: { read: false, write: false, delete: false, admin: false }, // No access
    scheduling: { read: true, write: false, delete: false, admin: false }, // View only for billing
    inventory: { read: true, write: false, delete: false, admin: false }, // View for cost analysis
    purchasing: { read: true, write: false, delete: false, admin: false }, // View for cost analysis
    billing: { read: true, write: true, delete: false, admin: false }, // Full billing access
    reports: { read: true, write: true, delete: false, admin: false }, // Financial reports
    userManagement: { read: false, write: false, delete: false, admin: false }, // No access
    systemSettings: { read: false, write: false, delete: false, admin: false }, // No access
    auditLogs: { read: true, write: false, delete: false, admin: false }, // Billing audit only
    hipaaCompliance: { read: true, write: false, delete: false, admin: false }, // View only
    faxManagement: { read: true, write: true, delete: false, admin: false }, // Billing related faxes
    documentManagement: { read: true, write: true, delete: false, admin: false }, // Billing documents
  },
};

// Navigation items for each role
export const ROLE_NAVIGATION: Record<UserRole, Array<{
  name: string;
  href: string;
  icon: string;
  description: string;
  badge?: string;
}>> = {
  admin: [
    { name: 'Dashboard', href: '/dashboard', icon: 'HomeIcon', description: 'System overview and analytics' },
    { name: 'Residents', href: '/residents', icon: 'UserGroupIcon', description: 'Resident management and care plans' },
    { name: 'Medical Records', href: '/medical-records', icon: 'DocumentTextIcon', description: 'EHR and medical documentation' },
    { name: 'AIMAR System', href: '/medications', icon: 'BeakerIcon', description: 'AI Medication Administration Records', badge: 'AI' },
    { name: 'Scheduling', href: '/scheduling', icon: 'CalendarDaysIcon', description: 'Staff and resident scheduling' },
    { name: 'Inventory', href: '/inventory', icon: 'CubeIcon', description: 'Medical supplies and equipment' },
    { name: 'Purchasing', href: '/purchasing', icon: 'ShoppingCartIcon', description: 'Supply chain management' },
    { name: 'Billing', href: '/billing', icon: 'CurrencyDollarIcon', description: 'Financial management and invoicing' },
    { name: 'Staff Management', href: '/staff', icon: 'UsersIcon', description: 'Employee management and performance' },
    { name: 'Reports', href: '/reports', icon: 'ChartBarIcon', description: 'Analytics and compliance reports' },
    { name: 'Fax Management', href: '/fax', icon: 'DocumentArrowUpIcon', description: 'Digital fax system' },
    { name: 'HIPAA Compliance', href: '/hipaa', icon: 'ShieldCheckIcon', description: 'Privacy and security compliance' },
    { name: 'System Settings', href: '/settings', icon: 'CogIcon', description: 'System configuration and preferences' },
  ],
  supervisor: [
    { name: 'Dashboard', href: '/dashboard', icon: 'HomeIcon', description: 'Operational overview and metrics' },
    { name: 'Residents', href: '/residents', icon: 'UserGroupIcon', description: 'Resident care coordination' },
    { name: 'Medical Records', href: '/medical-records', icon: 'DocumentTextIcon', description: 'Medical documentation review' },
    { name: 'AIMAR System', href: '/medications', icon: 'BeakerIcon', description: 'Medication oversight and compliance', badge: 'AI' },
    { name: 'Scheduling', href: '/scheduling', icon: 'CalendarDaysIcon', description: 'Staff scheduling and assignments' },
    { name: 'Inventory', href: '/inventory', icon: 'CubeIcon', description: 'Supply monitoring and management' },
    { name: 'Purchasing', href: '/purchasing', icon: 'ShoppingCartIcon', description: 'Purchase approvals and procurement' },
    { name: 'Staff Management', href: '/staff', icon: 'UsersIcon', description: 'Team management and performance' },
    { name: 'Documents', href: '/documents', icon: 'DocumentTextIcon', description: 'Document management and templates' },
    { name: 'Fax Management', href: '/fax', icon: 'DocumentArrowUpIcon', description: 'Communication management' },
    { name: 'HIPAA Compliance', href: '/hipaa', icon: 'ShieldCheckIcon', description: 'Regulatory compliance monitoring' },
  ],
  nurse: [
    { name: 'Dashboard', href: '/dashboard', icon: 'HomeIcon', description: 'Clinical overview and patient metrics' },
    { name: 'Residents', href: '/residents', icon: 'UserGroupIcon', description: 'Patient care management' },
    { name: 'Medical Records', href: '/medical-records', icon: 'DocumentTextIcon', description: 'Electronic health records' },
    { name: 'AIMAR System', href: '/medications', icon: 'BeakerIcon', description: 'AI Medication Administration Records', badge: 'AI' },
    { name: 'Scheduling', href: '/scheduling', icon: 'CalendarDaysIcon', description: 'Staff and patient scheduling' },
    { name: 'Inventory', href: '/inventory', icon: 'CubeIcon', description: 'Medical supplies and equipment' },
    { name: 'Purchasing', href: '/purchasing', icon: 'ShoppingCartIcon', description: 'Supply procurement and ordering' },
    { name: 'Staff Management', href: '/staff', icon: 'UsersIcon', description: 'Nursing staff coordination' },
    { name: 'Documents', href: '/documents', icon: 'DocumentTextIcon', description: 'Clinical documentation' },
    { name: 'Fax Management', href: '/fax', icon: 'DocumentArrowUpIcon', description: 'Medical communication' },
    { name: 'HIPAA Compliance', href: '/hipaa', icon: 'ShieldCheckIcon', description: 'Patient privacy compliance' },
  ],
  caregiver: [
    { name: 'My Dashboard', href: '/dashboard', icon: 'HomeIcon', description: 'Your daily overview and tasks' },
    { name: 'My Residents', href: '/residents', icon: 'UserGroupIcon', description: 'Residents under your care' },
    { name: 'Care Records', href: '/medical-records', icon: 'DocumentTextIcon', description: 'Patient care documentation' },
    { name: 'AIMAR', href: '/medications', icon: 'BeakerIcon', description: 'Medication administration tracking', badge: 'AI' },
    { name: 'My Schedule', href: '/scheduling', icon: 'CalendarDaysIcon', description: 'Your work schedule and assignments' },
    { name: 'Supply Requests', href: '/inventory', icon: 'CubeIcon', description: 'Request supplies and equipment' },
    { name: 'Care Reports', href: '/reports', icon: 'ChartBarIcon', description: 'Your care quality metrics' },
    { name: 'Documents', href: '/fax', icon: 'DocumentArrowUpIcon', description: 'Care-related documents' },
    { name: 'Training', href: '/hipaa', icon: 'ShieldCheckIcon', description: 'Compliance training and resources' },
  ],
  billing: [
    { name: 'Billing Dashboard', href: '/dashboard', icon: 'HomeIcon', description: 'Financial overview and metrics' },
    { name: 'Resident Billing', href: '/residents', icon: 'UserGroupIcon', description: 'Resident billing information' },
    { name: 'Billing Management', href: '/billing', icon: 'CurrencyDollarIcon', description: 'Invoice and payment processing' },
    { name: 'Financial Reports', href: '/reports', icon: 'ChartBarIcon', description: 'Revenue and expense analysis' },
    { name: 'Cost Analysis', href: '/inventory', icon: 'CubeIcon', description: 'Supply cost tracking' },
    { name: 'Purchase Orders', href: '/purchasing', icon: 'ShoppingCartIcon', description: 'Procurement cost management' },
    { name: 'Billing Documents', href: '/fax', icon: 'DocumentArrowUpIcon', description: 'Financial document management' },
    { name: 'Compliance', href: '/hipaa', icon: 'ShieldCheckIcon', description: 'Financial compliance requirements' },
  ],
};

// Utility functions
export const hasPermission = (userRole: UserRole, module: keyof RolePermissions, action: keyof Permission): boolean => {
  return ROLE_PERMISSIONS[userRole]?.[module]?.[action] || false;
};

export const canAccessModule = (userRole: UserRole, module: keyof RolePermissions): boolean => {
  return ROLE_PERMISSIONS[userRole]?.[module]?.read || false;
};

export const isAdmin = (userRole: UserRole): boolean => {
  return userRole === 'admin';
};

export const isSupervisor = (userRole: UserRole): boolean => {
  return userRole === 'supervisor' || userRole === 'admin';
};

export const getNavigationForRole = (userRole: UserRole) => {
  return ROLE_NAVIGATION[userRole] || [];
};

export const getRestrictedMessage = (userRole: UserRole, module: string): string => {
  const messages = {
    admin: 'You have full access to all system features.',
    supervisor: `As a supervisor, you have limited access to ${module}. Contact an administrator for full access.`,
    nurse: `As a nurse, you have comprehensive clinical access to ${module}. Contact an administrator for system-level access.`,
    caregiver: `As a caregiver, you can only access ${module} features related to your assigned residents. Contact your supervisor for additional access.`,
    billing: `As billing staff, you have access to financial aspects of ${module}. Contact an administrator for clinical access.`,
  };
  
  return messages[userRole] || 'Access restricted. Contact your administrator.';
};
