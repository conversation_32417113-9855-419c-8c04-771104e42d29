#!/bin/bash

# CareSyncAI Setup Script
# This script sets up the development environment for CareSyncAI

set -e

echo "🏥 CareSyncAI Development Setup"
echo "==============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if running on Windows (Git Bash/WSL)
check_environment() {
    log_step "Checking environment..."
    
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        log_info "Detected Windows environment"
        PLATFORM="windows"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_info "Detected Linux environment"
        PLATFORM="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        log_info "Detected macOS environment"
        PLATFORM="macos"
    else
        log_warn "Unknown platform: $OSTYPE"
        PLATFORM="unknown"
    fi
}

# Install dependencies
install_dependencies() {
    log_step "Installing dependencies..."
    
    # Check for Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    else
        NODE_VERSION=$(node --version)
        log_info "Node.js version: $NODE_VERSION"
    fi
    
    # Check for Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed. Please install Python 3.11+ from https://python.org/"
        exit 1
    else
        PYTHON_VERSION=$(python3 --version)
        log_info "Python version: $PYTHON_VERSION"
    fi
    
    # Check for Docker
    if ! command -v docker &> /dev/null; then
        log_warn "Docker is not installed. Please install Docker Desktop for development"
        log_info "Download from: https://www.docker.com/products/docker-desktop"
    else
        DOCKER_VERSION=$(docker --version)
        log_info "Docker version: $DOCKER_VERSION"
    fi
    
    # Check for Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_warn "Docker Compose is not installed"
    else
        COMPOSE_VERSION=$(docker-compose --version)
        log_info "Docker Compose version: $COMPOSE_VERSION"
    fi
}

# Setup environment files
setup_environment() {
    log_step "Setting up environment files..."
    
    # Create main .env file
    if [ ! -f ".env" ]; then
        log_info "Creating .env file from template..."
        cp .env.example .env
        log_warn "Please edit .env file with your actual configuration values"
    else
        log_info ".env file already exists"
    fi
    
    # Create frontend .env file
    if [ ! -f "frontend/.env" ]; then
        log_info "Creating frontend/.env file..."
        cp frontend/.env.example frontend/.env
    else
        log_info "frontend/.env file already exists"
    fi
}

# Setup backend
setup_backend() {
    log_step "Setting up backend..."
    
    cd backend
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    if [[ "$PLATFORM" == "windows" ]]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    
    # Install Python dependencies
    log_info "Installing Python dependencies..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    cd ..
    log_info "Backend setup completed ✅"
}

# Setup frontend
setup_frontend() {
    log_step "Setting up frontend..."
    
    cd frontend
    
    # Install Node.js dependencies
    log_info "Installing Node.js dependencies..."
    npm install
    
    cd ..
    log_info "Frontend setup completed ✅"
}

# Setup AI service
setup_ai() {
    log_step "Setting up AI service..."
    
    cd ai
    
    # Create virtual environment for AI
    if [ ! -d "venv" ]; then
        log_info "Creating AI Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    if [[ "$PLATFORM" == "windows" ]]; then
        source venv/Scripts/activate
    else
        source venv/bin/activate
    fi
    
    # Install AI dependencies
    log_info "Installing AI/ML dependencies..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    # Download NLTK data
    log_info "Downloading NLTK data..."
    python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('averaged_perceptron_tagger'); nltk.download('maxent_ne_chunker'); nltk.download('words')"
    
    cd ..
    log_info "AI service setup completed ✅"
}

# Setup database
setup_database() {
    log_step "Setting up database..."
    
    log_info "Database setup instructions:"
    echo "1. Create a Supabase project at https://supabase.com"
    echo "2. Run the SQL scripts in supabase/migrations/ in your Supabase SQL editor"
    echo "3. Run the seed data script: supabase/seed.sql"
    echo "4. Update your .env file with Supabase credentials"
    echo ""
    log_info "Database setup requires manual configuration ⚠️"
}

# Create necessary directories
create_directories() {
    log_step "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p backups
    mkdir -p ai/models
    
    log_info "Directories created ✅"
}

# Display next steps
show_next_steps() {
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Configure your .env files with actual values"
    echo "2. Set up Supabase database (see instructions above)"
    echo "3. Configure Zoho Books API credentials"
    echo "4. Start development servers:"
    echo ""
    echo "   Development mode:"
    echo "   - Backend: cd backend && source venv/bin/activate && python main.py"
    echo "   - Frontend: cd frontend && npm start"
    echo "   - AI Service: cd ai && source venv/bin/activate && python ai_api.py"
    echo ""
    echo "   Docker mode:"
    echo "   - All services: docker-compose up -d"
    echo ""
    echo "5. Access the application:"
    echo "   - Frontend: http://localhost:3000"
    echo "   - Backend API: http://localhost:8000/docs"
    echo "   - AI Service: http://localhost:8001/docs"
    echo ""
    echo "For more information, see README.md"
}

# Main setup process
main() {
    check_environment
    install_dependencies
    setup_environment
    create_directories
    setup_backend
    setup_frontend
    setup_ai
    setup_database
    show_next_steps
}

# Run main function
main "$@"
