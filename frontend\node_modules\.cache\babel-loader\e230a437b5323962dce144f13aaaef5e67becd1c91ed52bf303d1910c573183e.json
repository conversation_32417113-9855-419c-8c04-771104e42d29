{"ast": null, "code": "\"use client\";\n\n// src/useMutationState.ts\nimport * as React from \"react\";\nimport { notify<PERSON>anager, replaceEqualDeep } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nfunction useIsMutating(filters, queryClient) {\n  const client = useQueryClient(queryClient);\n  return useMutationState({\n    filters: {\n      ...filters,\n      status: \"pending\"\n    }\n  }, client).length;\n}\nfunction getResult(mutationCache, options) {\n  return mutationCache.findAll(options.filters).map(mutation => options.select ? options.select(mutation) : mutation.state);\n}\nfunction useMutationState(options = {}, queryClient) {\n  const mutationCache = useQueryClient(queryClient).getMutationCache();\n  const optionsRef = React.useRef(options);\n  const result = React.useRef(null);\n  if (!result.current) {\n    result.current = getResult(mutationCache, options);\n  }\n  React.useEffect(() => {\n    optionsRef.current = options;\n  });\n  return React.useSyncExternalStore(React.useCallback(onStoreChange => mutationCache.subscribe(() => {\n    const nextResult = replaceEqualDeep(result.current, getResult(mutationCache, optionsRef.current));\n    if (result.current !== nextResult) {\n      result.current = nextResult;\n      notifyManager.schedule(onStoreChange);\n    }\n  }), [mutationCache]), () => result.current, () => result.current);\n}\nexport { useIsMutating, useMutationState };", "map": {"version": 3, "names": ["React", "notify<PERSON><PERSON>ger", "replaceEqualDeep", "useQueryClient", "useIsMutating", "filters", "queryClient", "client", "useMutationState", "status", "length", "getResult", "mutationCache", "options", "findAll", "map", "mutation", "select", "state", "getMutationCache", "optionsRef", "useRef", "result", "current", "useEffect", "useSyncExternalStore", "useCallback", "onStoreChange", "subscribe", "nextResult", "schedule"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@tanstack\\react-query\\src\\useMutationState.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport { notify<PERSON><PERSON><PERSON>, replaceEqual<PERSON>eep } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  Mutation,\n  MutationCache,\n  MutationFilters,\n  MutationState,\n  QueryClient,\n} from '@tanstack/query-core'\n\nexport function useIsMutating(\n  filters?: MutationFilters,\n  queryClient?: QueryClient,\n): number {\n  const client = useQueryClient(queryClient)\n  return useMutationState(\n    { filters: { ...filters, status: 'pending' } },\n    client,\n  ).length\n}\n\ntype MutationStateOptions<TResult = MutationState> = {\n  filters?: MutationFilters\n  select?: (mutation: Mutation) => TResult\n}\n\nfunction getResult<TResult = MutationState>(\n  mutationCache: MutationCache,\n  options: MutationStateOptions<TResult>,\n): Array<TResult> {\n  return mutationCache\n    .findAll(options.filters)\n    .map(\n      (mutation): TResult =>\n        (options.select ? options.select(mutation) : mutation.state) as TResult,\n    )\n}\n\nexport function useMutationState<TResult = MutationState>(\n  options: MutationStateOptions<TResult> = {},\n  queryClient?: QueryClient,\n): Array<TResult> {\n  const mutationCache = useQueryClient(queryClient).getMutationCache()\n  const optionsRef = React.useRef(options)\n  const result = React.useRef<Array<TResult>>(null)\n  if (!result.current) {\n    result.current = getResult(mutationCache, options)\n  }\n\n  React.useEffect(() => {\n    optionsRef.current = options\n  })\n\n  return React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        mutationCache.subscribe(() => {\n          const nextResult = replaceEqualDeep(\n            result.current,\n            getResult(mutationCache, optionsRef.current),\n          )\n          if (result.current !== nextResult) {\n            result.current = nextResult\n            notifyManager.schedule(onStoreChange)\n          }\n        }),\n      [mutationCache],\n    ),\n    () => result.current,\n    () => result.current,\n  )!\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AAEvB,SAASC,aAAA,EAAeC,gBAAA,QAAwB;AAChD,SAASC,cAAA,QAAsB;AASxB,SAASC,cACdC,OAAA,EACAC,WAAA,EACQ;EACR,MAAMC,MAAA,GAASJ,cAAA,CAAeG,WAAW;EACzC,OAAOE,gBAAA,CACL;IAAEH,OAAA,EAAS;MAAE,GAAGA,OAAA;MAASI,MAAA,EAAQ;IAAU;EAAE,GAC7CF,MACF,EAAEG,MAAA;AACJ;AAOA,SAASC,UACPC,aAAA,EACAC,OAAA,EACgB;EAChB,OAAOD,aAAA,CACJE,OAAA,CAAQD,OAAA,CAAQR,OAAO,EACvBU,GAAA,CACEC,QAAA,IACEH,OAAA,CAAQI,MAAA,GAASJ,OAAA,CAAQI,MAAA,CAAOD,QAAQ,IAAIA,QAAA,CAASE,KAC1D;AACJ;AAEO,SAASV,iBACdK,OAAA,GAAyC,CAAC,GAC1CP,WAAA,EACgB;EAChB,MAAMM,aAAA,GAAgBT,cAAA,CAAeG,WAAW,EAAEa,gBAAA,CAAiB;EACnE,MAAMC,UAAA,GAAmBpB,KAAA,CAAAqB,MAAA,CAAOR,OAAO;EACvC,MAAMS,MAAA,GAAetB,KAAA,CAAAqB,MAAA,CAAuB,IAAI;EAChD,IAAI,CAACC,MAAA,CAAOC,OAAA,EAAS;IACnBD,MAAA,CAAOC,OAAA,GAAUZ,SAAA,CAAUC,aAAA,EAAeC,OAAO;EACnD;EAEMb,KAAA,CAAAwB,SAAA,CAAU,MAAM;IACpBJ,UAAA,CAAWG,OAAA,GAAUV,OAAA;EACvB,CAAC;EAED,OAAab,KAAA,CAAAyB,oBAAA,CACLzB,KAAA,CAAA0B,WAAA,CACHC,aAAA,IACCf,aAAA,CAAcgB,SAAA,CAAU,MAAM;IAC5B,MAAMC,UAAA,GAAa3B,gBAAA,CACjBoB,MAAA,CAAOC,OAAA,EACPZ,SAAA,CAAUC,aAAA,EAAeQ,UAAA,CAAWG,OAAO,CAC7C;IACA,IAAID,MAAA,CAAOC,OAAA,KAAYM,UAAA,EAAY;MACjCP,MAAA,CAAOC,OAAA,GAAUM,UAAA;MACjB5B,aAAA,CAAc6B,QAAA,CAASH,aAAa;IACtC;EACF,CAAC,GACH,CAACf,aAAa,CAChB,GACA,MAAMU,MAAA,CAAOC,OAAA,EACb,MAAMD,MAAA,CAAOC,OACf;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}