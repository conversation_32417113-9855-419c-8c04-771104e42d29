# 🏥 Care-SolAI Backend Documentation

## 📋 **System Overview**

### **🎯 Care-SolAI Backend Architecture**
The Care-SolAI backend is a comprehensive FastAPI-based system designed for residential healthcare management. It provides secure, HIPAA-compliant APIs for managing patients, caregivers, medical records, billing, and AI-powered healthcare analytics.

### **🏗️ Technology Stack**
- **Framework**: FastAPI 0.104.1 (Python 3.9+)
- **Database**: Supabase (PostgreSQL) with real-time capabilities
- **Authentication**: JWT with Supabase Auth integration
- **AI/ML**: TensorFlow 2.15.0, Transformers 4.36.0, Scikit-learn
- **External APIs**: Zoho Books, AWS S3, Hugging Face
- **Deployment**: Docker containers, AWS Lambda ready
- **Security**: HIPAA-compliant with encryption and audit trails

## 📁 **Project Structure**

```
backend/
├── core/                    # Core configuration and utilities
│   ├── config.py           # Application settings and environment variables
│   ├── database.py         # Supabase connection and utilities
│   └── auth.py             # Authentication and authorization
├── routers/                 # API endpoint definitions
│   ├── auth.py             # Authentication endpoints (/api/auth)
│   ├── patients.py         # Patient management (/api/patients)
│   ├── medical_records.py  # Medical records (/api/medical-records)
│   ├── records.py          # Document management (/api/records)
│   ├── inventory.py        # Inventory management (/api/inventory)
│   ├── billing.py          # Billing and finance (/api/billing)
│   ├── caregivers.py       # Caregiver management (/api/caregivers)
│   └── ai_predictions.py   # AI/ML predictions (/api/ai)
├── services/                # Business logic layer
│   ├── patient_service.py  # Patient-related business logic
│   ├── billing_service.py  # Billing and Zoho Books integration
│   ├── ai_service.py       # AI/ML service integration
│   └── zoho_service.py     # Zoho Books API wrapper
├── schemas/                 # Pydantic models for request/response
│   └── patient.py          # Patient data models
├── middleware/              # Custom middleware
│   ├── logging.py          # Request/response logging
│   └── rate_limiting.py    # API rate limiting
├── models/                  # Database models (if using ORM)
├── utils/                   # Utility functions
│   └── pagination.py       # Pagination helpers
├── main.py                  # FastAPI application entry point
├── requirements.txt         # Python dependencies
├── Dockerfile              # Docker container configuration
└── wireless_signing_server.py # Mobile signing server
```

## 🔐 **Authentication & Authorization**

### **JWT Token-Based Authentication**
```python
# Authentication flow:
POST /api/auth/login          # User login
POST /api/auth/register       # User registration
POST /api/auth/logout         # User logout
GET  /api/auth/me            # Get current user profile
PUT  /api/auth/me            # Update user profile
POST /api/auth/change-password # Change password
POST /api/auth/reset-password  # Password reset
POST /api/auth/refresh        # Refresh token
```

### **Role-Based Access Control**
- **Admin**: Full system access, user management, system configuration
- **Nurse**: Patient care, medical records, medication administration
- **Caregiver**: Basic patient care, daily activities, observations
- **Billing**: Financial records, insurance, billing management

### **Supabase Auth Integration**
```python
# Core authentication service
class AuthService:
    async def authenticate_user(email: str, password: str)
    async def create_user(user_data: dict)
    async def get_user_profile(user_id: str)
    async def update_user_profile(user_id: str, data: dict)
```

## 🏥 **API Endpoints Overview**

### **🔐 Authentication API (`/api/auth`)**
```python
POST   /api/auth/login              # User authentication
POST   /api/auth/register           # User registration
POST   /api/auth/logout             # User logout
GET    /api/auth/me                 # Get current user
PUT    /api/auth/me                 # Update user profile
POST   /api/auth/change-password    # Change password
POST   /api/auth/reset-password     # Reset password
POST   /api/auth/refresh            # Refresh access token
```

### **👥 Caregivers API (`/api/caregivers`)**
```python
GET    /api/caregivers              # List all caregivers
POST   /api/caregivers              # Create new caregiver
GET    /api/caregivers/{id}         # Get caregiver details
PUT    /api/caregivers/{id}         # Update caregiver
DELETE /api/caregivers/{id}         # Delete caregiver
GET    /api/caregivers/{id}/patients # Get caregiver's patients
POST   /api/caregivers/{id}/schedule # Update caregiver schedule
```

### **🏥 Patients API (`/api/patients`)**
```python
GET    /api/patients                # List all patients
POST   /api/patients                # Create new patient
GET    /api/patients/{id}           # Get patient details
PUT    /api/patients/{id}           # Update patient
DELETE /api/patients/{id}           # Delete patient
GET    /api/patients/{id}/records   # Get patient medical records
POST   /api/patients/{id}/admission # Patient admission
POST   /api/patients/{id}/discharge # Patient discharge
GET    /api/patients/search         # Search patients
```

### **📋 Medical Records API (`/api/medical-records`)**
```python
GET    /api/medical-records         # List medical records
POST   /api/medical-records         # Create medical record
GET    /api/medical-records/{id}    # Get record details
PUT    /api/medical-records/{id}    # Update record
DELETE /api/medical-records/{id}    # Delete record
GET    /api/medical-records/patient/{patient_id} # Patient records
POST   /api/medical-records/medication # Medication administration
GET    /api/medical-records/vitals  # Vital signs records
```

### **📄 Document Management API (`/api/records`)**
```python
POST   /api/records/upload          # Upload document
GET    /api/records                 # List documents
GET    /api/records/{id}            # Get document details
PUT    /api/records/{id}            # Update document metadata
DELETE /api/records/{id}            # Delete document
GET    /api/records/download/{id}   # Download document
POST   /api/records/{id}/share      # Share document
POST   /api/records/scan            # OCR document scanning
POST   /api/records/e-sign          # E-signature workflow
POST   /api/records/fax             # Fax document
```

### **📦 Inventory API (`/api/inventory`)**
```python
GET    /api/inventory               # List inventory items
POST   /api/inventory               # Create inventory item
GET    /api/inventory/{id}          # Get item details
PUT    /api/inventory/{id}          # Update item
DELETE /api/inventory/{id}          # Delete item
POST   /api/inventory/order         # Create purchase order
GET    /api/inventory/low-stock     # Get low stock items
POST   /api/inventory/restock       # Restock items
```

### **💰 Billing API (`/api/billing`)**
```python
GET    /api/billing/invoices        # List invoices
POST   /api/billing/invoices        # Create invoice
GET    /api/billing/invoices/{id}   # Get invoice details
PUT    /api/billing/invoices/{id}   # Update invoice
POST   /api/billing/payments        # Record payment
GET    /api/billing/reports         # Financial reports
POST   /api/billing/insurance       # Insurance claims
GET    /api/billing/zoho/sync       # Sync with Zoho Books
```

### **🤖 AI Predictions API (`/api/ai`)**
```python
POST   /api/ai/fall-risk            # Fall risk prediction
POST   /api/ai/health-trends        # Health trend analysis
POST   /api/ai/medication-review    # Medication interaction check
POST   /api/ai/care-plan            # AI-generated care plans
GET    /api/ai/models               # Available AI models
POST   /api/ai/predict              # General prediction endpoint
GET    /api/ai/analytics            # Healthcare analytics
```

## 🗄️ **Database Schema (Supabase)**

### **Core Tables**
```sql
-- Caregivers table
caregivers (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  role VARCHAR(50) NOT NULL,
  phone VARCHAR(20),
  license_number VARCHAR(50),
  license_expiry DATE,
  is_active BOOLEAN DEFAULT true,
  hire_date DATE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
)

-- Patients table
patients (
  id UUID PRIMARY KEY,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  date_of_birth DATE NOT NULL,
  gender VARCHAR(10),
  ssn_encrypted TEXT, -- Encrypted SSN
  address JSONB,
  phone VARCHAR(20),
  emergency_contact JSONB,
  insurance_info JSONB,
  admission_date DATE,
  discharge_date DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
)

-- Medical Records table
medical_records (
  id UUID PRIMARY KEY,
  patient_id UUID REFERENCES patients(id),
  caregiver_id UUID REFERENCES caregivers(id),
  record_type VARCHAR(50) NOT NULL,
  title VARCHAR(200) NOT NULL,
  content TEXT,
  metadata JSONB,
  is_confidential BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
)

-- Documents table
records (
  id UUID PRIMARY KEY,
  patient_id UUID REFERENCES patients(id),
  caregiver_id UUID REFERENCES caregivers(id),
  filename VARCHAR(255) NOT NULL,
  file_type VARCHAR(50),
  file_size INTEGER,
  storage_path TEXT,
  document_type VARCHAR(50),
  metadata JSONB,
  is_signed BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
)

-- Inventory table
shop_inventory (
  id UUID PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  category VARCHAR(100),
  description TEXT,
  quantity INTEGER DEFAULT 0,
  unit_price DECIMAL(10,2),
  supplier VARCHAR(200),
  reorder_level INTEGER DEFAULT 10,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
)

-- Billing table
billing (
  id UUID PRIMARY KEY,
  patient_id UUID REFERENCES patients(id),
  invoice_number VARCHAR(50) UNIQUE,
  amount DECIMAL(10,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  due_date DATE,
  paid_date DATE,
  payment_method VARCHAR(50),
  zoho_invoice_id VARCHAR(100),
  metadata JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
)
```

## 🔧 **Configuration & Environment**

### **Environment Variables (.env)**
```env
# Application Settings
APP_NAME=Care-SolAI
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# Security
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Zoho Books Integration
ZOHO_CLIENT_ID=your-zoho-client-id
ZOHO_CLIENT_SECRET=your-zoho-client-secret
ZOHO_REFRESH_TOKEN=your-zoho-refresh-token
ZOHO_ORGANIZATION_ID=your-zoho-org-id
ZOHO_BASE_URL=https://www.zohoapis.com/books/v3

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=care-solai-documents

# AI/ML Configuration
HUGGINGFACE_API_KEY=your-huggingface-key
OPENAI_API_KEY=your-openai-key
REPLICATE_API_TOKEN=your-replicate-token

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000","https://your-domain.com"]

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Upload
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_FILE_TYPES=["application/pdf","image/jpeg","image/png"]

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Monitoring
PROMETHEUS_PORT=9090
LOG_LEVEL=INFO
LOG_FORMAT=json

# Cache Settings
REDIS_URL=redis://localhost:6379
CACHE_TTL=300

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
```

## 🛡️ **Security & HIPAA Compliance**

### **Data Encryption**
- **At Rest**: All sensitive data encrypted in Supabase
- **In Transit**: TLS 1.3 for all API communications
- **PHI Protection**: Special handling for Protected Health Information

### **Audit Logging**
```python
class AuditLogger:
    async def log_data_access(user_id: str, patient_id: str, action: str)
    async def log_data_modification(user_id: str, resource: str, changes: dict)
    async def generate_audit_report(date_range: dict) -> dict
```

### **Access Controls**
- **Role-Based Permissions**: Granular access control
- **API Rate Limiting**: Prevent abuse and DoS attacks
- **Token Expiration**: Short-lived JWT tokens
- **Session Management**: Secure session handling

## 🚀 **Deployment & Scaling**

### **Docker Configuration**
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### **AWS Lambda Deployment**
- **Serverless**: Ready for AWS Lambda deployment
- **Auto-scaling**: Handles variable loads automatically
- **Cost-effective**: Pay-per-request pricing model

## 📊 **Monitoring & Analytics**

### **Health Checks**
```python
GET /health              # Basic health check
GET /health/detailed     # Detailed system status
GET /metrics            # Prometheus metrics
```

### **Logging & Monitoring**
- **Structured Logging**: JSON-formatted logs
- **Prometheus Metrics**: Performance monitoring
- **Error Tracking**: Comprehensive error reporting
- **Audit Trails**: HIPAA-compliant audit logging

## 🔄 **API Versioning & Documentation**

### **Interactive Documentation**
- **Swagger UI**: Available at `/docs` (development)
- **ReDoc**: Available at `/redoc` (development)
- **OpenAPI Schema**: Auto-generated API documentation

### **API Versioning**
- **Current Version**: v1.0.0
- **Backward Compatibility**: Maintained across versions
- **Deprecation Policy**: 6-month notice for breaking changes

This documentation provides a comprehensive overview of the Care-SolAI backend system. The next section will include a detailed setup guide for implementing this backend.
