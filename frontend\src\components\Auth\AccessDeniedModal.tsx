import React from 'react';
import {
  XMarkIcon,
  ShieldExclamationIcon,
  UserIcon,
  ExclamationTriangleIcon,
  LockClosedIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import { UserRole, getRestrictedMessage } from '../../utils/roleBasedAccess';

interface AccessDeniedModalProps {
  isOpen: boolean;
  onClose: () => void;
  userRole: UserRole;
  module: string;
  requiredAction?: string;
}

const AccessDeniedModal: React.FC<AccessDeniedModalProps> = ({
  isOpen,
  onClose,
  userRole,
  module,
  requiredAction,
}) => {
  if (!isOpen) return null;

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'from-red-500 to-pink-600';
      case 'supervisor':
        return 'from-purple-500 to-blue-600';
      case 'caregiver':
        return 'from-green-500 to-emerald-600';
      case 'billing':
        return 'from-amber-500 to-yellow-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getRoleTitle = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'System Administrator';
      case 'supervisor':
        return 'Care Supervisor';
      case 'caregiver':
        return 'Care Provider';
      case 'billing':
        return 'Billing Specialist';
      default:
        return 'User';
    }
  };

  const getModuleTitle = (module: string) => {
    const titles: Record<string, string> = {
      purchasing: 'Purchasing & Supply Management',
      billing: 'Billing & Financial Management',
      userManagement: 'Staff Management',
      systemSettings: 'System Settings',
      auditLogs: 'Audit Logs',
      inventory: 'Inventory Management',
      reports: 'Advanced Reports',
      faxManagement: 'Fax Management',
      hipaaCompliance: 'HIPAA Compliance',
    };
    return titles[module] || module;
  };

  const getAlternativeActions = (role: UserRole, module: string) => {
    const alternatives: Record<string, Record<UserRole, string[]>> = {
      purchasing: {
        caregiver: ['Request supplies through "Supply Requests"', 'Contact your supervisor for urgent needs'],
        billing: ['View cost analysis in "Financial Reports"', 'Contact purchasing department'],
        supervisor: ['Submit purchase requests for approval', 'Review pending purchase orders'],
        nurse: ['Submit purchase requests for approval', 'Review pending purchase orders'],
        admin: [],
      },
      billing: {
        caregiver: ['Focus on patient care documentation', 'Contact billing department for patient questions'],
        supervisor: ['View operational cost summaries', 'Contact billing department for detailed reports'],
        nurse: ['View operational cost summaries', 'Contact billing department for detailed reports'],
        admin: [],
        billing: [],
      },
      userManagement: {
        caregiver: ['View staff contact information in directory', 'Contact your supervisor for staff issues'],
        billing: ['Contact HR for staff-related questions', 'Use staff directory for contact information'],
        supervisor: ['Submit staff performance reports', 'Contact HR for personnel matters'],
        nurse: ['Submit staff performance reports', 'Contact HR for personnel matters'],
        admin: [],
      },
    };

    return alternatives[module]?.[role] || ['Contact your supervisor for assistance', 'Use available features in your role'];
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-gradient-to-br from-purple-900/75 via-blue-900/75 to-indigo-900/75 backdrop-blur-sm transition-opacity duration-300" />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-2xl transform overflow-hidden rounded-3xl bg-white shadow-2xl transition-all duration-300 border-4 border-purple-200">
          {/* Animated Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-red-50 via-pink-50 to-red-50"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-red-400/20 to-pink-500/20 rounded-full animate-pulse"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-red-300/20 to-pink-400/20 rounded-full animate-bounce"></div>
          
          <div className="relative z-10">
            {/* Header */}
            <div className="bg-gradient-to-r from-red-500 to-pink-600 px-8 py-6 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="p-3 bg-white/20 rounded-2xl mr-4">
                    <ShieldExclamationIcon className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-black">Access Restricted</h3>
                    <p className="text-red-100 text-sm font-medium">Professional Access Control</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-white/20 rounded-xl transition-colors duration-200"
                >
                  <XMarkIcon className="h-6 w-6 text-white" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="px-8 py-6 space-y-6">
              {/* Main Message */}
              <div className="text-center">
                <h4 className="text-xl font-bold text-gray-900 mb-2">
                  {getModuleTitle(module)} Access Required
                </h4>
                <p className="text-gray-600 leading-relaxed">
                  Your current role doesn't include access to this {module} {requiredAction ? `(${requiredAction})` : 'module'}. 
                  This restriction ensures patient safety, regulatory compliance, and operational security.
                </p>
              </div>

              {/* Role Information */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border-2 border-blue-200">
                <div className="flex items-center mb-4">
                  <div className={`p-3 bg-gradient-to-r ${getRoleColor(userRole)} rounded-2xl shadow-lg mr-4`}>
                    <UserIcon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">Your Current Role</div>
                    <div className={`text-sm font-medium bg-gradient-to-r ${getRoleColor(userRole)} bg-clip-text text-transparent`}>
                      {getRoleTitle(userRole)}
                    </div>
                  </div>
                </div>
                
                <div className="text-sm text-gray-700 leading-relaxed">
                  {getRestrictedMessage(userRole, module)}
                </div>
              </div>

              {/* Alternative Actions */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border-2 border-green-200">
                <div className="flex items-center mb-4">
                  <InformationCircleIcon className="h-6 w-6 text-green-600 mr-3" />
                  <h5 className="text-lg font-bold text-green-900">What You Can Do Instead</h5>
                </div>
                <ul className="space-y-2">
                  {getAlternativeActions(userRole, module).map((action, index) => (
                    <li key={index} className="flex items-start">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-sm text-green-800 font-medium">{action}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Security Notice */}
              <div className="bg-gradient-to-r from-amber-50 to-yellow-50 rounded-2xl p-4 border-2 border-amber-200">
                <div className="flex items-center">
                  <LockClosedIcon className="h-5 w-5 text-amber-600 mr-3" />
                  <div className="text-sm text-amber-800">
                    <span className="font-bold">Security Notice:</span> This restriction protects patient privacy, 
                    ensures HIPAA compliance, and maintains professional boundaries in healthcare operations.
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-8 py-6 border-t border-gray-200">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={onClose}
                  className="group relative overflow-hidden px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold rounded-2xl hover:from-purple-500 hover:to-blue-500 shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-purple-400"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">Continue Working</div>
                </button>
                
                <button
                  onClick={() => window.location.href = '/dashboard'}
                  className="group relative overflow-hidden px-8 py-3 bg-gradient-to-r from-amber-500 to-yellow-600 text-purple-900 font-bold rounded-2xl hover:from-yellow-400 hover:to-amber-400 shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-yellow-400"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">Return to Dashboard</div>
                </button>
              </div>

              {/* Contact Information */}
              <div className="mt-4 text-center">
                <div className="flex items-center justify-center text-gray-600 text-sm">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                  <span>Need additional access? Contact your supervisor or system administrator</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccessDeniedModal;
