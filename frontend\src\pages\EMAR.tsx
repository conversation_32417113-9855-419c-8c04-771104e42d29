import React, { useState } from 'react';
import {
  ClockI<PERSON>,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  BeakerIcon,
  UserIcon,
  ChatBubbleLeftEllipsisIcon,
  ExclamationCircleIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

interface Medication {
  id: string;
  residentName: string;
  residentId: string;
  medicationName: string;
  dosage: string;
  route: string;
  frequency: string;
  scheduledTime: string;
  status: 'pending' | 'administered' | 'missed' | 'refused';
  aiAlerts?: string[];
  interactions?: string[];
  refusedReason?: RefusedReason;
  missedReason?: MissedReason;
  administeredBy?: string;
  timestamp?: string;
}

interface RefusedReason {
  category: 'client_refused' | 'medication_unavailable' | 'medical_contraindication' | 'other';
  subcategory?: string;
  customReason?: string;
  staffMember: string;
  timestamp: string;
  aiSuggestions?: string[];
}

interface MissedReason {
  category: 'medication_unavailable' | 'resident_unavailable' | 'staff_oversight' | 'medical_hold' | 'other';
  subcategory?: string;
  customReason?: string;
  staffMember: string;
  timestamp: string;
  aiSuggestions?: string[];
}

interface ReasonCategory {
  label: string;
  subcategories: string[];
}

type RefusedReasonCategories = {
  client_refused: ReasonCategory;
  medication_unavailable: ReasonCategory;
  medical_contraindication: ReasonCategory;
  other: ReasonCategory;
};

type MissedReasonCategories = {
  medication_unavailable: ReasonCategory;
  resident_unavailable: ReasonCategory;
  staff_oversight: ReasonCategory;
  medical_hold: ReasonCategory;
  other: ReasonCategory;
};

const EMAR: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedShift, setSelectedShift] = useState('morning');

  // Reason tracking states
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonType, setReasonType] = useState<'refused' | 'missed' | null>(null);
  const [selectedMedication, setSelectedMedication] = useState<Medication | null>(null);
  const [reasonCategory, setReasonCategory] = useState('');
  const [reasonSubcategory, setReasonSubcategory] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [medications, setMedications] = useState<Medication[]>([]);

  // Initialize mock medication data
  React.useEffect(() => {
    setMedications([
    {
      id: '1',
      residentName: 'Margaret Thompson',
      residentId: 'R001',
      medicationName: 'Metformin',
      dosage: '500mg',
      route: 'Oral',
      frequency: 'Twice daily',
      scheduledTime: '08:00',
      status: 'administered',
      aiAlerts: [],
    },
    {
      id: '2',
      residentName: 'William Anderson',
      residentId: 'R002',
      medicationName: 'Lisinopril',
      dosage: '10mg',
      route: 'Oral',
      frequency: 'Once daily',
      scheduledTime: '09:00',
      status: 'pending',
      aiAlerts: ['Blood pressure check recommended before administration'],
      interactions: [],
    },
    {
      id: '3',
      residentName: 'Dorothy Garcia',
      residentId: 'R003',
      medicationName: 'Warfarin',
      dosage: '5mg',
      route: 'Oral',
      frequency: 'Once daily',
      scheduledTime: '09:30',
      status: 'pending',
      aiAlerts: ['INR levels due for check', 'Potential interaction with new supplement'],
      interactions: ['Vitamin K supplement - monitor INR closely'],
    },
    {
      id: '4',
      residentName: 'Robert Davis',
      residentId: 'R004',
      medicationName: 'Insulin Glargine',
      dosage: '20 units',
      route: 'Subcutaneous',
      frequency: 'Once daily',
      scheduledTime: '10:00',
      status: 'pending',
      aiAlerts: ['Blood glucose trending high - consider dose adjustment'],
    },
    ]);
  }, []);

  // AI-enhanced reason categories
  const refusedReasonCategories: RefusedReasonCategories = {
    client_refused: {
      label: 'Client Refused',
      subcategories: [
        'Feeling nauseous',
        'Difficulty swallowing',
        'Taste/texture issues',
        'Feeling better, doesn\'t want medication',
        'Confused/agitated',
        'Religious/cultural reasons',
        'Fear of side effects'
      ]
    },
    medication_unavailable: {
      label: 'Medication Not Available',
      subcategories: [
        'Out of stock',
        'Pharmacy delay',
        'Wrong medication delivered',
        'Expired medication',
        'Damaged/contaminated'
      ]
    },
    medical_contraindication: {
      label: 'Medical Contraindication',
      subcategories: [
        'Blood pressure too low/high',
        'Blood sugar levels concerning',
        'Drug interaction identified',
        'Allergic reaction risk',
        'Recent adverse event',
        'Doctor\'s hold order'
      ]
    },
    other: {
      label: 'Other',
      subcategories: []
    }
  };

  const missedReasonCategories: MissedReasonCategories = {
    medication_unavailable: {
      label: 'Medication Not Available',
      subcategories: [
        'Out of stock',
        'Pharmacy delay',
        'Wrong medication delivered',
        'Expired medication',
        'Damaged/contaminated'
      ]
    },
    resident_unavailable: {
      label: 'Resident Unavailable',
      subcategories: [
        'At medical appointment',
        'Sleeping/resting',
        'Away from facility',
        'In therapy session',
        'Emergency situation'
      ]
    },
    staff_oversight: {
      label: 'Staff Oversight',
      subcategories: [
        'Missed during rounds',
        'Shift change confusion',
        'Emergency took priority',
        'Documentation error',
        'Communication breakdown'
      ]
    },
    medical_hold: {
      label: 'Medical Hold',
      subcategories: [
        'Doctor\'s order to hold',
        'Pending lab results',
        'Pre-procedure hold',
        'Adverse reaction monitoring',
        'Drug interaction concern'
      ]
    },
    other: {
      label: 'Other',
      subcategories: []
    }
  };

  // Handle refused/missed medication with reason
  const handleMedicationAction = (medication: Medication, action: 'refused' | 'missed') => {
    setSelectedMedication(medication);
    setReasonType(action);
    setReasonCategory('');
    setReasonSubcategory('');
    setCustomReason('');
    setShowReasonModal(true);
  };

  const handleAdminister = (medicationId: string) => {
    setMedications(prev => prev.map(med =>
      med.id === medicationId
        ? {
            ...med,
            status: 'administered' as const,
            administeredBy: 'Current Staff Member',
            timestamp: new Date().toISOString()
          }
        : med
    ));
  };

  const submitReason = () => {
    if (!selectedMedication || !reasonType || !reasonCategory) {
      alert('Please fill in all required fields');
      return;
    }

    const reasonData = {
      category: reasonCategory as any,
      subcategory: reasonSubcategory,
      customReason: customReason,
      staffMember: 'Current Staff Member',
      timestamp: new Date().toISOString(),
      aiSuggestions: generateAISuggestions(reasonCategory, reasonSubcategory, customReason)
    };

    setMedications(prev => prev.map(med =>
      med.id === selectedMedication.id
        ? {
            ...med,
            status: reasonType,
            [reasonType === 'refused' ? 'refusedReason' : 'missedReason']: reasonData
          }
        : med
    ));

    setShowReasonModal(false);
    setSelectedMedication(null);
    setReasonType(null);
  };

  const generateAISuggestions = (category: string, subcategory: string, customReason: string): string[] => {
    // AI-enhanced suggestions based on the reason
    const suggestions: string[] = [];

    if (category === 'client_refused' && subcategory === 'Feeling nauseous') {
      suggestions.push(
        'Consider anti-nausea medication before next dose',
        'Offer medication with food',
        'Check for drug interactions causing nausea'
      );
    } else if (category === 'medication_unavailable' && subcategory === 'Out of stock') {
      suggestions.push(
        'Contact pharmacy for emergency supply',
        'Check for therapeutic alternatives',
        'Notify physician of medication gap'
      );
    } else if (category === 'resident_unavailable' && subcategory === 'At medical appointment') {
      suggestions.push(
        'Coordinate with appointment schedule',
        'Adjust medication timing',
        'Ensure medication travels with resident'
      );
    }

    return suggestions;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'administered':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'missed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'refused':
        return <XCircleIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'administered':
        return 'bg-green-100 text-green-800';
      case 'missed':
        return 'bg-red-100 text-red-800';
      case 'refused':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };



  const filteredMedications = medications.filter(med => {
    const hour = parseInt(med.scheduledTime.split(':')[0]);
    if (selectedShift === 'morning') return hour >= 6 && hour < 14;
    if (selectedShift === 'afternoon') return hour >= 14 && hour < 22;
    return hour >= 22 || hour < 6;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          AI-Enhanced Medication Administration Record (AIMAR)
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          Intelligent medication management with AI-powered safety checks and administration tracking
        </p>
      </div>

      {/* Controls */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <div>
              <label htmlFor="date" className="block text-sm font-medium text-gray-700">
                Date
              </label>
              <input
                type="date"
                id="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
              />
            </div>
            <div>
              <label htmlFor="shift" className="block text-sm font-medium text-gray-700">
                Shift
              </label>
              <select
                id="shift"
                value={selectedShift}
                onChange={(e) => setSelectedShift(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
              >
                <option value="morning">Morning (6 AM - 2 PM)</option>
                <option value="afternoon">Afternoon (2 PM - 10 PM)</option>
                <option value="night">Night (10 PM - 6 AM)</option>
              </select>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              <span className="font-medium">{filteredMedications.length}</span> medications scheduled
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
                <span className="text-sm text-yellow-700">
                  {medications.filter(m => m.aiAlerts && m.aiAlerts.length > 0).length} AI alerts
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <BeakerIcon className="h-4 w-4 text-blue-500" />
                <span className="text-sm text-blue-700">
                  AI Safety Monitoring Active
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Medication List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Scheduled Medications - {selectedShift.charAt(0).toUpperCase() + selectedShift.slice(1)} Shift
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredMedications.map((medication) => (
            <div key={medication.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <UserIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">
                        {medication.residentName}
                      </h4>
                      <p className="text-sm text-gray-500">ID: {medication.residentId}</p>
                    </div>
                  </div>
                  
                  <div className="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Medication</p>
                      <p className="text-sm text-gray-900">{medication.medicationName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Dosage & Route</p>
                      <p className="text-sm text-gray-900">{medication.dosage} - {medication.route}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Scheduled Time</p>
                      <p className="text-sm text-gray-900">{medication.scheduledTime}</p>
                    </div>
                  </div>

                  {/* AI Alerts */}
                  {medication.aiAlerts && medication.aiAlerts.length > 0 && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <div className="flex items-start">
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
                        <div className="ml-3">
                          <h5 className="text-sm font-medium text-yellow-800">AI Alerts</h5>
                          <ul className="mt-1 text-sm text-yellow-700 space-y-1">
                            {medication.aiAlerts.map((alert, index) => (
                              <li key={index}>• {alert}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Drug Interactions */}
                  {medication.interactions && medication.interactions.length > 0 && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                      <div className="flex items-start">
                        <BeakerIcon className="h-5 w-5 text-red-400 mt-0.5" />
                        <div className="ml-3">
                          <h5 className="text-sm font-medium text-red-800">Drug Interactions</h5>
                          <ul className="mt-1 text-sm text-red-700 space-y-1">
                            {medication.interactions.map((interaction, index) => (
                              <li key={index}>• {interaction}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="ml-6 flex flex-col items-end space-y-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(medication.status)}
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(medication.status)}`}>
                      {medication.status.charAt(0).toUpperCase() + medication.status.slice(1)}
                    </span>
                  </div>
                  
                  {medication.status === 'pending' && (
                    <div className="flex flex-col space-y-2">
                      <button
                        onClick={() => handleAdminister(medication.id)}
                        className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                      >
                        Administer
                      </button>
                      <button
                        onClick={() => handleMedicationAction(medication, 'refused')}
                        className="px-4 py-2 bg-yellow-600 text-white text-sm font-medium rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                      >
                        Refused
                      </button>
                      <button
                        onClick={() => handleMedicationAction(medication, 'missed')}
                        className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                      >
                        Missed
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Administered</p>
              <p className="text-2xl font-semibold text-gray-900">
                {medications.filter(m => m.status === 'administered').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-gray-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-2xl font-semibold text-gray-900">
                {medications.filter(m => m.status === 'pending').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">AI Alerts</p>
              <p className="text-2xl font-semibold text-gray-900">
                {medications.filter(m => m.aiAlerts && m.aiAlerts.length > 0).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <BeakerIcon className="h-8 w-8 text-red-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Interactions</p>
              <p className="text-2xl font-semibold text-gray-900">
                {medications.filter(m => m.interactions && m.interactions.length > 0).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Reason Modal */}
      {showReasonModal && selectedMedication && reasonType && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowReasonModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className={`mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10 ${
                    reasonType === 'refused' ? 'bg-yellow-100' : 'bg-red-100'
                  }`}>
                    {reasonType === 'refused' ? (
                      <ExclamationCircleIcon className="h-6 w-6 text-yellow-600" />
                    ) : (
                      <XCircleIcon className="h-6 w-6 text-red-600" />
                    )}
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      {reasonType === 'refused' ? 'Medication Refused' : 'Medication Missed'}
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        {selectedMedication.medicationName} ({selectedMedication.dosage}) for {selectedMedication.residentName}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">
                        Scheduled: {selectedMedication.scheduledTime}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-4">
                  {/* Reason Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Primary Reason <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={reasonCategory}
                      onChange={(e) => {
                        setReasonCategory(e.target.value);
                        setReasonSubcategory('');
                      }}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select a reason...</option>
                      {Object.entries(reasonType === 'refused' ? refusedReasonCategories : missedReasonCategories).map(([key, category]) => (
                        <option key={key} value={key}>{category.label}</option>
                      ))}
                    </select>
                  </div>

                  {/* Subcategory */}
                  {reasonCategory && (() => {
                    const categories = reasonType === 'refused' ? refusedReasonCategories : missedReasonCategories;
                    const selectedCategory = categories[reasonCategory as keyof typeof categories];
                    return selectedCategory?.subcategories && selectedCategory.subcategories.length > 0;
                  })() && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Specific Reason
                      </label>
                      <select
                        value={reasonSubcategory}
                        onChange={(e) => setReasonSubcategory(e.target.value)}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      >
                        <option value="">Select specific reason...</option>
                        {(() => {
                          const categories = reasonType === 'refused' ? refusedReasonCategories : missedReasonCategories;
                          const selectedCategory = categories[reasonCategory as keyof typeof categories];
                          return selectedCategory?.subcategories?.map((subcategory: string) => (
                            <option key={subcategory} value={subcategory}>{subcategory}</option>
                          ));
                        })()}
                      </select>
                    </div>
                  )}

                  {/* Custom Reason */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Additional Details {reasonCategory === 'other' && <span className="text-red-500">*</span>}
                    </label>
                    <textarea
                      value={customReason}
                      onChange={(e) => setCustomReason(e.target.value)}
                      placeholder="Provide additional details about the situation..."
                      rows={3}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>

                  {/* AI Suggestions Preview */}
                  {reasonCategory && reasonSubcategory && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="flex items-center mb-2">
                        <ChatBubbleLeftEllipsisIcon className="h-4 w-4 text-blue-600 mr-2" />
                        <span className="text-sm font-medium text-blue-800">AI Suggestions</span>
                      </div>
                      <ul className="text-xs text-blue-700 space-y-1">
                        {generateAISuggestions(reasonCategory, reasonSubcategory, customReason).map((suggestion, index) => (
                          <li key={index} className="flex items-start">
                            <span className="mr-2">•</span>
                            <span>{suggestion}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={submitReason}
                  disabled={!reasonCategory || (reasonCategory === 'other' && !customReason.trim())}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm"
                >
                  <DocumentTextIcon className="h-4 w-4 mr-2" />
                  Submit Reason
                </button>
                <button
                  onClick={() => setShowReasonModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EMAR;
