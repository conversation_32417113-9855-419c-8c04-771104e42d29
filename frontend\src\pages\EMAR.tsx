import React, { useState } from 'react';
import {
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  BeakerIcon,
  UserIcon,
} from '@heroicons/react/24/outline';

interface Medication {
  id: string;
  residentName: string;
  residentId: string;
  medicationName: string;
  dosage: string;
  route: string;
  frequency: string;
  scheduledTime: string;
  status: 'pending' | 'administered' | 'missed' | 'refused';
  aiAlerts?: string[];
  interactions?: string[];
}

const EMAR: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedShift, setSelectedShift] = useState('morning');

  // Mock medication data
  const medications: Medication[] = [
    {
      id: '1',
      residentName: '<PERSON>',
      residentId: 'R001',
      medicationName: 'Metformin',
      dosage: '500mg',
      route: 'Oral',
      frequency: 'Twice daily',
      scheduledTime: '08:00',
      status: 'administered',
      aiAlerts: [],
    },
    {
      id: '2',
      residentName: '<PERSON>',
      residentId: 'R002',
      medicationName: 'Lisinopril',
      dosage: '10mg',
      route: 'Oral',
      frequency: 'Once daily',
      scheduledTime: '09:00',
      status: 'pending',
      aiAlerts: ['Blood pressure check recommended before administration'],
      interactions: [],
    },
    {
      id: '3',
      residentName: 'Dorothy Garcia',
      residentId: 'R003',
      medicationName: 'Warfarin',
      dosage: '5mg',
      route: 'Oral',
      frequency: 'Once daily',
      scheduledTime: '09:30',
      status: 'pending',
      aiAlerts: ['INR levels due for check', 'Potential interaction with new supplement'],
      interactions: ['Vitamin K supplement - monitor INR closely'],
    },
    {
      id: '4',
      residentName: 'Robert Davis',
      residentId: 'R004',
      medicationName: 'Insulin Glargine',
      dosage: '20 units',
      route: 'Subcutaneous',
      frequency: 'Once daily',
      scheduledTime: '10:00',
      status: 'pending',
      aiAlerts: ['Blood glucose trending high - consider dose adjustment'],
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'administered':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'missed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'refused':
        return <XCircleIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'administered':
        return 'bg-green-100 text-green-800';
      case 'missed':
        return 'bg-red-100 text-red-800';
      case 'refused':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleAdminister = (medicationId: string) => {
    // In real app, this would update the medication status
    console.log('Administering medication:', medicationId);
  };

  const filteredMedications = medications.filter(med => {
    const hour = parseInt(med.scheduledTime.split(':')[0]);
    if (selectedShift === 'morning') return hour >= 6 && hour < 14;
    if (selectedShift === 'afternoon') return hour >= 14 && hour < 22;
    return hour >= 22 || hour < 6;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Electronic Medication Administration Record (eMAR)
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          AI-Enhanced medication management for residential care facilities
        </p>
      </div>

      {/* Controls */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <div>
              <label htmlFor="date" className="block text-sm font-medium text-gray-700">
                Date
              </label>
              <input
                type="date"
                id="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
              />
            </div>
            <div>
              <label htmlFor="shift" className="block text-sm font-medium text-gray-700">
                Shift
              </label>
              <select
                id="shift"
                value={selectedShift}
                onChange={(e) => setSelectedShift(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
              >
                <option value="morning">Morning (6 AM - 2 PM)</option>
                <option value="afternoon">Afternoon (2 PM - 10 PM)</option>
                <option value="night">Night (10 PM - 6 AM)</option>
              </select>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              <span className="font-medium">{filteredMedications.length}</span> medications scheduled
            </div>
            <div className="flex items-center space-x-2">
              <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
              <span className="text-sm text-yellow-700">
                {medications.filter(m => m.aiAlerts && m.aiAlerts.length > 0).length} AI alerts
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Medication List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Scheduled Medications - {selectedShift.charAt(0).toUpperCase() + selectedShift.slice(1)} Shift
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredMedications.map((medication) => (
            <div key={medication.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <UserIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">
                        {medication.residentName}
                      </h4>
                      <p className="text-sm text-gray-500">ID: {medication.residentId}</p>
                    </div>
                  </div>
                  
                  <div className="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Medication</p>
                      <p className="text-sm text-gray-900">{medication.medicationName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Dosage & Route</p>
                      <p className="text-sm text-gray-900">{medication.dosage} - {medication.route}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Scheduled Time</p>
                      <p className="text-sm text-gray-900">{medication.scheduledTime}</p>
                    </div>
                  </div>

                  {/* AI Alerts */}
                  {medication.aiAlerts && medication.aiAlerts.length > 0 && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <div className="flex items-start">
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
                        <div className="ml-3">
                          <h5 className="text-sm font-medium text-yellow-800">AI Alerts</h5>
                          <ul className="mt-1 text-sm text-yellow-700 space-y-1">
                            {medication.aiAlerts.map((alert, index) => (
                              <li key={index}>• {alert}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Drug Interactions */}
                  {medication.interactions && medication.interactions.length > 0 && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                      <div className="flex items-start">
                        <BeakerIcon className="h-5 w-5 text-red-400 mt-0.5" />
                        <div className="ml-3">
                          <h5 className="text-sm font-medium text-red-800">Drug Interactions</h5>
                          <ul className="mt-1 text-sm text-red-700 space-y-1">
                            {medication.interactions.map((interaction, index) => (
                              <li key={index}>• {interaction}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="ml-6 flex flex-col items-end space-y-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(medication.status)}
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(medication.status)}`}>
                      {medication.status.charAt(0).toUpperCase() + medication.status.slice(1)}
                    </span>
                  </div>
                  
                  {medication.status === 'pending' && (
                    <div className="flex flex-col space-y-2">
                      <button
                        onClick={() => handleAdminister(medication.id)}
                        className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                      >
                        Administer
                      </button>
                      <button className="px-4 py-2 bg-yellow-600 text-white text-sm font-medium rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        Refused
                      </button>
                      <button className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                        Missed
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Administered</p>
              <p className="text-2xl font-semibold text-gray-900">
                {medications.filter(m => m.status === 'administered').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-gray-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-2xl font-semibold text-gray-900">
                {medications.filter(m => m.status === 'pending').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">AI Alerts</p>
              <p className="text-2xl font-semibold text-gray-900">
                {medications.filter(m => m.aiAlerts && m.aiAlerts.length > 0).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <BeakerIcon className="h-8 w-8 text-red-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Interactions</p>
              <p className="text-2xl font-semibold text-gray-900">
                {medications.filter(m => m.interactions && m.interactions.length > 0).length}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EMAR;
