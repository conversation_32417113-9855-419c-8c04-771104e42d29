{"ast": null, "code": "// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = defaultedOptions => {\n  if (defaultedOptions.suspense) {\n    const clamp = value => value === \"static\" ? value : Math.max(value ?? 1e3, 1e3);\n    const originalStaleTime = defaultedOptions.staleTime;\n    defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args) => clamp(originalStaleTime(...args)) : clamp(originalStaleTime);\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\nexport { defaultThrowOnError, ensureSuspenseTimers, fetchOptimistic, shouldSuspend, willFetch };", "map": {"version": 3, "names": ["defaultThrowOnError", "_error", "query", "state", "data", "ensureSuspenseTimers", "defaultedOptions", "suspense", "clamp", "value", "Math", "max", "originalStaleTime", "staleTime", "args", "gcTime", "<PERSON><PERSON><PERSON><PERSON>", "result", "isRestoring", "isLoading", "isFetching", "shouldSuspend", "isPending", "fetchOptimistic", "observer", "errorResetBoundary", "catch", "clear<PERSON><PERSON>t"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@tanstack\\react-query\\src\\suspense.ts"], "sourcesContent": ["import type {\n  DefaultError,\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserver,\n  QueryObserverResult,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\n\nexport const defaultThrowOnError = <\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  _error: TError,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n) => query.state.data === undefined\n\nexport const ensureSuspenseTimers = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Handle staleTime to ensure minimum 1000ms in Suspense mode\n    // This prevents unnecessary refetching when components remount after suspending\n\n    const clamp = (value: number | 'static' | undefined) =>\n      value === 'static' ? value : Math.max(value ?? 1000, 1000)\n\n    const originalStaleTime = defaultedOptions.staleTime\n    defaultedOptions.staleTime =\n      typeof originalStaleTime === 'function'\n        ? (...args) => clamp(originalStaleTime(...args))\n        : clamp(originalStaleTime)\n\n    if (typeof defaultedOptions.gcTime === 'number') {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1000)\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n) => defaultedOptions?.suspense && result.isPending\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer.fetchOptimistic(defaultedOptions).catch(() => {\n    errorResetBoundary.clearReset()\n  })\n"], "mappings": ";AAUO,IAAMA,mBAAA,GAAsBA,CAMjCC,MAAA,EACAC,KAAA,KACGA,KAAA,CAAMC,KAAA,CAAMC,IAAA,KAAS;AAEnB,IAAMC,oBAAA,GACXC,gBAAA,IACG;EACH,IAAIA,gBAAA,CAAiBC,QAAA,EAAU;IAI7B,MAAMC,KAAA,GAASC,KAAA,IACbA,KAAA,KAAU,WAAWA,KAAA,GAAQC,IAAA,CAAKC,GAAA,CAAIF,KAAA,IAAS,KAAM,GAAI;IAE3D,MAAMG,iBAAA,GAAoBN,gBAAA,CAAiBO,SAAA;IAC3CP,gBAAA,CAAiBO,SAAA,GACf,OAAOD,iBAAA,KAAsB,aACzB,IAAIE,IAAA,KAASN,KAAA,CAAMI,iBAAA,CAAkB,GAAGE,IAAI,CAAC,IAC7CN,KAAA,CAAMI,iBAAiB;IAE7B,IAAI,OAAON,gBAAA,CAAiBS,MAAA,KAAW,UAAU;MAC/CT,gBAAA,CAAiBS,MAAA,GAASL,IAAA,CAAKC,GAAA,CAAIL,gBAAA,CAAiBS,MAAA,EAAQ,GAAI;IAClE;EACF;AACF;AAEO,IAAMC,SAAA,GAAYA,CACvBC,MAAA,EACAC,WAAA,KACGD,MAAA,CAAOE,SAAA,IAAaF,MAAA,CAAOG,UAAA,IAAc,CAACF,WAAA;AAExC,IAAMG,aAAA,GAAgBA,CAC3Bf,gBAAA,EAGAW,MAAA,KACGX,gBAAA,EAAkBC,QAAA,IAAYU,MAAA,CAAOK,SAAA;AAEnC,IAAMC,eAAA,GAAkBA,CAO7BjB,gBAAA,EAOAkB,QAAA,EACAC,kBAAA,KAEAD,QAAA,CAASD,eAAA,CAAgBjB,gBAAgB,EAAEoB,KAAA,CAAM,MAAM;EACrDD,kBAAA,CAAmBE,UAAA,CAAW;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}