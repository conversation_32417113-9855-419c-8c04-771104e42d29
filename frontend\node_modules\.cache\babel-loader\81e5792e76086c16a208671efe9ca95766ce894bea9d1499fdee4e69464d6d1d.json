{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\medisyn\\\\frontend\\\\src\\\\pages\\\\Profile.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"Profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-600\",\n        children: \"Manage your account settings and preferences\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: \"Personal Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"First Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-input\",\n              value: (user === null || user === void 0 ? void 0 : user.first_name) || '',\n              readOnly: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Last Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-input\",\n              value: (user === null || user === void 0 ? void 0 : user.last_name) || '',\n              readOnly: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              className: \"form-input\",\n              value: (user === null || user === void 0 ? void 0 : user.email) || '',\n              readOnly: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Role\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-input capitalize\",\n              value: (user === null || user === void 0 ? void 0 : user.role) || '',\n              readOnly: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 pt-6 border-t border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Profile editing functionality will be implemented in the next phase. This includes updating personal information, changing passwords, managing certifications, and notification preferences.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useAuth", "jsxDEV", "_jsxDEV", "Profile", "_s", "user", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "first_name", "readOnly", "last_name", "email", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/src/pages/Profile.tsx"], "sourcesContent": ["import React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Profile: React.FC = () => {\n  const { user } = useAuth();\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">Profile</h1>\n        <p className=\"mt-1 text-sm text-gray-600\">\n          Manage your account settings and preferences\n        </p>\n      </div>\n\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Personal Information</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n            <div>\n              <label className=\"form-label\">First Name</label>\n              <input\n                type=\"text\"\n                className=\"form-input\"\n                value={user?.first_name || ''}\n                readOnly\n              />\n            </div>\n            <div>\n              <label className=\"form-label\">Last Name</label>\n              <input\n                type=\"text\"\n                className=\"form-input\"\n                value={user?.last_name || ''}\n                readOnly\n              />\n            </div>\n            <div>\n              <label className=\"form-label\">Email</label>\n              <input\n                type=\"email\"\n                className=\"form-input\"\n                value={user?.email || ''}\n                readOnly\n              />\n            </div>\n            <div>\n              <label className=\"form-label\">Role</label>\n              <input\n                type=\"text\"\n                className=\"form-input capitalize\"\n                value={user?.role || ''}\n                readOnly\n              />\n            </div>\n          </div>\n          \n          <div className=\"mt-6 pt-6 border-t border-gray-200\">\n            <p className=\"text-sm text-gray-500\">\n              Profile editing functionality will be implemented in the next phase.\n              This includes updating personal information, changing passwords,\n              managing certifications, and notification preferences.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAE1B,oBACEE,OAAA;IAAKI,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBL,OAAA;MAAAK,QAAA,gBACEL,OAAA;QAAII,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7DT,OAAA;QAAGI,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENT,OAAA;MAAKI,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBL,OAAA;QAAKI,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BL,OAAA;UAAII,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACNT,OAAA;QAAKI,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBL,OAAA;UAAKI,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDL,OAAA;YAAAK,QAAA,gBACEL,OAAA;cAAOI,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChDT,OAAA;cACEU,IAAI,EAAC,MAAM;cACXN,SAAS,EAAC,YAAY;cACtBO,KAAK,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,UAAU,KAAI,EAAG;cAC9BC,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNT,OAAA;YAAAK,QAAA,gBACEL,OAAA;cAAOI,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/CT,OAAA;cACEU,IAAI,EAAC,MAAM;cACXN,SAAS,EAAC,YAAY;cACtBO,KAAK,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,SAAS,KAAI,EAAG;cAC7BD,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNT,OAAA;YAAAK,QAAA,gBACEL,OAAA;cAAOI,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CT,OAAA;cACEU,IAAI,EAAC,OAAO;cACZN,SAAS,EAAC,YAAY;cACtBO,KAAK,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,KAAK,KAAI,EAAG;cACzBF,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNT,OAAA;YAAAK,QAAA,gBACEL,OAAA;cAAOI,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CT,OAAA;cACEU,IAAI,EAAC,MAAM;cACXN,SAAS,EAAC,uBAAuB;cACjCO,KAAK,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,IAAI,KAAI,EAAG;cACxBH,QAAQ;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENT,OAAA;UAAKI,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDL,OAAA;YAAGI,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAIrC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACP,EAAA,CAnEID,OAAiB;EAAA,QACJH,OAAO;AAAA;AAAAmB,EAAA,GADpBhB,OAAiB;AAqEvB,eAAeA,OAAO;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}