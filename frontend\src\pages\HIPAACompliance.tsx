import React, { useState } from 'react';
import {
  ShieldCheckIcon,
  EyeIcon,
  LockClosedIcon,
  DocumentTextIcon,
  UserGroupIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  BellIcon,
  AcademicCapIcon,
  ChartBarIcon,
  KeyIcon,
  ServerIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';

interface AuditLogEntry {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  userRole: string;
  action: 'view' | 'create' | 'update' | 'delete' | 'export' | 'print' | 'login' | 'logout';
  resourceType: 'resident' | 'medical_record' | 'medication' | 'document' | 'staff' | 'system';
  resourceId: string;
  resourceName: string;
  ipAddress: string;
  userAgent: string;
  sessionId: string;
  details: {
    fieldsAccessed?: string[];
    fieldsModified?: string[];
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    reason?: string;
    authorization?: string;
  };
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  complianceFlags: string[];
}

interface AccessControl {
  userId: string;
  userName: string;
  role: string;
  permissions: {
    residents: {
      view: boolean;
      create: boolean;
      update: boolean;
      delete: boolean;
      export: boolean;
      viewAll: boolean;
      assignedOnly: boolean;
    };
    medicalRecords: {
      view: boolean;
      create: boolean;
      update: boolean;
      delete: boolean;
      viewSensitive: boolean;
      emergencyAccess: boolean;
    };
    medications: {
      view: boolean;
      administer: boolean;
      modify: boolean;
      order: boolean;
    };
    documents: {
      view: boolean;
      upload: boolean;
      download: boolean;
      delete: boolean;
      sign: boolean;
    };
    staff: {
      view: boolean;
      manage: boolean;
      viewPerformance: boolean;
      viewSalary: boolean;
    };
    system: {
      adminAccess: boolean;
      auditLogs: boolean;
      systemConfig: boolean;
      userManagement: boolean;
    };
  };
  restrictions: {
    ipWhitelist?: string[];
    timeRestrictions?: {
      allowedHours: { start: string; end: string };
      allowedDays: string[];
    };
    locationRestrictions?: string[];
    deviceRestrictions?: string[];
  };
  lastAccess: string;
  failedAttempts: number;
  accountStatus: 'active' | 'locked' | 'suspended' | 'disabled';
}

interface PrivacyConsent {
  residentId: string;
  residentName: string;
  consentType: 'treatment' | 'payment' | 'operations' | 'marketing' | 'research' | 'disclosure';
  status: 'granted' | 'denied' | 'revoked' | 'expired';
  grantedDate: string;
  expiryDate?: string;
  revokedDate?: string;
  consentDetails: {
    purpose: string;
    dataTypes: string[];
    recipients: string[];
    retentionPeriod: string;
    optOut: boolean;
  };
  documentedBy: string;
  witnessedBy?: string;
  digitalSignature?: {
    signatureData: string;
    timestamp: string;
    ipAddress: string;
  };
}

interface SecurityIncident {
  id: string;
  incidentDate: string;
  reportedDate: string;
  reportedBy: string;
  incidentType: 'unauthorized_access' | 'data_breach' | 'system_compromise' | 'physical_security' | 'policy_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'reported' | 'investigating' | 'contained' | 'resolved' | 'closed';
  affectedResidents: number;
  affectedRecords: number;
  description: string;
  rootCause?: string;
  containmentActions: string[];
  remediationActions: string[];
  preventiveActions: string[];
  notificationRequired: boolean;
  notificationsSent: Array<{
    recipient: string;
    method: 'email' | 'mail' | 'phone';
    sentDate: string;
    acknowledged: boolean;
  }>;
  regulatoryReporting: {
    hhs: { required: boolean; submitted: boolean; submissionDate?: string };
    state: { required: boolean; submitted: boolean; submissionDate?: string };
    other: Array<{ agency: string; required: boolean; submitted: boolean; submissionDate?: string }>;
  };
}

const HIPAACompliance: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'audit-logs' | 'access-control' | 'privacy' | 'incidents' | 'training'>('overview');

  // Mock data for audit logs
  const auditLogs: AuditLogEntry[] = [
    {
      id: 'audit_001',
      timestamp: '2024-01-20T14:30:15Z',
      userId: 'U001',
      userName: 'Sarah Johnson',
      userRole: 'nurse',
      action: 'view',
      resourceType: 'medical_record',
      resourceId: 'MR001',
      resourceName: 'Margaret Thompson - Medical Record',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      sessionId: 'sess_abc123',
      details: {
        fieldsAccessed: ['medications', 'vital_signs', 'allergies'],
        reason: 'Medication administration review'
      },
      riskLevel: 'low',
      complianceFlags: []
    },
    {
      id: 'audit_002',
      timestamp: '2024-01-20T15:45:22Z',
      userId: 'U002',
      userName: 'John Smith',
      userRole: 'caregiver',
      action: 'view',
      resourceType: 'resident',
      resourceId: 'R001',
      resourceName: 'Margaret Thompson - Profile',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      sessionId: 'sess_def456',
      details: {
        fieldsAccessed: ['care_plan', 'daily_notes'],
        reason: 'Shift handoff review'
      },
      riskLevel: 'low',
      complianceFlags: []
    },
    {
      id: 'audit_003',
      timestamp: '2024-01-20T16:20:10Z',
      userId: 'U003',
      userName: 'Unknown User',
      userRole: 'unknown',
      action: 'login',
      resourceType: 'system',
      resourceId: 'login_attempt',
      resourceName: 'Failed Login Attempt',
      ipAddress: '************',
      userAgent: 'curl/7.68.0',
      sessionId: 'sess_failed',
      details: {
        reason: 'Invalid credentials - multiple attempts'
      },
      riskLevel: 'high',
      complianceFlags: ['suspicious_activity', 'external_ip', 'multiple_failures']
    }
  ];

  // Mock privacy consents
  const privacyConsents: PrivacyConsent[] = [
    {
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      consentType: 'treatment',
      status: 'granted',
      grantedDate: '2024-01-15',
      consentDetails: {
        purpose: 'Medical treatment and care coordination',
        dataTypes: ['Medical records', 'Medication information', 'Care notes'],
        recipients: ['Healthcare providers', 'Emergency contacts'],
        retentionPeriod: 'Duration of residency + 7 years',
        optOut: false
      },
      documentedBy: 'Sarah Johnson, RN'
    },
    {
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      consentType: 'marketing',
      status: 'denied',
      grantedDate: '2024-01-15',
      consentDetails: {
        purpose: 'Marketing communications and newsletters',
        dataTypes: ['Contact information', 'Preferences'],
        recipients: ['Marketing department'],
        retentionPeriod: 'Until consent withdrawn',
        optOut: true
      },
      documentedBy: 'Sarah Johnson, RN'
    }
  ];

  // Mock security incidents
  const securityIncidents: SecurityIncident[] = [
    {
      id: 'INC_001',
      incidentDate: '2024-01-18T10:30:00Z',
      reportedDate: '2024-01-18T11:00:00Z',
      reportedBy: 'IT Administrator',
      incidentType: 'unauthorized_access',
      severity: 'medium',
      status: 'resolved',
      affectedResidents: 0,
      affectedRecords: 0,
      description: 'Multiple failed login attempts from external IP address',
      rootCause: 'Brute force attack attempt',
      containmentActions: ['IP address blocked', 'Account temporarily locked'],
      remediationActions: ['Password reset required', 'Security awareness reminder sent'],
      preventiveActions: ['Enhanced monitoring implemented', 'Rate limiting increased'],
      notificationRequired: false,
      notificationsSent: [],
      regulatoryReporting: {
        hhs: { required: false, submitted: false },
        state: { required: false, submitted: false },
        other: []
      }
    }
  ];

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'granted':
      case 'active':
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'denied':
      case 'revoked':
      case 'locked':
        return 'bg-red-100 text-red-800';
      case 'investigating':
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const tabs = [
    { id: 'overview', name: 'Compliance Overview', icon: ShieldCheckIcon },
    { id: 'audit-logs', name: 'Audit Logs', icon: DocumentTextIcon },
    { id: 'access-control', name: 'Access Control', icon: LockClosedIcon },
    { id: 'privacy', name: 'Privacy & Consent', icon: UserGroupIcon },
    { id: 'incidents', name: 'Security Incidents', icon: ExclamationTriangleIcon },
    { id: 'training', name: 'HIPAA Training', icon: AcademicCapIcon },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">HIPAA Compliance Center</h1>
        <p className="mt-1 text-sm text-gray-600">
          Comprehensive HIPAA compliance monitoring and management for residential healthcare
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            🛡️ Enterprise Security
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            📋 Audit Trails
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            🔐 Access Controls
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
            🎓 Staff Training
          </span>
        </div>
      </div>

      {/* Compliance Status Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Compliance Score</p>
              <p className="text-2xl font-bold">98.5%</p>
            </div>
            <ShieldCheckIcon className="h-8 w-8 text-green-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Audit Events Today</p>
              <p className="text-2xl font-bold">1,247</p>
            </div>
            <DocumentTextIcon className="h-8 w-8 text-blue-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-100 text-sm">Security Alerts</p>
              <p className="text-2xl font-bold">2</p>
            </div>
            <ExclamationTriangleIcon className="h-8 w-8 text-yellow-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Training Compliance</p>
              <p className="text-2xl font-bold">94%</p>
            </div>
            <AcademicCapIcon className="h-8 w-8 text-purple-200" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {selectedTab === 'overview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">HIPAA Compliance Dashboard</h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Compliance Metrics */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">Compliance Metrics</h4>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Administrative Safeguards</span>
                        <span>100%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '100%' }}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Physical Safeguards</span>
                        <span>95%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '95%' }}></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Technical Safeguards</span>
                        <span>98%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '98%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Recent Security Events */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">Recent Security Events</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Security Scan Completed</p>
                          <p className="text-xs text-gray-600">2 hours ago</p>
                        </div>
                      </div>
                      <span className="text-xs text-green-600">No issues found</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div className="flex items-center">
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Failed Login Attempts</p>
                          <p className="text-xs text-gray-600">4 hours ago</p>
                        </div>
                      </div>
                      <span className="text-xs text-yellow-600">Monitoring</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center">
                        <BellIcon className="h-5 w-5 text-blue-500 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Training Reminder Sent</p>
                          <p className="text-xs text-gray-600">1 day ago</p>
                        </div>
                      </div>
                      <span className="text-xs text-blue-600">Completed</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* HIPAA Requirements Checklist */}
              <div className="border border-gray-200 rounded-lg p-6">
                <h4 className="font-medium text-gray-900 mb-4">HIPAA Requirements Status</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-3">Administrative Safeguards</h5>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Security Officer Assigned</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Workforce Training</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Access Management</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Contingency Plan</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-3">Physical Safeguards</h5>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Facility Access Controls</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Workstation Security</span>
                      </div>
                      <div className="flex items-center">
                        <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500 mr-2" />
                        <span className="text-sm text-gray-600">Device Controls</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-700 mb-3">Technical Safeguards</h5>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Access Control</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Audit Controls</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Integrity</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-600">Transmission Security</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'audit-logs' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Audit Trail</h3>
                <div className="flex space-x-2">
                  <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                    Export Logs
                  </button>
                  <button className="px-3 py-1 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700">
                    Filter
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Timestamp
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Action
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Resource
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Risk Level
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        IP Address
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {auditLogs.map((log) => (
                      <tr key={log.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(log.timestamp).toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{log.userName}</div>
                            <div className="text-sm text-gray-500 capitalize">{log.userRole}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full capitalize">
                            {log.action}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm text-gray-900">{log.resourceName}</div>
                            <div className="text-sm text-gray-500 capitalize">{log.resourceType}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${getRiskLevelColor(log.riskLevel)}`}>
                            {log.riskLevel}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {log.ipAddress}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {selectedTab === 'access-control' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Role-Based Access Control</h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="border border-gray-200 rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">Access Permissions by Role</h4>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h5 className="font-medium text-blue-900">Administrator</h5>
                      <p className="text-sm text-blue-700 mt-1">Full system access, user management, audit logs</p>
                      <div className="mt-2 flex flex-wrap gap-1">
                        <span className="px-2 py-1 text-xs bg-blue-200 text-blue-800 rounded">All Residents</span>
                        <span className="px-2 py-1 text-xs bg-blue-200 text-blue-800 rounded">System Config</span>
                        <span className="px-2 py-1 text-xs bg-blue-200 text-blue-800 rounded">Audit Logs</span>
                      </div>
                    </div>

                    <div className="p-4 bg-green-50 rounded-lg">
                      <h5 className="font-medium text-green-900">Nurse</h5>
                      <p className="text-sm text-green-700 mt-1">Medical records, medication administration, care plans</p>
                      <div className="mt-2 flex flex-wrap gap-1">
                        <span className="px-2 py-1 text-xs bg-green-200 text-green-800 rounded">Medical Records</span>
                        <span className="px-2 py-1 text-xs bg-green-200 text-green-800 rounded">Medications</span>
                        <span className="px-2 py-1 text-xs bg-green-200 text-green-800 rounded">Care Plans</span>
                      </div>
                    </div>

                    <div className="p-4 bg-yellow-50 rounded-lg">
                      <h5 className="font-medium text-yellow-900">Caregiver</h5>
                      <p className="text-sm text-yellow-700 mt-1">Assigned residents, daily care notes, basic information</p>
                      <div className="mt-2 flex flex-wrap gap-1">
                        <span className="px-2 py-1 text-xs bg-yellow-200 text-yellow-800 rounded">Assigned Residents</span>
                        <span className="px-2 py-1 text-xs bg-yellow-200 text-yellow-800 rounded">Care Notes</span>
                        <span className="px-2 py-1 text-xs bg-yellow-200 text-yellow-800 rounded">Basic Info</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border border-gray-200 rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">Security Controls</h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <div className="flex items-center">
                        <KeyIcon className="h-5 w-5 text-gray-500 mr-3" />
                        <span className="text-sm font-medium">Multi-Factor Authentication</span>
                      </div>
                      <span className="text-sm text-green-600">Enabled</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <div className="flex items-center">
                        <ClockIcon className="h-5 w-5 text-gray-500 mr-3" />
                        <span className="text-sm font-medium">Session Timeout</span>
                      </div>
                      <span className="text-sm text-gray-600">15 minutes</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <div className="flex items-center">
                        <ServerIcon className="h-5 w-5 text-gray-500 mr-3" />
                        <span className="text-sm font-medium">Data Encryption</span>
                      </div>
                      <span className="text-sm text-green-600">AES-256</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <div className="flex items-center">
                        <GlobeAltIcon className="h-5 w-5 text-gray-500 mr-3" />
                        <span className="text-sm font-medium">IP Restrictions</span>
                      </div>
                      <span className="text-sm text-green-600">Active</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'privacy' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Privacy & Consent Management</h3>

              <div className="space-y-4">
                {privacyConsents.map((consent, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <UserGroupIcon className="h-5 w-5 text-blue-500" />
                          <div>
                            <h4 className="font-medium text-gray-900">{consent.residentName}</h4>
                            <p className="text-sm text-gray-600 capitalize">
                              {consent.consentType.replace('_', ' ')} Consent
                            </p>
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(consent.status)}`}>
                            {consent.status.toUpperCase()}
                          </span>
                        </div>

                        <div className="mt-3 grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm font-medium text-gray-700">Purpose</p>
                            <p className="text-sm text-gray-600">{consent.consentDetails.purpose}</p>
                          </div>

                          <div>
                            <p className="text-sm font-medium text-gray-700">Data Types</p>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {consent.consentDetails.dataTypes.map((type, idx) => (
                                <span key={idx} className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                                  {type}
                                </span>
                              ))}
                            </div>
                          </div>

                          <div>
                            <p className="text-sm font-medium text-gray-700">Granted Date</p>
                            <p className="text-sm text-gray-600">{consent.grantedDate}</p>
                          </div>

                          <div>
                            <p className="text-sm font-medium text-gray-700">Documented By</p>
                            <p className="text-sm text-gray-600">{consent.documentedBy}</p>
                          </div>
                        </div>
                      </div>

                      <div className="ml-4">
                        <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                          <EyeIcon className="h-4 w-4 inline mr-1" />
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedTab === 'incidents' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Security Incidents</h3>
                <button className="px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700">
                  Report Incident
                </button>
              </div>

              <div className="space-y-4">
                {securityIncidents.map((incident) => (
                  <div key={incident.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {incident.incidentType.replace('_', ' ').toUpperCase()}
                            </h4>
                            <p className="text-sm text-gray-600">
                              Incident ID: {incident.id} • Reported: {new Date(incident.reportedDate).toLocaleDateString()}
                            </p>
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(incident.status)}`}>
                            {incident.status.replace('_', ' ').toUpperCase()}
                          </span>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskLevelColor(incident.severity)}`}>
                            {incident.severity.toUpperCase()}
                          </span>
                        </div>

                        <div className="mt-4">
                          <p className="text-sm text-gray-800">{incident.description}</p>

                          <div className="mt-3 grid grid-cols-1 sm:grid-cols-3 gap-4">
                            <div>
                              <p className="text-sm font-medium text-gray-700">Affected Records</p>
                              <p className="text-sm text-gray-600">{incident.affectedRecords}</p>
                            </div>

                            <div>
                              <p className="text-sm font-medium text-gray-700">Affected Residents</p>
                              <p className="text-sm text-gray-600">{incident.affectedResidents}</p>
                            </div>

                            <div>
                              <p className="text-sm font-medium text-gray-700">Reported By</p>
                              <p className="text-sm text-gray-600">{incident.reportedBy}</p>
                            </div>
                          </div>

                          {incident.containmentActions.length > 0 && (
                            <div className="mt-3">
                              <p className="text-sm font-medium text-gray-700">Containment Actions</p>
                              <ul className="text-sm text-gray-600 list-disc list-inside">
                                {incident.containmentActions.map((action, idx) => (
                                  <li key={idx}>{action}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="ml-4">
                        <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                          <EyeIcon className="h-4 w-4 inline mr-1" />
                          View Full Report
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedTab === 'training' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">HIPAA Training & Compliance</h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="border border-gray-200 rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">Training Modules</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded">
                      <div>
                        <p className="font-medium text-gray-900">HIPAA Fundamentals</p>
                        <p className="text-sm text-gray-600">Basic privacy and security rules</p>
                      </div>
                      <span className="text-sm text-green-600">100% Complete</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-green-50 rounded">
                      <div>
                        <p className="font-medium text-gray-900">Breach Response</p>
                        <p className="text-sm text-gray-600">Incident reporting and response</p>
                      </div>
                      <span className="text-sm text-green-600">95% Complete</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded">
                      <div>
                        <p className="font-medium text-gray-900">Access Controls</p>
                        <p className="text-sm text-gray-600">User permissions and authentication</p>
                      </div>
                      <span className="text-sm text-yellow-600">85% Complete</span>
                    </div>
                  </div>
                </div>

                <div className="border border-gray-200 rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">Compliance Assessments</h4>
                  <div className="space-y-3">
                    <div className="p-3 bg-blue-50 rounded">
                      <div className="flex justify-between items-center">
                        <p className="font-medium text-gray-900">Annual Risk Assessment</p>
                        <span className="text-sm text-blue-600">Due: March 2024</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">Comprehensive security risk evaluation</p>
                    </div>

                    <div className="p-3 bg-green-50 rounded">
                      <div className="flex justify-between items-center">
                        <p className="font-medium text-gray-900">Quarterly Review</p>
                        <span className="text-sm text-green-600">Completed</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">Policy and procedure review</p>
                    </div>

                    <div className="p-3 bg-purple-50 rounded">
                      <div className="flex justify-between items-center">
                        <p className="font-medium text-gray-900">Penetration Testing</p>
                        <span className="text-sm text-purple-600">Scheduled</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">External security assessment</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HIPAACompliance;