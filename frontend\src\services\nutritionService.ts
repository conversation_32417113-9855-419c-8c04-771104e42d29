import { api } from './api';

// Types for Nutrition Service
export interface NutritionalProfile {
  residentId: string;
  residentName: string;
  age: number;
  gender: 'male' | 'female';
  height: number; // cm
  weight: number; // kg
  bmi: number;
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'active';
  medicalConditions: string[];
  currentMedications: string[];
  allergies: string[];
  dietaryRestrictions: string[];
  therapeuticDiet?: string;
  nutritionalGoals: string[];
  dailyCalorieTarget: number;
  macroTargets: {
    protein: number; // grams
    carbs: number; // grams
    fat: number; // grams
    fiber: number; // grams
  };
  micronutrientNeeds: {
    sodium: number; // mg
    potassium: number; // mg
    calcium: number; // mg
    iron: number; // mg
    vitaminD: number; // IU
    vitaminB12: number; // mcg
  };
  fluidRestriction?: number; // ml/day
  textureModification?: 'regular' | 'minced' | 'pureed' | 'liquid';
  lastAssessment: string;
  weightTrend: 'gaining' | 'stable' | 'losing';
  riskFactors: string[];
}

export interface MedicationNutritionInteraction {
  medicationName: string;
  genericName: string;
  interactionType: 'avoid' | 'timing' | 'enhance' | 'monitor' | 'supplement';
  affectedNutrients: string[];
  affectedFoods: string[];
  mechanism: string;
  clinicalSignificance: 'low' | 'moderate' | 'high' | 'severe';
  recommendations: {
    dietary: string[];
    timing: string[];
    monitoring: string[];
  };
  contraindications: string[];
  alternatives?: string[];
}

export interface MealPlan {
  id: string;
  residentId: string;
  planDate: string;
  planType: 'daily' | 'weekly' | 'monthly';
  meals: {
    [mealTime: string]: MealItem[];
  };
  nutritionalSummary: {
    totalCalories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sodium: number;
    potassium: number;
    calcium: number;
  };
  medicationConsiderations: string[];
  aiOptimizations: string[];
  complianceScore: number; // 0-100
  approvedBy?: string;
  approvedDate?: string;
}

export interface MealItem {
  id: string;
  name: string;
  category: 'protein' | 'carbohydrate' | 'vegetable' | 'fruit' | 'dairy' | 'fat' | 'beverage';
  portion: string;
  weight: number; // grams
  calories: number;
  nutrients: {
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sodium: number;
    potassium: number;
    calcium: number;
    iron: number;
    vitaminC: number;
    vitaminD: number;
  };
  allergens: string[];
  textureModified: boolean;
  medicationInteractions: string[];
  preparationNotes?: string;
}

export interface NutritionalAssessment {
  id: string;
  residentId: string;
  assessmentDate: string;
  assessorName: string;
  assessmentType: 'admission' | 'quarterly' | 'annual' | 'change_in_condition';
  anthropometrics: {
    weight: number;
    height: number;
    bmi: number;
    weightChange: number; // kg change from last assessment
    idealBodyWeight: number;
  };
  nutritionalStatus: {
    albumin?: number;
    prealbumin?: number;
    hemoglobin?: number;
    totalProtein?: number;
    cholesterol?: number;
    glucose?: number;
  };
  functionalStatus: {
    independentEating: boolean;
    assistanceLevel: 'independent' | 'setup' | 'partial' | 'total';
    swallowingDifficulty: boolean;
    appetiteLevel: 'poor' | 'fair' | 'good' | 'excellent';
    dentalProblems: boolean;
  };
  riskFactors: string[];
  interventions: string[];
  goals: string[];
  nextAssessmentDate: string;
  aiRecommendations: string[];
}

class NutritionService {
  // Get Nutritional Profile
  async getNutritionalProfile(residentId: string): Promise<NutritionalProfile> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockProfile: NutritionalProfile = {
            residentId,
            residentName: 'Margaret Thompson',
            age: 78,
            gender: 'female',
            height: 162,
            weight: 68.5,
            bmi: 26.1,
            activityLevel: 'light',
            medicalConditions: ['Type 2 Diabetes', 'Hypertension', 'Osteoporosis'],
            currentMedications: ['Metformin', 'Lisinopril', 'Calcium Carbonate'],
            allergies: ['Shellfish', 'Tree nuts'],
            dietaryRestrictions: ['Low sodium', 'Diabetic diet'],
            therapeuticDiet: 'Cardiac Diet',
            nutritionalGoals: ['Blood sugar control', 'Weight maintenance', 'Bone health'],
            dailyCalorieTarget: 1800,
            macroTargets: {
              protein: 90, // 20% of calories
              carbs: 225, // 50% of calories
              fat: 60, // 30% of calories
              fiber: 25
            },
            micronutrientNeeds: {
              sodium: 2000,
              potassium: 3500,
              calcium: 1200,
              iron: 8,
              vitaminD: 800,
              vitaminB12: 2.4
            },
            fluidRestriction: 2000,
            lastAssessment: '2024-01-15',
            weightTrend: 'stable',
            riskFactors: ['Age >75', 'Multiple medications', 'Chronic conditions']
          };
          resolve(mockProfile);
        }, 1000);
      });
    }

    try {
      const response = await api.get<NutritionalProfile>(`/nutrition/profile/${residentId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to get nutritional profile');
    }
  }

  // Analyze Medication-Nutrition Interactions
  async analyzeMedicationInteractions(medications: string[]): Promise<MedicationNutritionInteraction[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockInteractions: MedicationNutritionInteraction[] = [
            {
              medicationName: 'Warfarin',
              genericName: 'warfarin sodium',
              interactionType: 'avoid',
              affectedNutrients: ['Vitamin K'],
              affectedFoods: ['Leafy greens', 'Broccoli', 'Brussels sprouts', 'Liver'],
              mechanism: 'Vitamin K antagonizes warfarin anticoagulant effects',
              clinicalSignificance: 'high',
              recommendations: {
                dietary: ['Maintain consistent vitamin K intake', 'Limit large portions of high vitamin K foods'],
                timing: ['No specific timing requirements'],
                monitoring: ['Monitor INR levels closely', 'Watch for bleeding signs']
              },
              contraindications: ['Vitamin K supplements', 'Large amounts of leafy greens']
            },
            {
              medicationName: 'Metformin',
              genericName: 'metformin hydrochloride',
              interactionType: 'enhance',
              affectedNutrients: ['Vitamin B12', 'Folate'],
              affectedFoods: ['High-carbohydrate meals'],
              mechanism: 'Take with food to reduce GI side effects and improve tolerance',
              clinicalSignificance: 'moderate',
              recommendations: {
                dietary: ['Take with meals', 'Limit simple carbohydrates', 'Include B12-rich foods'],
                timing: ['Take with breakfast and dinner', 'Avoid on empty stomach'],
                monitoring: ['Monitor B12 levels annually', 'Watch for GI symptoms']
              },
              contraindications: ['Excessive alcohol consumption']
            }
          ];
          resolve(mockInteractions);
        }, 1500);
      });
    }

    try {
      const response = await api.post<MedicationNutritionInteraction[]>('/nutrition/medication-interactions', { medications });
      return response.data;
    } catch (error) {
      throw new Error('Failed to analyze medication interactions');
    }
  }

  // Generate AI-Optimized Meal Plan
  async generateMealPlan(residentId: string, preferences?: {
    duration: 'daily' | 'weekly' | 'monthly';
    mealTypes: string[];
    excludeFoods: string[];
    emphasizeNutrients: string[];
  }): Promise<MealPlan> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockMealPlan: MealPlan = {
            id: `mp_${Date.now()}`,
            residentId,
            planDate: new Date().toISOString().split('T')[0],
            planType: preferences?.duration || 'daily',
            meals: {
              breakfast: [
                {
                  id: 'b1',
                  name: 'Steel-cut oatmeal with berries',
                  category: 'carbohydrate',
                  portion: '1 cup',
                  weight: 240,
                  calories: 150,
                  nutrients: {
                    protein: 5,
                    carbs: 27,
                    fat: 3,
                    fiber: 4,
                    sodium: 200,
                    potassium: 164,
                    calcium: 20,
                    iron: 2,
                    vitaminC: 10,
                    vitaminD: 0
                  },
                  allergens: [],
                  textureModified: false,
                  medicationInteractions: ['Compatible with Metformin - high fiber helps blood sugar control']
                }
              ],
              lunch: [
                {
                  id: 'l1',
                  name: 'Grilled salmon with herbs',
                  category: 'protein',
                  portion: '4 oz',
                  weight: 113,
                  calories: 206,
                  nutrients: {
                    protein: 28,
                    carbs: 0,
                    fat: 9,
                    fiber: 0,
                    sodium: 300,
                    potassium: 628,
                    calcium: 20,
                    iron: 1,
                    vitaminC: 0,
                    vitaminD: 360
                  },
                  allergens: ['Fish'],
                  textureModified: false,
                  medicationInteractions: ['Omega-3 supports heart health with Lisinopril therapy']
                }
              ]
            },
            nutritionalSummary: {
              totalCalories: 1785,
              protein: 89,
              carbs: 201,
              fat: 58,
              fiber: 28,
              sodium: 1890,
              potassium: 3200,
              calcium: 1150
            },
            medicationConsiderations: [
              'Metformin: Take with breakfast and dinner to reduce GI side effects',
              'Lisinopril: Monitor potassium intake - current plan provides adequate but not excessive potassium',
              'Calcium Carbonate: Take with vitamin D-rich foods for better absorption'
            ],
            aiOptimizations: [
              'Increased fiber content to support blood sugar control',
              'Omega-3 rich foods for cardiovascular health',
              'Calcium-rich foods strategically placed with vitamin D sources',
              'Portion sizes optimized for weight maintenance goals'
            ],
            complianceScore: 94
          };
          resolve(mockMealPlan);
        }, 2500);
      });
    }

    try {
      const response = await api.post<MealPlan>('/nutrition/generate-meal-plan', { residentId, preferences });
      return response.data;
    } catch (error) {
      throw new Error('Failed to generate meal plan');
    }
  }

  // Nutritional Assessment
  async createNutritionalAssessment(assessment: Partial<NutritionalAssessment>): Promise<NutritionalAssessment> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockAssessment: NutritionalAssessment = {
            id: `na_${Date.now()}`,
            residentId: assessment.residentId || 'R001',
            assessmentDate: new Date().toISOString().split('T')[0],
            assessorName: 'Sarah Johnson, RD',
            assessmentType: assessment.assessmentType || 'quarterly',
            anthropometrics: {
              weight: 68.5,
              height: 162,
              bmi: 26.1,
              weightChange: -0.5,
              idealBodyWeight: 65
            },
            nutritionalStatus: {
              albumin: 3.8,
              prealbumin: 22,
              hemoglobin: 12.5,
              totalProtein: 7.2,
              cholesterol: 180,
              glucose: 145
            },
            functionalStatus: {
              independentEating: true,
              assistanceLevel: 'setup',
              swallowingDifficulty: false,
              appetiteLevel: 'good',
              dentalProblems: false
            },
            riskFactors: ['Age >75', 'Multiple medications', 'Diabetes'],
            interventions: ['Continue diabetic diet', 'Monitor weight weekly', 'Encourage fluid intake'],
            goals: ['Maintain current weight', 'Improve blood sugar control', 'Prevent malnutrition'],
            nextAssessmentDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            aiRecommendations: [
              'Consider increasing protein intake to 1.2g/kg body weight',
              'Monitor for vitamin B12 deficiency due to Metformin use',
              'Optimize meal timing with medication schedule'
            ]
          };
          resolve(mockAssessment);
        }, 2000);
      });
    }

    try {
      const response = await api.post<NutritionalAssessment>('/nutrition/assessment', assessment);
      return response.data;
    } catch (error) {
      throw new Error('Failed to create nutritional assessment');
    }
  }

  // Get Food Database for Meal Planning
  async searchFoods(query: string, filters?: {
    category?: string;
    allergenFree?: string[];
    textureModified?: boolean;
  }): Promise<MealItem[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockFoods: MealItem[] = [
            {
              id: 'f1',
              name: 'Grilled chicken breast',
              category: 'protein',
              portion: '4 oz',
              weight: 113,
              calories: 185,
              nutrients: {
                protein: 35,
                carbs: 0,
                fat: 4,
                fiber: 0,
                sodium: 300,
                potassium: 256,
                calcium: 15,
                iron: 1,
                vitaminC: 0,
                vitaminD: 0
              },
              allergens: [],
              textureModified: false,
              medicationInteractions: []
            }
          ];
          resolve(mockFoods);
        }, 800);
      });
    }

    try {
      const response = await api.get<MealItem[]>('/nutrition/foods/search', { params: { query, ...filters } });
      return response.data;
    } catch (error) {
      throw new Error('Failed to search foods');
    }
  }

  // Analyze Nutritional Intake
  async analyzeNutritionalIntake(residentId: string, dateRange: { start: string; end: string }): Promise<{
    averageDailyIntake: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
      fiber: number;
      sodium: number;
    };
    complianceScore: number;
    deficiencies: string[];
    excesses: string[];
    recommendations: string[];
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            averageDailyIntake: {
              calories: 1750,
              protein: 85,
              carbs: 195,
              fat: 62,
              fiber: 22,
              sodium: 2100
            },
            complianceScore: 87,
            deficiencies: ['Vitamin D', 'Fiber'],
            excesses: ['Sodium'],
            recommendations: [
              'Increase vitamin D-rich foods or consider supplementation',
              'Add more high-fiber foods like beans and whole grains',
              'Reduce sodium intake by limiting processed foods'
            ]
          });
        }, 1500);
      });
    }

    try {
      const response = await api.post('/nutrition/analyze-intake', { residentId, dateRange });
      return response.data;
    } catch (error) {
      throw new Error('Failed to analyze nutritional intake');
    }
  }
}

export const nutritionService = new NutritionService();
