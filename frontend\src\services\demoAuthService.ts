// Demo Authentication Service for CareSyncAI
// Handles demo account authentication and session management

import { 
  DemoUser, 
  validateDemoCredentials, 
  isDemoAccount, 
  generateDemoToken, 
  validateDemoToken,
  simulateNetworkDelay,
  getDemoUserByEmail
} from '../utils/demoAccounts';

export interface DemoAuthResponse {
  success: boolean;
  user?: DemoUser;
  token?: string;
  error?: string;
}

interface DemoSession {
  user: DemoUser;
  token: string;
  expires_at: number;
}

class DemoAuthService {
  private static instance: DemoAuthService;
  private currentSession: DemoSession | null = null;

  private constructor() {
    // Load session from localStorage on initialization
    this.loadSessionFromStorage();
  }

  public static getInstance(): DemoAuthService {
    if (!DemoAuthService.instance) {
      DemoAuthService.instance = new DemoAuthService();
    }
    return DemoAuthService.instance;
  }

  // Login with demo credentials
  public async login(email: string, password: string): Promise<DemoAuthResponse> {
    try {
      // Simulate network delay
      await simulateNetworkDelay(800);

      // Check if it's a demo account
      if (!isDemoAccount(email)) {
        return {
          success: false,
          error: 'Not a demo account. Please use regular authentication.'
        };
      }

      // Validate credentials
      const user = validateDemoCredentials(email, password);
      
      if (!user) {
        return {
          success: false,
          error: 'Invalid demo credentials. Please check email and password.'
        };
      }

      // Generate session token
      const token = generateDemoToken(user);
      const expires_at = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

      // Create session
      const session: DemoSession = {
        user,
        token,
        expires_at
      };

      // Store session
      this.currentSession = session;
      this.saveSessionToStorage(session);

      console.log('🎭 Demo Login Successful:', {
        user: `${user.first_name} ${user.last_name}`,
        role: user.role,
        email: user.email
      });

      return {
        success: true,
        user,
        token
      };

    } catch (error) {
      console.error('❌ Demo login error:', error);
      return {
        success: false,
        error: 'Login failed. Please try again.'
      };
    }
  }

  // Get current session
  public getCurrentSession(): DemoSession | null {
    if (!this.currentSession) {
      return null;
    }

    // Check if session is expired
    if (this.currentSession.expires_at < Date.now()) {
      this.logout();
      return null;
    }

    return this.currentSession;
  }

  // Get current user
  public getCurrentUser(): DemoUser | null {
    const session = this.getCurrentSession();
    return session?.user || null;
  }

  // Check if user is logged in
  public isLoggedIn(): boolean {
    return this.getCurrentSession() !== null;
  }

  // Logout
  public logout(): void {
    this.currentSession = null;
    this.clearSessionFromStorage();
    
    console.log('🚪 Demo logout successful');
  }

  // Validate session token
  public validateSession(token: string): DemoUser | null {
    const user = validateDemoToken(token);
    
    if (user && this.currentSession?.token === token) {
      return user;
    }
    
    return null;
  }

  // Switch user role (for demo purposes)
  public switchRole(newRole: string): boolean {
    if (!this.currentSession) {
      return false;
    }

    // Find demo user with the new role
    let newUserEmail = '';
    switch (newRole) {
      case 'admin':
        newUserEmail = '<EMAIL>';
        break;
      case 'supervisor':
        newUserEmail = '<EMAIL>';
        break;
      case 'caregiver':
        newUserEmail = '<EMAIL>';
        break;
      case 'billing':
        newUserEmail = '<EMAIL>';
        break;
      default:
        return false;
    }

    const newUser = getDemoUserByEmail(newUserEmail);
    
    if (!newUser) {
      return false;
    }

    // Update current session
    this.currentSession.user = newUser;
    this.currentSession.token = generateDemoToken(newUser);
    this.saveSessionToStorage(this.currentSession);

    console.log('🔄 Demo role switched:', {
      newRole: newUser.role,
      user: `${newUser.first_name} ${newUser.last_name}`
    });

    return true;
  }

  // Save session to localStorage
  private saveSessionToStorage(session: DemoSession): void {
    try {
      localStorage.setItem('caresyncai_demo_session', JSON.stringify(session));
    } catch (error) {
      console.error('Error saving demo session:', error);
    }
  }

  // Load session from localStorage
  private loadSessionFromStorage(): void {
    try {
      const sessionData = localStorage.getItem('caresyncai_demo_session');
      
      if (sessionData) {
        const session: DemoSession = JSON.parse(sessionData);
        
        // Check if session is still valid
        if (session.expires_at > Date.now()) {
          this.currentSession = session;
          
          console.log('🔄 Demo session restored:', {
            user: `${session.user.first_name} ${session.user.last_name}`,
            role: session.user.role
          });
        } else {
          this.clearSessionFromStorage();
        }
      }
    } catch (error) {
      console.error('Error loading demo session:', error);
      this.clearSessionFromStorage();
    }
  }

  // Clear session from localStorage
  private clearSessionFromStorage(): void {
    try {
      localStorage.removeItem('caresyncai_demo_session');
    } catch (error) {
      console.error('Error clearing demo session:', error);
    }
  }

  // Get session info for debugging
  public getSessionInfo(): any {
    const session = this.getCurrentSession();
    
    if (!session) {
      return { status: 'No active session' };
    }

    return {
      status: 'Active session',
      user: {
        name: `${session.user.first_name} ${session.user.last_name}`,
        email: session.user.email,
        role: session.user.role,
        title: session.user.title
      },
      expires_in: Math.round((session.expires_at - Date.now()) / (1000 * 60 * 60)), // hours
      token_preview: session.token.substring(0, 20) + '...'
    };
  }
}

// Export singleton instance
export const demoAuthService = DemoAuthService.getInstance();

// Export types
export type { DemoUser, DemoSession };

// Utility functions
export const isDemoMode = (): boolean => {
  return process.env.NODE_ENV === 'development' || window.location.hostname.includes('demo');
};

export const getDemoLoginUrl = (): string => {
  return '/login?demo=true';
};

export const formatDemoCredentials = () => {
  return {
    admin: '<EMAIL> / admin123',
    supervisor: '<EMAIL> / nurse123',
    caregiver: '<EMAIL> / care123',
    billing: '<EMAIL> / billing123'
  };
};
