import React from 'react';

const Inventory: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Inventory</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage medical supplies and equipment
        </p>
      </div>

      <div className="card">
        <div className="card-body">
          <div className="text-center py-12">
            <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-gray-400 text-xl">📦</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Smart Inventory Management
            </h3>
            <p className="text-gray-600 mb-4">
              AI-driven inventory tracking with automated reorder alerts.
            </p>
            <p className="text-sm text-gray-500">
              Features: Stock levels, reorder points, supplier management,
              usage analytics, and predictive restocking.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Inventory;
