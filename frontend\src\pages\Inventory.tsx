import React, { useState } from 'react';
import {
  CubeIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  PlusIcon,
  PencilIcon,
  BeakerIcon,
} from '@heroicons/react/24/outline';

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  minimumStock: number;
  maximumStock: number;
  unit: string;
  location: string;
  lastUpdated: string;
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'overstocked';
  cost: number;
  supplier: string;
  expirationDate?: string;
}

const Inventory: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showAddItemModal, setShowAddItemModal] = useState(false);

  // Mock inventory data
  const inventoryItems: InventoryItem[] = [
    {
      id: 'INV001',
      name: 'Disposable Gloves (Nitrile)',
      category: 'Medical Supplies',
      currentStock: 45,
      minimumStock: 50,
      maximumStock: 200,
      unit: 'boxes',
      location: 'Medical Storage Room A',
      lastUpdated: '2024-01-15',
      status: 'low_stock',
      cost: 12.50,
      supplier: 'MedSupply Co.',
      expirationDate: '2025-06-30'
    },
    {
      id: 'INV002',
      name: 'Hand Sanitizer',
      category: 'Cleaning Supplies',
      currentStock: 8,
      minimumStock: 15,
      maximumStock: 50,
      unit: 'bottles',
      location: 'Cleaning Closet B',
      lastUpdated: '2024-01-14',
      status: 'low_stock',
      cost: 8.75,
      supplier: 'CleanCorp',
    },
    {
      id: 'INV003',
      name: 'Bed Sheets (Twin)',
      category: 'Linens',
      currentStock: 120,
      minimumStock: 80,
      maximumStock: 150,
      unit: 'sets',
      location: 'Linen Storage',
      lastUpdated: '2024-01-15',
      status: 'in_stock',
      cost: 25.00,
      supplier: 'LinenPro',
    },
    {
      id: 'INV004',
      name: 'Blood Pressure Monitors',
      category: 'Medical Equipment',
      currentStock: 12,
      minimumStock: 8,
      maximumStock: 15,
      unit: 'units',
      location: 'Medical Equipment Room',
      lastUpdated: '2024-01-13',
      status: 'in_stock',
      cost: 89.99,
      supplier: 'MedTech Solutions',
    },
    {
      id: 'INV005',
      name: 'Toilet Paper',
      category: 'Personal Care',
      currentStock: 0,
      minimumStock: 20,
      maximumStock: 100,
      unit: 'packs',
      location: 'Storage Room C',
      lastUpdated: '2024-01-15',
      status: 'out_of_stock',
      cost: 15.50,
      supplier: 'EssentialSupplies',
    }
  ];

  const categories = ['all', ...Array.from(new Set(inventoryItems.map(item => item.category)))];

  const filteredItems = selectedCategory === 'all'
    ? inventoryItems
    : inventoryItems.filter(item => item.category === selectedCategory);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock':
        return 'bg-green-100 text-green-800';
      case 'low_stock':
        return 'bg-yellow-100 text-yellow-800';
      case 'out_of_stock':
        return 'bg-red-100 text-red-800';
      case 'overstocked':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in_stock':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'low_stock':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'out_of_stock':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'overstocked':
        return <ArrowTrendingUpIcon className="h-5 w-5 text-blue-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const lowStockItems = inventoryItems.filter(item => item.status === 'low_stock' || item.status === 'out_of_stock');
  const totalValue = inventoryItems.reduce((sum, item) => sum + (item.currentStock * item.cost), 0);

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="relative overflow-hidden bg-gradient-to-br from-indigo-900 via-purple-900 to-blue-900 rounded-2xl p-8 text-white shadow-2xl">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-amber-400 to-yellow-500 rounded-full opacity-10 transform translate-x-32 -translate-y-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-400 to-purple-400 rounded-full opacity-10 transform -translate-x-24 translate-y-24"></div>

        <div className="relative z-10 flex justify-between items-start">
          <div>
            <div className="flex items-center mb-4">
              <div className="p-3 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-xl mr-4 shadow-lg">
                <CubeIcon className="h-8 w-8 text-purple-900" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                  Care-SolAI Inventory Hub
                </h1>
                <p className="text-blue-200 text-lg font-medium">Smart Inventory & Asset Management</p>
              </div>
            </div>
            <p className="text-blue-100 max-w-2xl leading-relaxed">
              AI-driven inventory tracking with automated reorder alerts, predictive analytics, and comprehensive
              asset management designed for healthcare facilities.
            </p>

            {/* Feature Highlights */}
            <div className="mt-6 flex flex-wrap gap-3">
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg">
                <BeakerIcon className="h-4 w-4 mr-2" />
                Predictive Analytics
              </span>
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg">
                <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                Smart Alerts
              </span>
              <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-amber-500 to-yellow-500 text-purple-900 shadow-lg">
                <ChartBarIcon className="h-4 w-4 mr-2" />
                Real-time Tracking
              </span>
            </div>
          </div>

          {/* Enhanced Add Item Button */}
          <button
            onClick={() => setShowAddItemModal(true)}
            className="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 to-yellow-500 text-purple-900 font-bold rounded-xl hover:from-yellow-400 hover:to-amber-400 transform hover:scale-105 transition-all duration-200 shadow-xl"
          >
            <PlusIcon className="h-6 w-6 mr-3 group-hover:rotate-90 transition-transform duration-200" />
            <div className="text-left">
              <div className="text-sm">Add New Item</div>
              <div className="text-xs opacity-80">Expand inventory</div>
            </div>
          </button>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Items Card */}
        <div className="group relative overflow-hidden bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-blue-400 to-transparent rounded-full opacity-20 transform translate-x-6 -translate-y-6"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-xl shadow-lg">
                <CubeIcon className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">{inventoryItems.length}</div>
                <div className="text-blue-200 text-sm font-medium">Total Items</div>
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-blue-100 text-sm">Across all categories</div>
              <div className="w-full bg-blue-800 rounded-full h-2">
                <div className="bg-gradient-to-r from-blue-300 to-blue-400 h-2 rounded-full transition-all duration-500 w-4/5"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Low Stock Alerts Card */}
        <div className="group relative overflow-hidden bg-gradient-to-br from-amber-500 via-yellow-600 to-orange-600 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-yellow-400 to-transparent rounded-full opacity-20 transform translate-x-6 -translate-y-6"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-xl shadow-lg">
                <ExclamationTriangleIcon className="h-6 w-6 text-amber-900" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">{lowStockItems.length}</div>
                <div className="text-yellow-100 text-sm font-medium">Low Stock Alerts</div>
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-yellow-100 text-sm">Require immediate attention</div>
              <div className="w-full bg-yellow-700 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-red-400 to-red-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${Math.min((lowStockItems.length / inventoryItems.length) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Total Value Card */}
        <div className="group relative overflow-hidden bg-gradient-to-br from-purple-600 via-purple-700 to-violet-800 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-purple-400 to-transparent rounded-full opacity-20 transform translate-x-6 -translate-y-6"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-xl shadow-lg">
                <ChartBarIcon className="h-6 w-6 text-purple-900" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">${totalValue.toLocaleString()}</div>
                <div className="text-purple-200 text-sm font-medium">Total Value</div>
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-purple-100 text-sm">Current inventory worth</div>
              <div className="w-full bg-purple-800 rounded-full h-2">
                <div className="bg-gradient-to-r from-amber-400 to-yellow-500 h-2 rounded-full transition-all duration-500 w-3/4"></div>
              </div>
            </div>
          </div>
        </div>

        {/* AI Predictions Card */}
        <div className="group relative overflow-hidden bg-gradient-to-br from-indigo-600 via-blue-700 to-purple-800 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-indigo-400 to-transparent rounded-full opacity-20 transform translate-x-6 -translate-y-6"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-purple-400 to-purple-500 rounded-xl shadow-lg">
                <BeakerIcon className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">{lowStockItems.length + 3}</div>
                <div className="text-indigo-200 text-sm font-medium">AI Predictions</div>
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-indigo-100 text-sm">Reorder recommendations</div>
              <div className="w-full bg-indigo-800 rounded-full h-2">
                <div className="bg-gradient-to-r from-purple-300 to-purple-400 h-2 rounded-full transition-all duration-500 w-2/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Filter by Category</h3>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1 rounded-full text-sm font-medium ${
                selectedCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category === 'all' ? 'All Categories' : category}
            </button>
          ))}
        </div>
      </div>

      {/* Low Stock Alerts */}
      {lowStockItems.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 mr-2" />
            <h3 className="text-lg font-medium text-yellow-900">Low Stock Alerts</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {lowStockItems.map((item) => (
              <div key={item.id} className="bg-white p-4 rounded border border-yellow-200">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{item.name}</h4>
                  {getStatusIcon(item.status)}
                </div>
                <p className="text-sm text-gray-600 mb-1">Current: {item.currentStock} {item.unit}</p>
                <p className="text-sm text-gray-600 mb-2">Minimum: {item.minimumStock} {item.unit}</p>
                <button className="w-full px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700">
                  Reorder Now
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Inventory Table */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Inventory Items ({filteredItems.length})
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Item
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredItems.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{item.name}</div>
                      <div className="text-sm text-gray-500">{item.supplier}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {item.currentStock} / {item.maximumStock} {item.unit}
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className={`h-2 rounded-full ${
                          item.currentStock <= item.minimumStock
                            ? 'bg-red-600'
                            : item.currentStock <= item.minimumStock * 1.5
                            ? 'bg-yellow-600'
                            : 'bg-green-600'
                        }`}
                        style={{
                          width: `${Math.min((item.currentStock / item.maximumStock) * 100, 100)}%`
                        }}
                      ></div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                      {item.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.location}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${(item.currentStock * item.cost).toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button className="text-blue-600 hover:text-blue-900">
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        <PencilIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* AI Insights */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <div className="flex items-center mb-4">
          <BeakerIcon className="h-6 w-6 text-purple-600 mr-2" />
          <h3 className="text-lg font-medium text-purple-900">AI Inventory Insights</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded border border-purple-200">
            <div className="flex items-center mb-2">
              <ArrowTrendingUpIcon className="h-5 w-5 text-green-600 mr-2" />
              <h4 className="font-medium text-gray-900">Usage Trend</h4>
            </div>
            <p className="text-sm text-gray-600">Medical supplies usage increased 15% this month</p>
          </div>
          <div className="bg-white p-4 rounded border border-purple-200">
            <div className="flex items-center mb-2">
              <ClockIcon className="h-5 w-5 text-blue-600 mr-2" />
              <h4 className="font-medium text-gray-900">Reorder Prediction</h4>
            </div>
            <p className="text-sm text-gray-600">Gloves will need reordering in 3 days based on usage patterns</p>
          </div>
          <div className="bg-white p-4 rounded border border-purple-200">
            <div className="flex items-center mb-2">
              <ArrowTrendingDownIcon className="h-5 w-5 text-orange-600 mr-2" />
              <h4 className="font-medium text-gray-900">Cost Optimization</h4>
            </div>
            <p className="text-sm text-gray-600">Switch to bulk purchasing for 12% savings on cleaning supplies</p>
          </div>
        </div>
      </div>

      {/* Add Item Modal */}
      {showAddItemModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddItemModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <PlusIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Add New Inventory Item
                      </h3>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAddItemModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Item Name</label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Enter item name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Category</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                      <option>Medical Supplies</option>
                      <option>Cleaning Supplies</option>
                      <option>Linens</option>
                      <option>Medical Equipment</option>
                      <option>Personal Care</option>
                    </select>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Current Stock</label>
                      <input
                        type="number"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Unit</label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="boxes, bottles, etc."
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Minimum Stock</label>
                      <input
                        type="number"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Maximum Stock</label>
                      <input
                        type="number"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="0"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Location</label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Storage location"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Cost per Unit</label>
                      <input
                        type="number"
                        step="0.01"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Supplier</label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="Supplier name"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Item added successfully!');
                    setShowAddItemModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Add Item
                </button>
                <button
                  onClick={() => setShowAddItemModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Inventory;
