{"mappings": ";;;;;;;;;AAEO,SAAS,0CAAW,KAAe;IACxC,MAAM,SAAS,CAAA,GAAA,oBAAM,EAAE,KAAK,CAAC;IAC7B,MAAM,QAAQ,SAAS,MAAM,CAAC,EAAE,EAAE;IAClC,IAAI,SAAS,IACX,OAAO;IAET,gCAAgC;IAChC,OAAO,QAAQ,SAAS;AAC1B", "sources": ["packages/@react-aria/utils/src/inertValue.ts"], "sourcesContent": ["import {version} from 'react';\n\nexport function inertValue(value?: boolean): string | boolean | undefined {\n  const pieces = version.split('.');\n  const major = parseInt(pieces[0], 10);\n  if (major >= 19) {\n    return value;\n  }\n  // compatibility with React < 19\n  return value ? 'true' : undefined;\n}\n"], "names": [], "version": 3, "file": "inertValue.main.js.map"}