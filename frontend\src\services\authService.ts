import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    role: string;
  };
}

export interface RegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role?: string;
  license_number?: string;
  license_expiry?: string;
}

export interface UserProfile {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  role: string;
  license_number?: string;
  license_expiry?: string;
  is_active: boolean;
  hire_date?: string;
  certifications?: string[];
}

class AuthService {
  async login(email: string, password: string): Promise<LoginResponse> {
    // Check if we're in mock mode
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';

    if (isMockMode) {
      // Mock authentication for testing
      const validCredentials = [
        {
          email: '<EMAIL>',
          password: 'admin123',
          user: {
            id: 'mock-admin-id',
            email: '<EMAIL>',
            first_name: 'Admin',
            last_name: 'User',
            role: 'admin'
          }
        },
        {
          email: '<EMAIL>',
          password: 'nurse123',
          user: {
            id: 'mock-nurse-id',
            email: '<EMAIL>',
            first_name: 'Sarah',
            last_name: 'Johnson',
            role: 'nurse'
          }
        },
        {
          email: '<EMAIL>',
          password: 'care123',
          user: {
            id: 'mock-caregiver-id',
            email: '<EMAIL>',
            first_name: 'John',
            last_name: 'Smith',
            role: 'caregiver'
          }
        }
      ];

      const validUser = validCredentials.find(
        cred => cred.email === email && cred.password === password
      );

      if (validUser) {
        const mockToken = `mock-token-${validUser.user.role}-${Date.now()}`;
        localStorage.setItem('access_token', mockToken);

        return {
          access_token: mockToken,
          token_type: 'bearer',
          expires_in: 3600,
          user: validUser.user
        };
      } else {
        throw new Error('Invalid credentials. Try: <EMAIL> / admin123');
      }
    }

    // Real API authentication
    try {
      const response = await api.post<LoginResponse>('/auth/login', {
        email,
        password,
      });

      const { access_token } = response.data;
      localStorage.setItem('access_token', access_token);

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || 'Login failed'
      );
    }
  }

  async register(userData: RegisterRequest): Promise<void> {
    try {
      await api.post('/auth/register', userData);
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || 'Registration failed'
      );
    }
  }

  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API call failed:', error);
    } finally {
      localStorage.removeItem('access_token');
    }
  }

  async getCurrentUser(): Promise<UserProfile> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';

    if (isMockMode) {
      const token = localStorage.getItem('access_token');
      if (!token || !token.startsWith('mock-token-')) {
        throw new Error('Not authenticated');
      }

      // Extract role from mock token
      const role = token.split('-')[2];
      const mockUsers = {
        admin: {
          id: 'mock-admin-id',
          first_name: 'Admin',
          last_name: 'User',
          email: '<EMAIL>',
          phone: '******-0123',
          role: 'admin',
          is_active: true,
          hire_date: '2023-01-01',
          certifications: ['Healthcare Administration', 'HIPAA Compliance']
        },
        nurse: {
          id: 'mock-nurse-id',
          first_name: 'Sarah',
          last_name: 'Johnson',
          email: '<EMAIL>',
          phone: '******-0124',
          role: 'nurse',
          license_number: 'RN123456',
          license_expiry: '2025-12-31',
          is_active: true,
          hire_date: '2023-02-15',
          certifications: ['RN License', 'CPR Certified', 'Medication Administration']
        },
        caregiver: {
          id: 'mock-caregiver-id',
          first_name: 'John',
          last_name: 'Smith',
          email: '<EMAIL>',
          phone: '******-0125',
          role: 'caregiver',
          license_number: 'CG789012',
          license_expiry: '2024-12-31',
          is_active: true,
          hire_date: '2023-03-01',
          certifications: ['First Aid', 'Patient Care']
        }
      };

      return mockUsers[role as keyof typeof mockUsers] || mockUsers.caregiver;
    }

    try {
      const response = await api.get<UserProfile>('/auth/me');
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || 'Failed to get user profile'
      );
    }
  }

  async updateProfile(data: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const response = await api.put<{ user: UserProfile }>('/auth/me', data);
      return response.data.user;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || 'Failed to update profile'
      );
    }
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      await api.post('/auth/change-password', {
        current_password: currentPassword,
        new_password: newPassword,
      });
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || 'Failed to change password'
      );
    }
  }

  async resetPassword(email: string): Promise<void> {
    try {
      await api.post('/auth/reset-password', { email });
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || 'Failed to send reset email'
      );
    }
  }

  async refreshToken(): Promise<string> {
    try {
      const response = await api.post<{ access_token: string }>('/auth/refresh');
      const { access_token } = response.data;
      localStorage.setItem('access_token', access_token);
      return access_token;
    } catch (error: any) {
      localStorage.removeItem('access_token');
      throw new Error(
        error.response?.data?.detail || 'Failed to refresh token'
      );
    }
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  }

  getToken(): string | null {
    return localStorage.getItem('access_token');
  }
}

export const authService = new AuthService();
export { api };
