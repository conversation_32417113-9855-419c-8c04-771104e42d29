{"ast": null, "code": "\"use client\";\n\n// src/useIsFetching.ts\nimport * as React from \"react\";\nimport { notifyManager } from \"@tanstack/query-core\";\nimport { useQueryClient } from \"./QueryClientProvider.js\";\nfunction useIsFetching(filters, queryClient) {\n  const client = useQueryClient(queryClient);\n  const queryCache = client.getQueryCache();\n  return React.useSyncExternalStore(React.useCallback(onStoreChange => queryCache.subscribe(notifyManager.batchCalls(onStoreChange)), [queryCache]), () => client.isFetching(filters), () => client.isFetching(filters));\n}\nexport { useIsFetching };", "map": {"version": 3, "names": ["React", "notify<PERSON><PERSON>ger", "useQueryClient", "useIsFetching", "filters", "queryClient", "client", "queryCache", "get<PERSON><PERSON><PERSON><PERSON>ache", "useSyncExternalStore", "useCallback", "onStoreChange", "subscribe", "batchCalls", "isFetching"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@tanstack\\react-query\\src\\useIsFetching.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport { notifyManager } from '@tanstack/query-core'\n\nimport { useQueryClient } from './QueryClientProvider'\nimport type { QueryClient, QueryFilters } from '@tanstack/query-core'\n\nexport function useIsFetching(\n  filters?: QueryFilters,\n  queryClient?: QueryClient,\n): number {\n  const client = useQueryClient(queryClient)\n  const queryCache = client.getQueryCache()\n\n  return React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        queryCache.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [queryCache],\n    ),\n    () => client.isFetching(filters),\n    () => client.isFetching(filters),\n  )\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AACvB,SAASC,aAAA,QAAqB;AAE9B,SAASC,cAAA,QAAsB;AAGxB,SAASC,cACdC,OAAA,EACAC,WAAA,EACQ;EACR,MAAMC,MAAA,GAASJ,cAAA,CAAeG,WAAW;EACzC,MAAME,UAAA,GAAaD,MAAA,CAAOE,aAAA,CAAc;EAExC,OAAaR,KAAA,CAAAS,oBAAA,CACLT,KAAA,CAAAU,WAAA,CACHC,aAAA,IACCJ,UAAA,CAAWK,SAAA,CAAUX,aAAA,CAAcY,UAAA,CAAWF,aAAa,CAAC,GAC9D,CAACJ,UAAU,CACb,GACA,MAAMD,MAAA,CAAOQ,UAAA,CAAWV,OAAO,GAC/B,MAAME,MAAA,CAAOQ,UAAA,CAAWV,OAAO,CACjC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}