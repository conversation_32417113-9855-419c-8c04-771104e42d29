{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\medisyn\\\\frontend\\\\src\\\\contexts\\\\SupabaseContext.tsx\",\n  _s = $RefreshSig$();\nimport React, { createContext, useContext } from 'react';\nimport { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'your-supabase-url';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-supabase-anon-key';\n\n// Create Supabase client\nconst supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Context type\n\n// Create context\nconst SupabaseContext = /*#__PURE__*/createContext(undefined);\n\n// Provider component\n\nexport const SupabaseProvider = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(SupabaseContext.Provider, {\n    value: {\n      supabase\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook to use Supabase\n_c = SupabaseProvider;\nexport const useSupabase = () => {\n  _s();\n  const context = useContext(SupabaseContext);\n  if (context === undefined) {\n    throw new Error('useSupabase must be used within a SupabaseProvider');\n  }\n  return context;\n};\n\n// Export the client for direct use\n_s(useSupabase, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport { supabase };\nvar _c;\n$RefreshReg$(_c, \"SupabaseProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "createClient", "jsxDEV", "_jsxDEV", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "supabase", "SupabaseContext", "undefined", "SupabaseProvider", "children", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useSupabase", "_s", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/src/contexts/SupabaseContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, ReactNode } from 'react';\nimport { createClient, SupabaseClient } from '@supabase/supabase-js';\n\n// Supabase configuration\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'your-supabase-url';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'your-supabase-anon-key';\n\n// Create Supabase client\nconst supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Context type\ninterface SupabaseContextType {\n  supabase: SupabaseClient;\n}\n\n// Create context\nconst SupabaseContext = createContext<SupabaseContextType | undefined>(undefined);\n\n// Provider component\ninterface SupabaseProviderProps {\n  children: ReactNode;\n}\n\nexport const SupabaseProvider: React.FC<SupabaseProviderProps> = ({ children }) => {\n  return (\n    <SupabaseContext.Provider value={{ supabase }}>\n      {children}\n    </SupabaseContext.Provider>\n  );\n};\n\n// Hook to use Supabase\nexport const useSupabase = (): SupabaseContextType => {\n  const context = useContext(SupabaseContext);\n  if (context === undefined) {\n    throw new Error('useSupabase must be used within a SupabaseProvider');\n  }\n  return context;\n};\n\n// Export the client for direct use\nexport { supabase };\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,QAAmB,OAAO;AACnE,SAASC,YAAY,QAAwB,uBAAuB;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,mBAAmB;AAC7E,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,wBAAwB;;AAE3F;AACA,MAAMC,QAAQ,GAAGT,YAAY,CAACG,WAAW,EAAEI,eAAe,CAAC;;AAE3D;;AAKA;AACA,MAAMG,eAAe,gBAAGZ,aAAa,CAAkCa,SAAS,CAAC;;AAEjF;;AAKA,OAAO,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACjF,oBACEX,OAAA,CAACQ,eAAe,CAACI,QAAQ;IAACC,KAAK,EAAE;MAAEN;IAAS,CAAE;IAAAI,QAAA,EAC3CA;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE/B,CAAC;;AAED;AAAAC,EAAA,GARaR,gBAAiD;AAS9D,OAAO,MAAMS,WAAW,GAAGA,CAAA,KAA2B;EAAAC,EAAA;EACpD,MAAMC,OAAO,GAAGxB,UAAU,CAACW,eAAe,CAAC;EAC3C,IAAIa,OAAO,KAAKZ,SAAS,EAAE;IACzB,MAAM,IAAIa,KAAK,CAAC,oDAAoD,CAAC;EACvE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,WAAW;AASxB,SAASZ,QAAQ;AAAG,IAAAW,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}