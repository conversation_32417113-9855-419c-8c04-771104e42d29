"""
AI predictions API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import uuid
import logging

from services.ai_service import AIService
from core.auth import get_current_user, check_patient_access
from core.database import DatabaseService

router = APIRouter()
ai_service = AIService()
logger = logging.getLogger(__name__)

class PredictionRequest(BaseModel):
    patient_id: uuid.UUID
    prediction_type: str  # 'fall_risk', 'health_decline', 'medication_adherence'
    force_refresh: bool = False

class NoteAnalysisRequest(BaseModel):
    note_text: str
    patient_id: Optional[uuid.UUID] = None

@router.get("/patient/{patient_id}")
async def get_patient_predictions(
    patient_id: uuid.UUID,
    current_user: dict = Depends(get_current_user)
):
    """Get all AI predictions for a patient"""
    # Check patient access
    if not check_patient_access(
        current_user["caregiver_id"],
        str(patient_id),
        current_user["role"]
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient"
        )

    try:
        predictions = await ai_service.get_patient_predictions(str(patient_id))
        return {
            "patient_id": str(patient_id),
            "predictions": predictions,
            "total_predictions": len(predictions)
        }
    except Exception as e:
        logger.error(f"Error getting patient predictions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve predictions: {str(e)}"
        )

@router.post("/predict")
async def create_prediction(
    prediction_request: PredictionRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Create a new AI prediction"""
    # Check patient access
    if not check_patient_access(
        current_user["caregiver_id"],
        str(prediction_request.patient_id),
        current_user["role"]
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient"
        )

    try:
        # Get patient data
        patient_data = await ai_service.get_patient_data_for_ai(str(prediction_request.patient_id))

        if not patient_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient not found"
            )

        # Generate prediction based on type
        if prediction_request.prediction_type == "fall_risk":
            prediction = await ai_service.predict_fall_risk(
                patient_data,
                force_refresh=prediction_request.force_refresh
            )
        elif prediction_request.prediction_type == "health_decline":
            prediction = await ai_service.predict_health_decline(
                patient_data,
                force_refresh=prediction_request.force_refresh
            )
        elif prediction_request.prediction_type == "medication_adherence":
            prediction = await ai_service.predict_medication_adherence(
                patient_data,
                force_refresh=prediction_request.force_refresh
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported prediction type: {prediction_request.prediction_type}"
            )

        # Save prediction to database in background
        background_tasks.add_task(
            ai_service.save_prediction,
            prediction,
            current_user["caregiver_id"]
        )

        return prediction
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating prediction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create prediction: {str(e)}"
        )

@router.get("/fall-risk/{patient_id}")
async def get_fall_risk_prediction(
    patient_id: uuid.UUID,
    current_user: dict = Depends(get_current_user)
):
    """Get fall risk prediction for a patient"""
    # Check patient access
    if not check_patient_access(
        current_user["caregiver_id"],
        str(patient_id),
        current_user["role"]
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient"
        )

    try:
        # Get patient data
        patient_data = await ai_service.get_patient_data_for_ai(str(patient_id))

        if not patient_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient not found"
            )

        # Get or generate fall risk prediction
        prediction = await ai_service.predict_fall_risk(patient_data)

        return prediction
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting fall risk prediction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get fall risk prediction: {str(e)}"
        )

@router.post("/analyze-note")
async def analyze_caregiver_note(
    note_request: NoteAnalysisRequest,
    current_user: dict = Depends(get_current_user)
):
    """Analyze caregiver note using NLP"""
    try:
        # Check patient access if patient_id is provided
        if note_request.patient_id:
            if not check_patient_access(
                current_user["caregiver_id"],
                str(note_request.patient_id),
                current_user["role"]
            ):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this patient"
                )

        # Analyze the note
        analysis = await ai_service.analyze_caregiver_note(
            note_request.note_text,
            str(note_request.patient_id) if note_request.patient_id else None
        )

        return analysis
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing note: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze note: {str(e)}"
        )

@router.get("/health-trends/{patient_id}")
async def get_health_trends(
    patient_id: uuid.UUID,
    days: int = 30,
    current_user: dict = Depends(get_current_user)
):
    """Get health trend analysis for a patient"""
    # Check patient access
    if not check_patient_access(
        current_user["caregiver_id"],
        str(patient_id),
        current_user["role"]
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient"
        )

    try:
        trends = await ai_service.analyze_health_trends(str(patient_id), days)
        return trends
    except Exception as e:
        logger.error(f"Error getting health trends: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get health trends: {str(e)}"
        )

@router.get("/insights/dashboard")
async def get_ai_dashboard_insights(
    current_user: dict = Depends(get_current_user)
):
    """Get AI insights for dashboard"""
    try:
        insights = await ai_service.get_dashboard_insights(
            current_user["caregiver_id"],
            current_user["role"]
        )
        return insights
    except Exception as e:
        logger.error(f"Error getting dashboard insights: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get dashboard insights: {str(e)}"
        )

@router.post("/batch-predictions")
async def generate_batch_predictions(
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Generate predictions for all patients (admin only)"""
    if current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can generate batch predictions"
        )

    try:
        # Start batch prediction process in background
        background_tasks.add_task(
            ai_service.generate_batch_predictions,
            current_user["caregiver_id"]
        )

        return {
            "message": "Batch prediction generation started",
            "status": "processing"
        }
    except Exception as e:
        logger.error(f"Error starting batch predictions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start batch predictions: {str(e)}"
        )
