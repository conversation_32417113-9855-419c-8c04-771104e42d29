-- Seed data for CareSyncAI development and testing

-- Insert sample caregivers
INSERT INTO caregivers (id, first_name, last_name, email, phone, role, license_number, license_expiry, address, emergency_contact, certifications) VALUES
(
    '550e8400-e29b-41d4-a716-446655440001',
    '<PERSON>',
    '<PERSON>',
    '<EMAIL>',
    '555-0101',
    'admin',
    'RN123456',
    '2025-12-31',
    '{"street": "123 Admin St", "city": "Healthcare City", "state": "CA", "zip": "90210"}',
    '{"name": "<PERSON>", "phone": "555-0102", "relationship": "spouse"}',
    ARRAY['RN', 'CPR', 'First Aid']
),
(
    '550e8400-e29b-41d4-a716-446655440002',
    '<PERSON>',
    '<PERSON>',
    'micha<PERSON>.<EMAIL>',
    '555-0201',
    'supervisor',
    'LPN789012',
    '2025-06-30',
    '{"street": "456 Supervisor Ave", "city": "Healthcare City", "state": "CA", "zip": "90211"}',
    '{"name": "<PERSON> <PERSON>", "phone": "555-0202", "relationship": "spouse"}',
    ARRAY['LPN', 'CPR', 'Medication Administration']
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    'Emily',
    'Rodriguez',
    '<EMAIL>',
    '555-0301',
    'caregiver',
    'CNA345678',
    '2024-12-31',
    '{"street": "789 Caregiver Ln", "city": "Healthcare City", "state": "CA", "zip": "90212"}',
    '{"name": "Carlos Rodriguez", "phone": "555-0302", "relationship": "brother"}',
    ARRAY['CNA', 'CPR', 'Dementia Care']
),
(
    '550e8400-e29b-41d4-a716-446655440004',
    'David',
    'Wilson',
    '<EMAIL>',
    '555-0401',
    'caregiver',
    'CNA901234',
    '2025-03-31',
    '{"street": "321 Helper St", "city": "Healthcare City", "state": "CA", "zip": "90213"}',
    '{"name": "Mary Wilson", "phone": "555-0402", "relationship": "mother"}',
    ARRAY['CNA', 'CPR', 'Physical Therapy Assistant']
),
(
    '550e8400-e29b-41d4-a716-446655440005',
    'Jennifer',
    'Brown',
    '<EMAIL>',
    '555-0501',
    'billing',
    NULL,
    NULL,
    '{"street": "654 Finance Rd", "city": "Healthcare City", "state": "CA", "zip": "90214"}',
    '{"name": "Robert Brown", "phone": "555-0502", "relationship": "husband"}',
    ARRAY['Medical Billing', 'HIPAA Compliance']
);

-- Insert sample patients
INSERT INTO patients (id, first_name, last_name, date_of_birth, gender, phone, email, address, emergency_contacts, insurance_info, primary_caregiver_id, backup_caregiver_id, medical_conditions, allergies, medications, care_level, mobility_score, cognitive_score) VALUES
(
    '660e8400-e29b-41d4-a716-446655440001',
    'Margaret',
    'Thompson',
    '1935-03-15',
    'Female',
    '555-1001',
    '<EMAIL>',
    '{"street": "123 Oak Street", "city": "Hometown", "state": "CA", "zip": "90301", "apartment": "2A"}',
    '[{"name": "Robert Thompson", "phone": "555-1002", "relationship": "son", "is_primary": true}, {"name": "Susan Miller", "phone": "555-1003", "relationship": "daughter", "is_primary": false}]',
    '{"provider": "Medicare", "policy_number": "1234567890A", "group_number": "GRP001", "secondary": {"provider": "Blue Cross", "policy_number": "BC987654321"}}',
    '550e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440004',
    ARRAY['diabetes', 'hypertension', 'arthritis'],
    ARRAY['penicillin', 'shellfish'],
    '[{"name": "Metformin", "dosage": "500mg", "frequency": "twice daily", "time": ["08:00", "20:00"]}, {"name": "Lisinopril", "dosage": "10mg", "frequency": "once daily", "time": ["08:00"]}]',
    3,
    6,
    8
),
(
    '660e8400-e29b-41d4-a716-446655440002',
    'William',
    'Anderson',
    '1940-07-22',
    'Male',
    '555-2001',
    '<EMAIL>',
    '{"street": "456 Pine Avenue", "city": "Hometown", "state": "CA", "zip": "90302"}',
    '[{"name": "Patricia Anderson", "phone": "555-2002", "relationship": "wife", "is_primary": true}, {"name": "James Anderson", "phone": "555-2003", "relationship": "son", "is_primary": false}]',
    '{"provider": "Medicare", "policy_number": "2345678901B", "group_number": "GRP002"}',
    '550e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440004',
    ARRAY['parkinsons', 'depression'],
    ARRAY['latex'],
    '[{"name": "Carbidopa-Levodopa", "dosage": "25-100mg", "frequency": "three times daily", "time": ["08:00", "14:00", "20:00"]}, {"name": "Sertraline", "dosage": "50mg", "frequency": "once daily", "time": ["08:00"]}]',
    4,
    4,
    7
),
(
    '660e8400-e29b-41d4-a716-446655440003',
    'Dorothy',
    'Garcia',
    '1938-11-08',
    'Female',
    '555-3001',
    '<EMAIL>',
    '{"street": "789 Maple Drive", "city": "Hometown", "state": "CA", "zip": "90303", "apartment": "B"}',
    '[{"name": "Maria Garcia", "phone": "555-3002", "relationship": "daughter", "is_primary": true}, {"name": "Carlos Garcia", "phone": "555-3003", "relationship": "son", "is_primary": false}]',
    '{"provider": "Medicaid", "policy_number": "3456789012C", "group_number": "GRP003"}',
    '550e8400-e29b-41d4-a716-446655440004',
    '550e8400-e29b-41d4-a716-446655440003',
    ARRAY['dementia', 'osteoporosis'],
    ARRAY['aspirin'],
    '[{"name": "Donepezil", "dosage": "10mg", "frequency": "once daily", "time": ["20:00"]}, {"name": "Calcium", "dosage": "600mg", "frequency": "twice daily", "time": ["08:00", "20:00"]}]',
    5,
    3,
    4
);

-- Insert sample medical records
INSERT INTO medical_records (patient_id, caregiver_id, vitals, symptoms, medications_given, activities_performed, mood_assessment, pain_level, notes) VALUES
(
    '660e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440003',
    '{"heart_rate": 78, "systolic_bp": 135, "diastolic_bp": 85, "temperature": 98.6, "oxygen_saturation": 97, "weight": 145}',
    ARRAY['mild fatigue'],
    '[{"name": "Metformin", "dosage": "500mg", "time_given": "08:00", "given": true}, {"name": "Lisinopril", "dosage": "10mg", "time_given": "08:00", "given": true}]',
    ARRAY['morning walk', 'breakfast assistance', 'medication administration'],
    'cheerful',
    2,
    'Patient in good spirits today. Blood sugar levels stable. Completed morning routine without assistance.'
),
(
    '660e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440003',
    '{"heart_rate": 72, "systolic_bp": 128, "diastolic_bp": 82, "temperature": 98.4, "oxygen_saturation": 96}',
    ARRAY['mild tremor', 'stiffness'],
    '[{"name": "Carbidopa-Levodopa", "dosage": "25-100mg", "time_given": "08:00", "given": true}]',
    ARRAY['physical therapy exercises', 'medication administration'],
    'calm',
    3,
    'Tremor slightly more pronounced this morning. Responded well to medication. Completed PT exercises with minimal assistance.'
),
(
    '660e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440004',
    '{"heart_rate": 85, "systolic_bp": 140, "diastolic_bp": 90, "temperature": 98.8, "oxygen_saturation": 95}',
    ARRAY['confusion', 'restlessness'],
    '[{"name": "Donepezil", "dosage": "10mg", "time_given": "20:00", "given": false, "reason": "patient refused"}]',
    ARRAY['personal care assistance', 'meal preparation'],
    'agitated',
    1,
    'Patient more confused than usual. Refused evening medication. Family notified. Will attempt medication administration later.'
);

-- Insert sample inventory items
INSERT INTO shop_inventory (item_name, item_code, category, description, current_quantity, reorder_level, max_quantity, unit_cost, supplier_info, location) VALUES
('Disposable Gloves (Nitrile)', 'GLV-NIT-L', 'PPE', 'Large size nitrile gloves, powder-free', 150, 50, 500, 12.99, '{"supplier": "MedSupply Co", "contact": "555-9001", "lead_time_days": 3}', 'Supply Closet A'),
('Blood Pressure Cuffs', 'BP-CUFF-STD', 'Medical Equipment', 'Standard adult blood pressure cuffs', 8, 3, 15, 45.00, '{"supplier": "HealthTech Inc", "contact": "555-9002", "lead_time_days": 7}', 'Equipment Room'),
('Thermometer Covers', 'THERM-COV', 'Medical Supplies', 'Disposable thermometer probe covers', 200, 25, 1000, 8.50, '{"supplier": "MedSupply Co", "contact": "555-9001", "lead_time_days": 3}', 'Supply Closet A'),
('Wound Dressing Kits', 'WOUND-KIT-STD', 'Medical Supplies', 'Standard wound care dressing kits', 25, 10, 50, 15.75, '{"supplier": "WoundCare Plus", "contact": "555-9003", "lead_time_days": 5}', 'Medical Cabinet'),
('Hand Sanitizer (8oz)', 'SANITIZER-8OZ', 'Hygiene', 'Alcohol-based hand sanitizer, 8oz bottles', 30, 15, 100, 3.25, '{"supplier": "CleanCare Corp", "contact": "555-9004", "lead_time_days": 2}', 'Supply Closet B'),
('Medication Cups', 'MED-CUP-1OZ', 'Medication', 'Disposable 1oz medication cups', 500, 100, 2000, 0.05, '{"supplier": "PharmSupply Ltd", "contact": "555-9005", "lead_time_days": 4}', 'Medication Room'),
('Adult Diapers (Large)', 'DIAPER-L', 'Personal Care', 'Adult incontinence briefs, large size', 40, 20, 100, 18.99, '{"supplier": "ComfortCare Inc", "contact": "555-9006", "lead_time_days": 3}', 'Personal Care Storage'),
('Pulse Oximeters', 'OXIMETER-FINGER', 'Medical Equipment', 'Fingertip pulse oximeters', 5, 2, 10, 89.99, '{"supplier": "HealthTech Inc", "contact": "555-9002", "lead_time_days": 7}', 'Equipment Room');

-- Insert sample billing records
INSERT INTO billing (patient_id, caregiver_id, invoice_number, billing_period_start, billing_period_end, services_provided, hours_worked, hourly_rate, subtotal, tax_amount, total_amount, status, due_date) VALUES
(
    '660e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440003',
    'INV-2024-001',
    '2024-01-01',
    '2024-01-07',
    '[{"service": "Personal Care", "hours": 14, "rate": 25.00}, {"service": "Medication Management", "hours": 7, "rate": 30.00}, {"service": "Meal Preparation", "hours": 7, "rate": 20.00}]',
    28.0,
    25.00,
    700.00,
    56.00,
    756.00,
    'sent',
    '2024-02-01'
),
(
    '660e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440003',
    'INV-2024-002',
    '2024-01-01',
    '2024-01-07',
    '[{"service": "Personal Care", "hours": 21, "rate": 25.00}, {"service": "Physical Therapy Assistance", "hours": 7, "rate": 35.00}]',
    28.0,
    27.50,
    770.00,
    61.60,
    831.60,
    'paid',
    '2024-02-01'
);

-- Insert sample care schedules
INSERT INTO care_schedules (patient_id, caregiver_id, scheduled_date, start_time, end_time, service_type, status) VALUES
('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440003', CURRENT_DATE + INTERVAL '1 day', '08:00', '12:00', 'Morning Care', 'scheduled'),
('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440003', CURRENT_DATE + INTERVAL '1 day', '18:00', '20:00', 'Evening Care', 'scheduled'),
('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', CURRENT_DATE + INTERVAL '1 day', '09:00', '13:00', 'Morning Care + PT', 'scheduled'),
('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440004', CURRENT_DATE + INTERVAL '1 day', '10:00', '14:00', 'Personal Care', 'scheduled');

-- Insert sample AI predictions
INSERT INTO ai_predictions (patient_id, prediction_type, prediction_value, confidence_score, factors, recommendation, model_version, expires_at) VALUES
(
    '660e8400-e29b-41d4-a716-446655440001',
    'fall_risk',
    0.35,
    0.82,
    '{"age_factor": 0.15, "mobility_score": 0.10, "medication_count": 0.05, "medical_conditions": 0.05}',
    'Monitor mobility closely. Consider physical therapy evaluation.',
    'v1.2.0',
    NOW() + INTERVAL '30 days'
),
(
    '660e8400-e29b-41d4-a716-446655440002',
    'fall_risk',
    0.68,
    0.91,
    '{"age_factor": 0.18, "mobility_score": 0.24, "parkinsons": 0.20, "medication_effects": 0.06}',
    'High fall risk. Implement fall prevention measures immediately. Consider home safety assessment.',
    'v1.2.0',
    NOW() + INTERVAL '30 days'
),
(
    '660e8400-e29b-41d4-a716-446655440003',
    'medication_adherence',
    0.45,
    0.76,
    '{"cognitive_decline": 0.30, "medication_refusal_history": 0.15}',
    'Medication adherence concerns. Consider alternative administration methods or family involvement.',
    'v1.1.0',
    NOW() + INTERVAL '14 days'
);
