import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const Profile: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Personal Information</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label className="form-label">First Name</label>
              <input
                type="text"
                className="form-input"
                value={user?.first_name || ''}
                readOnly
              />
            </div>
            <div>
              <label className="form-label">Last Name</label>
              <input
                type="text"
                className="form-input"
                value={user?.last_name || ''}
                readOnly
              />
            </div>
            <div>
              <label className="form-label">Email</label>
              <input
                type="email"
                className="form-input"
                value={user?.email || ''}
                readOnly
              />
            </div>
            <div>
              <label className="form-label">Role</label>
              <input
                type="text"
                className="form-input capitalize"
                value={user?.role || ''}
                readOnly
              />
            </div>
          </div>
          
          <div className="mt-6 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              Profile editing functionality will be implemented in the next phase.
              This includes updating personal information, changing passwords,
              managing certifications, and notification preferences.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
