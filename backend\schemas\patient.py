"""
Pydantic schemas for Patient models
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import date, datetime
from enum import Enum
import uuid

class PatientStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DISCHARGED = "discharged"
    DECEASED = "deceased"

class Gender(str, Enum):
    MALE = "Male"
    FEMALE = "Female"
    OTHER = "Other"
    PREFER_NOT_TO_SAY = "Prefer not to say"

class Address(BaseModel):
    street: str = Field(..., min_length=1, max_length=255)
    city: str = Field(..., min_length=1, max_length=100)
    state: str = Field(..., min_length=2, max_length=50)
    zip: str = Field(..., min_length=5, max_length=10)
    apartment: Optional[str] = Field(None, max_length=50)
    
    class Config:
        schema_extra = {
            "example": {
                "street": "123 Main Street",
                "city": "Hometown",
                "state": "CA",
                "zip": "90210",
                "apartment": "2A"
            }
        }

class EmergencyContact(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    phone: str = Field(..., min_length=10, max_length=20)
    relationship: str = Field(..., min_length=1, max_length=50)
    is_primary: bool = False
    
    class Config:
        schema_extra = {
            "example": {
                "name": "John Doe",
                "phone": "555-0123",
                "relationship": "son",
                "is_primary": True
            }
        }

class InsuranceInfo(BaseModel):
    provider: str = Field(..., min_length=1, max_length=100)
    policy_number: str = Field(..., min_length=1, max_length=50)
    group_number: Optional[str] = Field(None, max_length=50)
    secondary: Optional[Dict[str, Any]] = None
    
    class Config:
        schema_extra = {
            "example": {
                "provider": "Medicare",
                "policy_number": "1234567890A",
                "group_number": "GRP001",
                "secondary": {
                    "provider": "Blue Cross",
                    "policy_number": "BC987654321"
                }
            }
        }

class Medication(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    dosage: str = Field(..., min_length=1, max_length=50)
    frequency: str = Field(..., min_length=1, max_length=100)
    time: List[str] = Field(default_factory=list)
    instructions: Optional[str] = Field(None, max_length=500)
    prescriber: Optional[str] = Field(None, max_length=200)
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Metformin",
                "dosage": "500mg",
                "frequency": "twice daily",
                "time": ["08:00", "20:00"],
                "instructions": "Take with food",
                "prescriber": "Dr. Smith"
            }
        }

class PatientBase(BaseModel):
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    date_of_birth: date
    gender: Optional[Gender] = None
    phone: Optional[str] = Field(None, min_length=10, max_length=20)
    email: Optional[str] = Field(None, max_length=255)
    address: Address
    emergency_contacts: List[EmergencyContact] = Field(default_factory=list)
    insurance_info: Optional[InsuranceInfo] = None
    medical_conditions: List[str] = Field(default_factory=list)
    allergies: List[str] = Field(default_factory=list)
    medications: List[Medication] = Field(default_factory=list)
    care_level: int = Field(default=1, ge=1, le=5)
    mobility_score: int = Field(default=5, ge=1, le=10)
    cognitive_score: int = Field(default=10, ge=1, le=10)
    notes: Optional[str] = Field(None, max_length=2000)
    
    @validator('date_of_birth')
    def validate_date_of_birth(cls, v):
        if v > date.today():
            raise ValueError('Date of birth cannot be in the future')
        if v < date(1900, 1, 1):
            raise ValueError('Date of birth cannot be before 1900')
        return v
    
    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v
    
    @validator('emergency_contacts')
    def validate_emergency_contacts(cls, v):
        if not v:
            raise ValueError('At least one emergency contact is required')
        primary_contacts = [contact for contact in v if contact.is_primary]
        if len(primary_contacts) != 1:
            raise ValueError('Exactly one primary emergency contact is required')
        return v

class PatientCreate(PatientBase):
    primary_caregiver_id: Optional[uuid.UUID] = None
    backup_caregiver_id: Optional[uuid.UUID] = None
    admission_date: Optional[date] = None

class PatientUpdate(BaseModel):
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone: Optional[str] = Field(None, min_length=10, max_length=20)
    email: Optional[str] = Field(None, max_length=255)
    address: Optional[Address] = None
    emergency_contacts: Optional[List[EmergencyContact]] = None
    insurance_info: Optional[InsuranceInfo] = None
    primary_caregiver_id: Optional[uuid.UUID] = None
    backup_caregiver_id: Optional[uuid.UUID] = None
    status: Optional[PatientStatus] = None
    medical_conditions: Optional[List[str]] = None
    allergies: Optional[List[str]] = None
    medications: Optional[List[Medication]] = None
    care_level: Optional[int] = Field(None, ge=1, le=5)
    mobility_score: Optional[int] = Field(None, ge=1, le=10)
    cognitive_score: Optional[int] = Field(None, ge=1, le=10)
    notes: Optional[str] = Field(None, max_length=2000)
    discharge_date: Optional[date] = None

class PatientResponse(PatientBase):
    id: uuid.UUID
    patient_number: str
    status: PatientStatus
    admission_date: date
    discharge_date: Optional[date] = None
    primary_caregiver_id: Optional[uuid.UUID] = None
    backup_caregiver_id: Optional[uuid.UUID] = None
    fall_risk_score: float
    created_at: datetime
    updated_at: datetime
    
    # Computed fields
    age: Optional[int] = None
    primary_caregiver_name: Optional[str] = None
    backup_caregiver_name: Optional[str] = None
    
    class Config:
        from_attributes = True
        schema_extra = {
            "example": {
                "id": "660e8400-e29b-41d4-a716-************",
                "patient_number": "P20240001",
                "first_name": "Margaret",
                "last_name": "Thompson",
                "date_of_birth": "1935-03-15",
                "gender": "Female",
                "phone": "555-1001",
                "email": "<EMAIL>",
                "address": {
                    "street": "123 Oak Street",
                    "city": "Hometown",
                    "state": "CA",
                    "zip": "90301",
                    "apartment": "2A"
                },
                "status": "active",
                "care_level": 3,
                "mobility_score": 6,
                "cognitive_score": 8,
                "fall_risk_score": 0.35,
                "age": 89
            }
        }

class PatientSummary(BaseModel):
    id: uuid.UUID
    patient_number: str
    first_name: str
    last_name: str
    age: int
    status: PatientStatus
    care_level: int
    fall_risk_score: float
    primary_caregiver_name: Optional[str] = None
    last_visit_date: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class PatientSearchResponse(BaseModel):
    patients: List[PatientSummary]
    total_count: int
    page: int
    page_size: int
    total_pages: int
