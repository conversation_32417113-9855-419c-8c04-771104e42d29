import { api } from './api';

// Types for Staff Service
export interface StaffMember {
  id: string;
  employeeId: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    ssn: string; // encrypted
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
    };
  };
  contactInfo: {
    phone: string;
    email: string;
    emergencyContact: {
      name: string;
      phone: string;
      relationship: string;
    };
  };
  employment: {
    role: 'administrator' | 'nurse' | 'caregiver' | 'medication_aide' | 'activities_coordinator' | 'maintenance' | 'dietary' | 'housekeeping';
    department: string;
    hireDate: string;
    status: 'active' | 'inactive' | 'on_leave' | 'terminated';
    employmentType: 'full_time' | 'part_time' | 'contract' | 'per_diem';
    payRate: number;
    benefits: string[];
  };
  credentials: {
    licenses: Array<{
      type: string;
      number: string;
      issueDate: string;
      expiryDate: string;
      issuingState: string;
      status: 'active' | 'expired' | 'pending_renewal' | 'suspended';
    }>;
    certifications: Array<{
      name: string;
      issuer: string;
      issueDate: string;
      expiryDate: string;
      status: 'active' | 'expired' | 'pending_renewal';
      renewalRequired: boolean;
    }>;
    trainings: Array<{
      name: string;
      provider: string;
      completedDate: string;
      nextDueDate?: string;
      status: 'completed' | 'overdue' | 'upcoming' | 'in_progress';
      mandatory: boolean;
    }>;
    backgroundCheck: {
      completedDate: string;
      expiryDate: string;
      status: 'clear' | 'pending' | 'flagged';
      provider: string;
    };
  };
  schedule: {
    shiftType: 'day' | 'evening' | 'night' | 'day_extended' | 'night_extended' | 'rotating';
    hoursPerWeek: number;
    preferredDays: string[];
    availability: Array<{
      day: string;
      startTime: string;
      endTime: string;
    }>;
    timeOffRequests: Array<{
      startDate: string;
      endDate: string;
      reason: string;
      status: 'pending' | 'approved' | 'denied';
    }>;
  };
  performance: {
    overallRating: number; // 1-5
    metrics: {
      residentSatisfaction: number;
      punctuality: number;
      teamwork: number;
      clinicalSkills: number;
      communication: number;
      reliability: number;
    };
    lastReviewDate: string;
    nextReviewDate: string;
    goals: Array<{
      description: string;
      targetDate: string;
      status: 'not_started' | 'in_progress' | 'completed';
    }>;
    strengths: string[];
    improvementAreas: string[];
    disciplinaryActions: Array<{
      date: string;
      type: 'verbal_warning' | 'written_warning' | 'suspension' | 'termination';
      reason: string;
      description: string;
    }>;
  };
  workload: {
    assignedResidents: string[];
    currentTasks: number;
    avgTasksPerShift: number;
    workloadScore: number; // 1-100
    specializations: string[];
    restrictions: string[];
  };
}

export interface Shift {
  id: string;
  staffMemberId: string;
  date: string;
  startTime: string;
  endTime: string;
  shiftType: 'day' | 'evening' | 'night' | 'day_extended' | 'night_extended';
  status: 'scheduled' | 'in_progress' | 'completed' | 'missed' | 'called_off';
  assignedResidents: string[];
  tasks: Array<{
    id: string;
    description: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    status: 'pending' | 'in_progress' | 'completed' | 'skipped';
    estimatedTime: number; // minutes
    actualTime?: number; // minutes
    notes?: string;
  }>;
  breaks: Array<{
    startTime: string;
    endTime: string;
    type: 'meal' | 'rest';
  }>;
  clockIn?: string;
  clockOut?: string;
  notes?: string;
  supervisorApproval?: {
    approvedBy: string;
    approvedDate: string;
    notes?: string;
  };
}

export interface StaffSchedule {
  weekStartDate: string;
  shifts: Shift[];
  totalHours: number;
  overtimeHours: number;
  staffingLevels: {
    day: { required: number; scheduled: number; actual: number };
    evening: { required: number; scheduled: number; actual: number };
    night: { required: number; scheduled: number; actual: number };
    day_extended: { required: number; scheduled: number; actual: number };
    night_extended: { required: number; scheduled: number; actual: number };
  };
  callOffs: Array<{
    staffMemberId: string;
    shiftId: string;
    reason: string;
    replacementFound: boolean;
  }>;
}

export interface PerformanceReview {
  id: string;
  staffMemberId: string;
  reviewDate: string;
  reviewPeriod: { start: string; end: string };
  reviewer: string;
  type: 'annual' | 'quarterly' | 'probationary' | 'disciplinary';
  ratings: {
    residentCare: number;
    teamwork: number;
    communication: number;
    punctuality: number;
    initiative: number;
    overallPerformance: number;
  };
  achievements: string[];
  goalsFromLastReview: Array<{
    goal: string;
    status: 'achieved' | 'partially_achieved' | 'not_achieved';
    notes?: string;
  }>;
  newGoals: Array<{
    goal: string;
    targetDate: string;
    measurableOutcome: string;
  }>;
  developmentPlan: {
    trainingNeeds: string[];
    careerGoals: string[];
    supportRequired: string[];
  };
  overallComments: string;
  employeeComments?: string;
  actionItems: Array<{
    item: string;
    responsible: string;
    dueDate: string;
  }>;
}

class StaffService {
  // Get All Staff Members
  async getAllStaff(filters?: {
    role?: string;
    department?: string;
    status?: string;
    shiftType?: string;
  }): Promise<StaffMember[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // Mock staff data would be returned here
          resolve([]);
        }, 1000);
      });
    }

    try {
      const response = await api.get<StaffMember[]>('/staff', { params: filters });
      return response.data;
    } catch (error) {
      throw new Error('Failed to get staff members');
    }
  }

  // Get Staff Member by ID
  async getStaffMember(staffId: string): Promise<StaffMember> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // Mock staff member data
          resolve({} as StaffMember);
        }, 800);
      });
    }

    try {
      const response = await api.get<StaffMember>(`/staff/${staffId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to get staff member');
    }
  }

  // Create Staff Member
  async createStaffMember(staffData: Partial<StaffMember>): Promise<StaffMember> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ...staffData,
            id: `staff_${Date.now()}`,
            employeeId: `EMP${Math.floor(Math.random() * 10000)}`
          } as StaffMember);
        }, 1500);
      });
    }

    try {
      const response = await api.post<StaffMember>('/staff', staffData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to create staff member');
    }
  }

  // Update Staff Member
  async updateStaffMember(staffId: string, updates: Partial<StaffMember>): Promise<StaffMember> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ ...updates, id: staffId } as StaffMember);
        }, 1000);
      });
    }

    try {
      const response = await api.put<StaffMember>(`/staff/${staffId}`, updates);
      return response.data;
    } catch (error) {
      throw new Error('Failed to update staff member');
    }
  }

  // Get Staff Schedule
  async getStaffSchedule(weekStartDate: string): Promise<StaffSchedule> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // Mock schedule data
          resolve({
            weekStartDate,
            shifts: [],
            totalHours: 0,
            overtimeHours: 0,
            staffingLevels: {
              day: { required: 8, scheduled: 7, actual: 6 },
              evening: { required: 6, scheduled: 6, actual: 5 },
              night: { required: 4, scheduled: 4, actual: 4 },
              day_extended: { required: 3, scheduled: 3, actual: 3 },
              night_extended: { required: 2, scheduled: 2, actual: 2 }
            },
            callOffs: []
          });
        }, 1200);
      });
    }

    try {
      const response = await api.get<StaffSchedule>(`/staff/schedule/${weekStartDate}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to get staff schedule');
    }
  }

  // Create/Update Shift
  async createShift(shiftData: Partial<Shift>): Promise<Shift> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ...shiftData,
            id: `shift_${Date.now()}`,
            status: 'scheduled'
          } as Shift);
        }, 800);
      });
    }

    try {
      const response = await api.post<Shift>('/staff/shifts', shiftData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to create shift');
    }
  }

  // Clock In/Out
  async clockInOut(staffId: string, action: 'clock_in' | 'clock_out', timestamp?: string): Promise<{
    success: boolean;
    timestamp: string;
    shiftId?: string;
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            timestamp: timestamp || new Date().toISOString(),
            shiftId: `shift_${Date.now()}`
          });
        }, 500);
      });
    }

    try {
      const response = await api.post(`/staff/${staffId}/clock`, { action, timestamp });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to ${action.replace('_', ' ')}`);
    }
  }

  // Performance Review
  async createPerformanceReview(reviewData: Partial<PerformanceReview>): Promise<PerformanceReview> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ...reviewData,
            id: `review_${Date.now()}`
          } as PerformanceReview);
        }, 1500);
      });
    }

    try {
      const response = await api.post<PerformanceReview>('/staff/performance-reviews', reviewData);
      return response.data;
    } catch (error) {
      throw new Error('Failed to create performance review');
    }
  }

  // Get Credential Alerts
  async getCredentialAlerts(): Promise<Array<{
    staffId: string;
    staffName: string;
    credentialType: string;
    credentialName: string;
    expiryDate: string;
    daysUntilExpiry: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([
            {
              staffId: 'S001',
              staffName: 'Sarah Johnson',
              credentialType: 'certification',
              credentialName: 'Medication Administration',
              expiryDate: '2024-03-15',
              daysUntilExpiry: 54,
              severity: 'medium'
            }
          ]);
        }, 800);
      });
    }

    try {
      const response = await api.get('/staff/credential-alerts');
      return response.data;
    } catch (error) {
      throw new Error('Failed to get credential alerts');
    }
  }

  // Generate Staff Reports
  async generateStaffReport(reportType: 'attendance' | 'performance' | 'credentials' | 'workload', 
                           dateRange: { start: string; end: string },
                           filters?: any): Promise<{
    reportData: any;
    summary: any;
    recommendations: string[];
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            reportData: {},
            summary: {
              totalStaff: 24,
              averagePerformance: 4.2,
              attendanceRate: 94.5,
              credentialCompliance: 87.5
            },
            recommendations: [
              'Schedule credential renewal training for 3 staff members',
              'Consider workload redistribution for evening shift',
              'Implement recognition program for top performers'
            ]
          });
        }, 2000);
      });
    }

    try {
      const response = await api.post('/staff/reports', { reportType, dateRange, filters });
      return response.data;
    } catch (error) {
      throw new Error('Failed to generate staff report');
    }
  }
}

export const staffService = new StaffService();
