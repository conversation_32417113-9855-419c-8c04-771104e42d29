<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Care-SolAI - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Mock data
        const mockPatients = [
            {
                id: 1,
                name: "<PERSON>",
                age: 72,
                condition: "Diabetes Type 2",
                riskLevel: "Medium",
                lastVisit: "2024-06-28",
                nextAppointment: "2024-07-05"
            },
            {
                id: 2,
                name: "Mary Johnson",
                age: 68,
                condition: "Hypertension",
                riskLevel: "Low",
                lastVisit: "2024-06-25",
                nextAppointment: "2024-07-10"
            },
            {
                id: 3,
                name: "Robert Davis",
                age: 75,
                condition: "Heart Disease",
                riskLevel: "High",
                lastVisit: "2024-06-30",
                nextAppointment: "2024-07-02"
            }
        ];

        const mockStats = {
            totalPatients: 156,
            activeAlerts: 8,
            todayAppointments: 12,
            monthlyRevenue: 45600
        };

        // Components
        const StatCard = ({ title, value, icon, color = "blue" }) => (
            <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                    <div className={`p-3 rounded-full bg-${color}-100 text-${color}-600`}>
                        {icon}
                    </div>
                    <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">{title}</p>
                        <p className="text-2xl font-semibold text-gray-900">{value}</p>
                    </div>
                </div>
            </div>
        );

        const PatientCard = ({ patient }) => {
            const getRiskColor = (risk) => {
                switch (risk) {
                    case 'High': return 'text-red-600 bg-red-100';
                    case 'Medium': return 'text-yellow-600 bg-yellow-100';
                    case 'Low': return 'text-green-600 bg-green-100';
                    default: return 'text-gray-600 bg-gray-100';
                }
            };

            return (
                <div className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start">
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900">{patient.name}</h3>
                            <p className="text-sm text-gray-600">Age: {patient.age}</p>
                            <p className="text-sm text-gray-600">{patient.condition}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(patient.riskLevel)}`}>
                            {patient.riskLevel} Risk
                        </span>
                    </div>
                    <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Last Visit:</span>
                            <span className="text-gray-900">{patient.lastVisit}</span>
                        </div>
                        <div className="flex justify-between text-sm mt-1">
                            <span className="text-gray-600">Next Appointment:</span>
                            <span className="text-gray-900">{patient.nextAppointment}</span>
                        </div>
                    </div>
                </div>
            );
        };

        const Sidebar = ({ activeTab, setActiveTab }) => {
            const menuItems = [
                { id: 'dashboard', name: 'Dashboard', icon: '📊' },
                { id: 'patients', name: 'Patients', icon: '👥' },
                { id: 'medical-records', name: 'Medical Records', icon: '📋' },
                { id: 'billing', name: 'Billing', icon: '💰' },
                { id: 'inventory', name: 'Inventory', icon: '📦' },
                { id: 'ai-insights', name: 'AI Insights', icon: '🤖' }
            ];

            return (
                <div className="bg-white shadow-lg h-screen w-64 fixed left-0 top-0">
                    <div className="p-6">
                        <h1 className="text-2xl font-bold text-blue-600">Care-SolAI</h1>
                        <p className="text-sm text-gray-600">Healthcare Management</p>
                    </div>
                    <nav className="mt-6">
                        {menuItems.map((item) => (
                            <button
                                key={item.id}
                                onClick={() => setActiveTab(item.id)}
                                className={`w-full flex items-center px-6 py-3 text-left hover:bg-blue-50 transition-colors ${
                                    activeTab === item.id ? 'bg-blue-50 border-r-2 border-blue-500 text-blue-600' : 'text-gray-700'
                                }`}
                            >
                                <span className="mr-3">{item.icon}</span>
                                {item.name}
                            </button>
                        ))}
                    </nav>
                </div>
            );
        };

        const Dashboard = () => (
            <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">Dashboard</h2>
                
                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <StatCard 
                        title="Total Patients" 
                        value={mockStats.totalPatients} 
                        icon="👥"
                        color="blue"
                    />
                    <StatCard 
                        title="Active Alerts" 
                        value={mockStats.activeAlerts} 
                        icon="⚠️"
                        color="red"
                    />
                    <StatCard 
                        title="Today's Appointments" 
                        value={mockStats.todayAppointments} 
                        icon="📅"
                        color="green"
                    />
                    <StatCard 
                        title="Monthly Revenue" 
                        value={`$${mockStats.monthlyRevenue.toLocaleString()}`} 
                        icon="💰"
                        color="purple"
                    />
                </div>

                {/* Recent Patients */}
                <div className="bg-white rounded-lg shadow">
                    <div className="p-6 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Recent Patients</h3>
                    </div>
                    <div className="p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {mockPatients.map((patient) => (
                                <PatientCard key={patient.id} patient={patient} />
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );

        const PatientsView = () => (
            <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">Patient Management</h2>
                <div className="bg-white rounded-lg shadow">
                    <div className="p-6 border-b border-gray-200 flex justify-between items-center">
                        <h3 className="text-lg font-semibold text-gray-900">All Patients</h3>
                        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Add New Patient
                        </button>
                    </div>
                    <div className="p-6">
                        <div className="space-y-4">
                            {mockPatients.map((patient) => (
                                <div key={patient.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                    <div className="flex justify-between items-center">
                                        <div>
                                            <h4 className="font-semibold text-gray-900">{patient.name}</h4>
                                            <p className="text-sm text-gray-600">Age: {patient.age} | {patient.condition}</p>
                                        </div>
                                        <div className="flex space-x-2">
                                            <button className="text-blue-600 hover:text-blue-800">View</button>
                                            <button className="text-green-600 hover:text-green-800">Edit</button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );

        const AIInsights = () => (
            <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">AI Insights</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Fall Risk Predictions</h3>
                        <div className="space-y-3">
                            <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                                <span className="font-medium">Robert Davis</span>
                                <span className="text-red-600 font-semibold">High Risk (85%)</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                                <span className="font-medium">John Smith</span>
                                <span className="text-yellow-600 font-semibold">Medium Risk (45%)</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                                <span className="font-medium">Mary Johnson</span>
                                <span className="text-green-600 font-semibold">Low Risk (15%)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Health Trends</h3>
                        <div className="space-y-4">
                            <div className="border-l-4 border-blue-500 pl-4">
                                <p className="font-medium">Blood Pressure Trends</p>
                                <p className="text-sm text-gray-600">3 patients showing improvement</p>
                            </div>
                            <div className="border-l-4 border-green-500 pl-4">
                                <p className="font-medium">Medication Adherence</p>
                                <p className="text-sm text-gray-600">Overall compliance: 92%</p>
                            </div>
                            <div className="border-l-4 border-yellow-500 pl-4">
                                <p className="font-medium">Care Notes Analysis</p>
                                <p className="text-sm text-gray-600">5 patients need follow-up</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );

        const App = () => {
            const [activeTab, setActiveTab] = useState('dashboard');

            const renderContent = () => {
                switch (activeTab) {
                    case 'dashboard':
                        return <Dashboard />;
                    case 'patients':
                        return <PatientsView />;
                    case 'ai-insights':
                        return <AIInsights />;
                    default:
                        return (
                            <div className="text-center py-12">
                                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                                    {activeTab.charAt(0).toUpperCase() + activeTab.slice(1).replace('-', ' ')}
                                </h2>
                                <p className="text-gray-600">This section is under development.</p>
                            </div>
                        );
                }
            };

            return (
                <div className="flex">
                    <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />
                    <main className="ml-64 flex-1 p-8">
                        {renderContent()}
                    </main>
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
