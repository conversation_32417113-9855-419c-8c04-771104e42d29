{"ast": null, "code": "export function getNativeWebSocket() {\n  if (typeof WebSocket !== \"undefined\") return WebSocket;\n  if (typeof global.WebSocket !== \"undefined\") return global.WebSocket;\n  if (typeof window.WebSocket !== \"undefined\") return window.WebSocket;\n  if (typeof self.WebSocket !== \"undefined\") return self.WebSocket;\n  throw new Error(\"`WebSocket` is not supported in this environment\");\n}", "map": {"version": 3, "names": ["getNativeWebSocket", "WebSocket", "global", "window", "self", "Error"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\isows\\utils.ts"], "sourcesContent": ["export function getNativeWebSocket() {\n  if (typeof WebSocket !== \"undefined\") return WebSocket;\n  if (typeof global.WebSocket !== \"undefined\") return global.WebSocket;\n  if (typeof window.WebSocket !== \"undefined\") return window.WebSocket;\n  if (typeof self.WebSocket !== \"undefined\") return self.WebSocket;\n  throw new Error(\"`WebSocket` is not supported in this environment\");\n}\n"], "mappings": "AAAA,OAAM,SAAUA,kBAAkBA,CAAA;EAChC,IAAI,OAAOC,SAAS,KAAK,WAAW,EAAE,OAAOA,SAAS;EACtD,IAAI,OAAOC,MAAM,CAACD,SAAS,KAAK,WAAW,EAAE,OAAOC,MAAM,CAACD,SAAS;EACpE,IAAI,OAAOE,MAAM,CAACF,SAAS,KAAK,WAAW,EAAE,OAAOE,MAAM,CAACF,SAAS;EACpE,IAAI,OAAOG,IAAI,CAACH,SAAS,KAAK,WAAW,EAAE,OAAOG,IAAI,CAACH,SAAS;EAChE,MAAM,IAAII,KAAK,CAAC,kDAAkD,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}