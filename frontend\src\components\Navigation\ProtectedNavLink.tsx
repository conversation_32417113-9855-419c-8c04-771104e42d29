import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAccessControl } from '../../hooks/useAccessControl';
import AccessDeniedModal from '../Auth/AccessDeniedModal';

interface ProtectedNavLinkProps {
  to: string;
  module: keyof import('../../utils/roleBasedAccess').RolePermissions;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  requireAction?: keyof import('../../utils/roleBasedAccess').Permission;
  style?: React.CSSProperties;
  title?: string;
}

const ProtectedNavLink: React.FC<ProtectedNavLinkProps> = ({
  to,
  module,
  children,
  className = '',
  onClick,
  requireAction = 'read',
  style,
  title,
}) => {
  const location = useLocation();
  const { 
    checkAccess, 
    showAccessDenied, 
    accessDeniedModal, 
    hideAccessDenied,
    userRole 
  } = useAccessControl();

  const hasAccess = checkAccess(module, requireAction);
  const isCurrentPath = location.pathname === to || location.pathname.startsWith(to + '/');

  const handleClick = (e: React.MouseEvent) => {
    if (!hasAccess) {
      e.preventDefault();
      showAccessDenied(module, requireAction);
      return;
    }
    
    onClick?.();
  };

  // If user has access, render normal Link
  if (hasAccess) {
    return (
      <>
        <Link
          to={to}
          className={className}
          onClick={onClick}
          style={style}
          title={title}
        >
          {children}
        </Link>
        <AccessDeniedModal
          isOpen={accessDeniedModal.isOpen}
          onClose={hideAccessDenied}
          userRole={userRole}
          module={accessDeniedModal.info?.module || ''}
          requiredAction={accessDeniedModal.info?.requiredAction}
        />
      </>
    );
  }

  // If user doesn't have access, render button that shows modal
  return (
    <>
      <button
        onClick={handleClick}
        className={`${className} ${isCurrentPath ? '' : 'opacity-60 hover:opacity-80'} transition-opacity duration-200`}
        style={style}
        title={title || `Access restricted for your role`}
      >
        {children}
      </button>
      <AccessDeniedModal
        isOpen={accessDeniedModal.isOpen}
        onClose={hideAccessDenied}
        userRole={userRole}
        module={accessDeniedModal.info?.module || ''}
        requiredAction={accessDeniedModal.info?.requiredAction}
      />
    </>
  );
};

export default ProtectedNavLink;
