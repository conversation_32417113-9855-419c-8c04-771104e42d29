{"ast": null, "code": "// src/notifyManager.ts\nvar defaultScheduler = cb => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = callback => {\n    callback();\n  };\n  let batchNotifyFn = callback => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = callback => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach(callback => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: callback => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: callback => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: fn => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: fn => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: fn => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\nexport { createNotifyManager, defaultScheduler, notifyManager };", "map": {"version": 3, "names": ["defaultScheduler", "cb", "setTimeout", "createNotifyManager", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "scheduleFn", "schedule", "push", "flush", "originalQueue", "length", "for<PERSON>ach", "batch", "result", "batchCalls", "args", "setNotifyFunction", "fn", "setBatchNotifyFunction", "setScheduler", "notify<PERSON><PERSON>ger"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@tanstack\\query-core\\src\\notifyManager.ts"], "sourcesContent": ["// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends Array<unknown>> = (...args: T) => void\n\ntype ScheduleFunction = (callback: () => void) => void\n\nexport const defaultScheduler: ScheduleFunction = (cb) => setTimeout(cb, 0)\n\nexport function createNotifyManager() {\n  let queue: Array<NotifyCallback> = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n  let scheduleFn = defaultScheduler\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  return {\n    batch: <T>(callback: () => T): T => {\n      let result\n      transactions++\n      try {\n        result = callback()\n      } finally {\n        transactions--\n        if (!transactions) {\n          flush()\n        }\n      }\n      return result\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: <T extends Array<unknown>>(\n      callback: BatchCallsCallback<T>,\n    ): BatchCallsCallback<T> => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args)\n        })\n      }\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn: NotifyFunction) => {\n      notifyFn = fn\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn: BatchNotifyFunction) => {\n      batchNotifyFn = fn\n    },\n    setScheduler: (fn: ScheduleFunction) => {\n      scheduleFn = fn\n    },\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n"], "mappings": ";AAYO,IAAMA,gBAAA,GAAsCC,EAAA,IAAOC,UAAA,CAAWD,EAAA,EAAI,CAAC;AAEnE,SAASE,oBAAA,EAAsB;EACpC,IAAIC,KAAA,GAA+B,EAAC;EACpC,IAAIC,YAAA,GAAe;EACnB,IAAIC,QAAA,GAA4BC,QAAA,IAAa;IAC3CA,QAAA,CAAS;EACX;EACA,IAAIC,aAAA,GAAsCD,QAAA,IAAyB;IACjEA,QAAA,CAAS;EACX;EACA,IAAIE,UAAA,GAAaT,gBAAA;EAEjB,MAAMU,QAAA,GAAYH,QAAA,IAAmC;IACnD,IAAIF,YAAA,EAAc;MAChBD,KAAA,CAAMO,IAAA,CAAKJ,QAAQ;IACrB,OAAO;MACLE,UAAA,CAAW,MAAM;QACfH,QAAA,CAASC,QAAQ;MACnB,CAAC;IACH;EACF;EACA,MAAMK,KAAA,GAAQA,CAAA,KAAY;IACxB,MAAMC,aAAA,GAAgBT,KAAA;IACtBA,KAAA,GAAQ,EAAC;IACT,IAAIS,aAAA,CAAcC,MAAA,EAAQ;MACxBL,UAAA,CAAW,MAAM;QACfD,aAAA,CAAc,MAAM;UAClBK,aAAA,CAAcE,OAAA,CAASR,QAAA,IAAa;YAClCD,QAAA,CAASC,QAAQ;UACnB,CAAC;QACH,CAAC;MACH,CAAC;IACH;EACF;EAEA,OAAO;IACLS,KAAA,EAAWT,QAAA,IAAyB;MAClC,IAAIU,MAAA;MACJZ,YAAA;MACA,IAAI;QACFY,MAAA,GAASV,QAAA,CAAS;MACpB,UAAE;QACAF,YAAA;QACA,IAAI,CAACA,YAAA,EAAc;UACjBO,KAAA,CAAM;QACR;MACF;MACA,OAAOK,MAAA;IACT;IAAA;AAAA;AAAA;IAIAC,UAAA,EACEX,QAAA,IAC0B;MAC1B,OAAO,IAAIY,IAAA,KAAS;QAClBT,QAAA,CAAS,MAAM;UACbH,QAAA,CAAS,GAAGY,IAAI;QAClB,CAAC;MACH;IACF;IACAT,QAAA;IAAA;AAAA;AAAA;AAAA;IAKAU,iBAAA,EAAoBC,EAAA,IAAuB;MACzCf,QAAA,GAAWe,EAAA;IACb;IAAA;AAAA;AAAA;AAAA;IAKAC,sBAAA,EAAyBD,EAAA,IAA4B;MACnDb,aAAA,GAAgBa,EAAA;IAClB;IACAE,YAAA,EAAeF,EAAA,IAAyB;MACtCZ,UAAA,GAAaY,EAAA;IACf;EACF;AACF;AAGO,IAAMG,aAAA,GAAgBrB,mBAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}