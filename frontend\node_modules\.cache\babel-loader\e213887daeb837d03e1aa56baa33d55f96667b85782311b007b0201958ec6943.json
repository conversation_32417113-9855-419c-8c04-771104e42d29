{"ast": null, "code": "export const SIGN_OUT_SCOPES = ['global', 'local', 'others'];", "map": {"version": 3, "names": ["SIGN_OUT_SCOPES"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@supabase\\auth-js\\src\\lib\\types.ts"], "sourcesContent": ["import { AuthError } from './errors'\nimport { Fetch } from './fetch'\nimport type { SolanaSignInInput, SolanaSignInOutput } from '@solana/wallet-standard-features'\n\n/** One of the providers supported by GoTrue. */\nexport type Provider =\n  | 'apple'\n  | 'azure'\n  | 'bitbucket'\n  | 'discord'\n  | 'facebook'\n  | 'figma'\n  | 'github'\n  | 'gitlab'\n  | 'google'\n  | 'kakao'\n  | 'keycloak'\n  | 'linkedin'\n  | 'linkedin_oidc'\n  | 'notion'\n  | 'slack'\n  | 'slack_oidc'\n  | 'spotify'\n  | 'twitch'\n  | 'twitter'\n  | 'workos'\n  | 'zoom'\n  | 'fly'\n\nexport type AuthChangeEventMFA = 'MFA_CHALLENGE_VERIFIED'\n\nexport type AuthChangeEvent =\n  | 'INITIAL_SESSION'\n  | 'PASSWORD_RECOVERY'\n  | 'SIGNED_IN'\n  | 'SIGNED_OUT'\n  | 'TOKEN_REFRESHED'\n  | 'USER_UPDATED'\n  | AuthChangeEventMFA\n\n/**\n * Provide your own global lock implementation instead of the default\n * implementation. The function should acquire a lock for the duration of the\n * `fn` async function, such that no other client instances will be able to\n * hold it at the same time.\n *\n * @experimental\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout should occur. If positive it\n *                       should throw an Error with an `isAcquireTimeout`\n *                       property set to true if the operation fails to be\n *                       acquired after this much time (ms).\n * @param fn The operation to execute when the lock is acquired.\n */\nexport type LockFunc = <R>(name: string, acquireTimeout: number, fn: () => Promise<R>) => Promise<R>\n\nexport type GoTrueClientOptions = {\n  /* The URL of the GoTrue server. */\n  url?: string\n  /* Any additional headers to send to the GoTrue server. */\n  headers?: { [key: string]: string }\n  /* Optional key name used for storing tokens in local storage. */\n  storageKey?: string\n  /* Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user. */\n  detectSessionInUrl?: boolean\n  /* Set to \"true\" if you want to automatically refresh the token before expiring. */\n  autoRefreshToken?: boolean\n  /* Set to \"true\" if you want to automatically save the user session into local storage. If set to false, session will just be saved in memory. */\n  persistSession?: boolean\n  /* Provide your own local storage implementation to use instead of the browser's local storage. */\n  storage?: SupportedStorage\n  /* A custom fetch implementation. */\n  fetch?: Fetch\n  /* If set to 'pkce' PKCE flow. Defaults to the 'implicit' flow otherwise */\n  flowType?: AuthFlowType\n  /* If debug messages are emitted. Can be used to inspect the behavior of the library. If set to a function, the provided function will be used instead of `console.log()` to perform the logging. */\n  debug?: boolean | ((message: string, ...args: any[]) => void)\n  /**\n   * Provide your own locking mechanism based on the environment. By default no locking is done at this time.\n   *\n   * @experimental\n   */\n  lock?: LockFunc\n  /**\n   * Set to \"true\" if there is a custom authorization header set globally.\n   * @experimental\n   */\n  hasCustomAuthorizationHeader?: boolean\n}\n\nexport type WeakPasswordReasons = 'length' | 'characters' | 'pwned' | (string & {})\nexport type WeakPassword = {\n  reasons: WeakPasswordReasons[]\n  message: string\n}\n\nexport type AuthResponse =\n  | {\n      data: {\n        user: User | null\n        session: Session | null\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n        session: null\n      }\n      error: AuthError\n    }\n\nexport type AuthResponsePassword =\n  | {\n      data: {\n        user: User | null\n        session: Session | null\n        weak_password?: WeakPassword | null\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n        session: null\n      }\n      error: AuthError\n    }\n\n/**\n * AuthOtpResponse is returned when OTP is used.\n *\n * {@see AuthResponse}\n */\nexport type AuthOtpResponse =\n  | {\n      data: { user: null; session: null; messageId?: string | null }\n      error: null\n    }\n  | {\n      data: { user: null; session: null; messageId?: string | null }\n      error: AuthError\n    }\n\nexport type AuthTokenResponse =\n  | {\n      data: {\n        user: User\n        session: Session\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n        session: null\n      }\n      error: AuthError\n    }\n\nexport type AuthTokenResponsePassword =\n  | {\n      data: {\n        user: User\n        session: Session\n        weakPassword?: WeakPassword\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n        session: null\n        weakPassword?: null\n      }\n      error: AuthError\n    }\n\nexport type OAuthResponse =\n  | {\n      data: {\n        provider: Provider\n        url: string\n      }\n      error: null\n    }\n  | {\n      data: {\n        provider: Provider\n        url: null\n      }\n      error: AuthError\n    }\n\nexport type SSOResponse =\n  | {\n      data: {\n        /**\n         * URL to open in a browser which will complete the sign-in flow by\n         * taking the user to the identity provider's authentication flow.\n         *\n         * On browsers you can set the URL to `window.location.href` to take\n         * the user to the authentication flow.\n         */\n        url: string\n      }\n      error: null\n    }\n  | {\n      data: null\n      error: AuthError\n    }\n\nexport type UserResponse =\n  | {\n      data: {\n        user: User\n      }\n      error: null\n    }\n  | {\n      data: {\n        user: null\n      }\n      error: AuthError\n    }\n\nexport interface Session {\n  /**\n   * The oauth provider token. If present, this can be used to make external API requests to the oauth provider used.\n   */\n  provider_token?: string | null\n  /**\n   * The oauth provider refresh token. If present, this can be used to refresh the provider_token via the oauth provider's API.\n   * Not all oauth providers return a provider refresh token. If the provider_refresh_token is missing, please refer to the oauth provider's documentation for information on how to obtain the provider refresh token.\n   */\n  provider_refresh_token?: string | null\n  /**\n   * The access token jwt. It is recommended to set the JWT_EXPIRY to a shorter expiry value.\n   */\n  access_token: string\n  /**\n   * A one-time used refresh token that never expires.\n   */\n  refresh_token: string\n  /**\n   * The number of seconds until the token expires (since it was issued). Returned when a login is confirmed.\n   */\n  expires_in: number\n  /**\n   * A timestamp of when the token will expire. Returned when a login is confirmed.\n   */\n  expires_at?: number\n  token_type: string\n  user: User\n}\n\n/**\n * An authentication methord reference (AMR) entry.\n *\n * An entry designates what method was used by the user to verify their\n * identity and at what time.\n *\n * @see {@link GoTrueMFAApi#getAuthenticatorAssuranceLevel}.\n */\nexport interface AMREntry {\n  /** Authentication method name. */\n  method: 'password' | 'otp' | 'oauth' | 'mfa/totp' | (string & {})\n\n  /**\n   * Timestamp when the method was successfully used. Represents number of\n   * seconds since 1st January 1970 (UNIX epoch) in UTC.\n   */\n  timestamp: number\n}\n\nexport interface UserIdentity {\n  id: string\n  user_id: string\n  identity_data?: {\n    [key: string]: any\n  }\n  identity_id: string\n  provider: string\n  created_at?: string\n  last_sign_in_at?: string\n  updated_at?: string\n}\n\n/**\n * A MFA factor.\n *\n * @see {@link GoTrueMFAApi#enroll}\n * @see {@link GoTrueMFAApi#listFactors}\n * @see {@link GoTrueMFAAdminApi#listFactors}\n */\nexport interface Factor {\n  /** ID of the factor. */\n  id: string\n\n  /** Friendly name of the factor, useful to disambiguate between multiple factors. */\n  friendly_name?: string\n\n  /**\n   * Type of factor. `totp` and `phone` supported with this version\n   */\n  factor_type: 'totp' | 'phone' | (string & {})\n\n  /** Factor's status. */\n  status: 'verified' | 'unverified'\n\n  created_at: string\n  updated_at: string\n}\n\nexport interface UserAppMetadata {\n  provider?: string\n  [key: string]: any\n}\n\nexport interface UserMetadata {\n  [key: string]: any\n}\n\nexport interface User {\n  id: string\n  app_metadata: UserAppMetadata\n  user_metadata: UserMetadata\n  aud: string\n  confirmation_sent_at?: string\n  recovery_sent_at?: string\n  email_change_sent_at?: string\n  new_email?: string\n  new_phone?: string\n  invited_at?: string\n  action_link?: string\n  email?: string\n  phone?: string\n  created_at: string\n  confirmed_at?: string\n  email_confirmed_at?: string\n  phone_confirmed_at?: string\n  last_sign_in_at?: string\n  role?: string\n  updated_at?: string\n  identities?: UserIdentity[]\n  is_anonymous?: boolean\n  is_sso_user?: boolean\n  factors?: Factor[]\n  deleted_at?: string\n}\n\nexport interface UserAttributes {\n  /**\n   * The user's email.\n   */\n  email?: string\n\n  /**\n   * The user's phone.\n   */\n  phone?: string\n\n  /**\n   * The user's password.\n   */\n  password?: string\n\n  /**\n   * The nonce sent for reauthentication if the user's password is to be updated.\n   *\n   * Call reauthenticate() to obtain the nonce first.\n   */\n  nonce?: string\n\n  /**\n   * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n   *\n   * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n   *\n   */\n  data?: object\n}\n\nexport interface AdminUserAttributes extends Omit<UserAttributes, 'data'> {\n  /**\n   * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n   *\n   *\n   * The `user_metadata` should be a JSON object that includes user-specific info, such as their first and last name.\n   *\n   * Note: When using the GoTrueAdminApi and wanting to modify a user's metadata,\n   * this attribute is used instead of UserAttributes data.\n   *\n   */\n  user_metadata?: object\n\n  /**\n   * A custom data object to store the user's application specific metadata. This maps to the `auth.users.app_metadata` column.\n   *\n   * Only a service role can modify.\n   *\n   * The `app_metadata` should be a JSON object that includes app-specific info, such as identity providers, roles, and other\n   * access control information.\n   */\n  app_metadata?: object\n\n  /**\n   * Confirms the user's email address if set to true.\n   *\n   * Only a service role can modify.\n   */\n  email_confirm?: boolean\n\n  /**\n   * Confirms the user's phone number if set to true.\n   *\n   * Only a service role can modify.\n   */\n  phone_confirm?: boolean\n\n  /**\n   * Determines how long a user is banned for.\n   *\n   * The format for the ban duration follows a strict sequence of decimal numbers with a unit suffix.\n   * Valid time units are \"ns\", \"us\" (or \"µs\"), \"ms\", \"s\", \"m\", \"h\".\n   *\n   * For example, some possible durations include: '300ms', '2h45m'.\n   *\n   * Setting the ban duration to 'none' lifts the ban on the user.\n   */\n  ban_duration?: string | 'none'\n\n  /**\n   * The `role` claim set in the user's access token JWT.\n   *\n   * When a user signs up, this role is set to `authenticated` by default. You should only modify the `role` if you need to provision several levels of admin access that have different permissions on individual columns in your database.\n   *\n   * Setting this role to `service_role` is not recommended as it grants the user admin privileges.\n   */\n  role?: string\n\n  /**\n   * The `password_hash` for the user's password.\n   *\n   * Allows you to specify a password hash for the user. This is useful for migrating a user's password hash from another service.\n   *\n   * Supports bcrypt, scrypt (firebase), and argon2 password hashes.\n   */\n  password_hash?: string\n\n  /**\n   * The `id` for the user.\n   *\n   * Allows you to overwrite the default `id` set for the user.\n   */\n  id?: string\n}\n\nexport interface Subscription {\n  /**\n   * The subscriber UUID. This will be set by the client.\n   */\n  id: string\n  /**\n   * The function to call every time there is an event. eg: (eventName) => {}\n   */\n  callback: (event: AuthChangeEvent, session: Session | null) => void\n  /**\n   * Call this to remove the listener.\n   */\n  unsubscribe: () => void\n}\n\nexport type SignInAnonymouslyCredentials = {\n  options?: {\n    /**\n     * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n     *\n     * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n     */\n    data?: object\n    /** Verification token received when the user completes the captcha on the site. */\n    captchaToken?: string\n  }\n}\n\nexport type SignUpWithPasswordCredentials =\n  | {\n      /** The user's email address. */\n      email: string\n      /** The user's password. */\n      password: string\n      options?: {\n        /** The redirect url embedded in the email link */\n        emailRedirectTo?: string\n        /**\n         * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n         *\n         * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n         */\n        data?: object\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      /** The user's phone number. */\n      phone: string\n      /** The user's password. */\n      password: string\n      options?: {\n        /**\n         * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n         *\n         * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n         */\n        data?: object\n        /** Verification token received when the user completes the captcha on the site. Requires a configured WhatsApp sender on Twilio */\n        captchaToken?: string\n        /** Messaging channel to use (e.g. whatsapp or sms) */\n        channel?: 'sms' | 'whatsapp'\n      }\n    }\n\nexport type SignInWithPasswordCredentials =\n  | {\n      /** The user's email address. */\n      email: string\n      /** The user's password. */\n      password: string\n      options?: {\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      /** The user's phone number. */\n      phone: string\n      /** The user's password. */\n      password: string\n      options?: {\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n\nexport type SignInWithPasswordlessCredentials =\n  | {\n      /** The user's email address. */\n      email: string\n      options?: {\n        /** The redirect url embedded in the email link */\n        emailRedirectTo?: string\n        /** If set to false, this method will not create a new user. Defaults to true. */\n        shouldCreateUser?: boolean\n        /**\n         * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n         *\n         * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n         */\n        data?: object\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      /** The user's phone number. */\n      phone: string\n      options?: {\n        /** If set to false, this method will not create a new user. Defaults to true. */\n        shouldCreateUser?: boolean\n        /**\n         * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n         *\n         * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n         */\n        data?: object\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n        /** Messaging channel to use (e.g. whatsapp or sms) */\n        channel?: 'sms' | 'whatsapp'\n      }\n    }\n\nexport type AuthFlowType = 'implicit' | 'pkce'\nexport type SignInWithOAuthCredentials = {\n  /** One of the providers supported by GoTrue. */\n  provider: Provider\n  options?: {\n    /** A URL to send the user to after they are confirmed. */\n    redirectTo?: string\n    /** A space-separated list of scopes granted to the OAuth application. */\n    scopes?: string\n    /** An object of query params */\n    queryParams?: { [key: string]: string }\n    /** If set to true does not immediately redirect the current browser context to visit the OAuth authorization page for the provider. */\n    skipBrowserRedirect?: boolean\n  }\n}\n\nexport type SignInWithIdTokenCredentials = {\n  /** Provider name or OIDC `iss` value identifying which provider should be used to verify the provided token. Supported names: `google`, `apple`, `azure`, `facebook`, `kakao`, `keycloak` (deprecated). */\n  provider: 'google' | 'apple' | 'azure' | 'facebook' | 'kakao' | (string & {})\n  /** OIDC ID token issued by the specified provider. The `iss` claim in the ID token must match the supplied provider. Some ID tokens contain an `at_hash` which require that you provide an `access_token` value to be accepted properly. If the token contains a `nonce` claim you must supply the nonce used to obtain the ID token. */\n  token: string\n  /** If the ID token contains an `at_hash` claim, then the hash of this value is compared to the value in the ID token. */\n  access_token?: string\n  /** If the ID token contains a `nonce` claim, then the hash of this value is compared to the value in the ID token. */\n  nonce?: string\n  options?: {\n    /** Verification token received when the user completes the captcha on the site. */\n    captchaToken?: string\n  }\n}\n\nexport type SolanaWallet = {\n  signIn?: (...inputs: SolanaSignInInput[]) => Promise<SolanaSignInOutput | SolanaSignInOutput[]>\n  publicKey?: {\n    toBase58: () => string\n  } | null\n\n  signMessage?: (message: Uint8Array, encoding?: 'utf8' | string) => Promise<Uint8Array> | undefined\n}\n\nexport type SolanaWeb3Credentials =\n  | {\n      chain: 'solana'\n\n      /** Wallet interface to use. If not specified will default to `window.solana`. */\n      wallet?: SolanaWallet\n\n      /** Optional statement to include in the Sign in with Solana message. Must not include new line characters. Most wallets like Phantom **require specifying a statement!** */\n      statement?: string\n\n      options?: {\n        /** URL to use with the wallet interface. Some wallets do not allow signing a message for URLs different from the current page. */\n        url?: string\n\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n\n        signInWithSolana?: Partial<\n          Omit<SolanaSignInInput, 'version' | 'chain' | 'domain' | 'uri' | 'statement'>\n        >\n      }\n    }\n  | {\n      chain: 'solana'\n\n      /** Sign in with Solana compatible message. Must include `Issued At`, `URI` and `Version`. */\n      message: string\n\n      /** Ed25519 signature of the message. */\n      signature: Uint8Array\n\n      options?: {\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n\nexport type Web3Credentials = SolanaWeb3Credentials\n\nexport type VerifyOtpParams = VerifyMobileOtpParams | VerifyEmailOtpParams | VerifyTokenHashParams\nexport interface VerifyMobileOtpParams {\n  /** The user's phone number. */\n  phone: string\n  /** The otp sent to the user's phone number. */\n  token: string\n  /** The user's verification type. */\n  type: MobileOtpType\n  options?: {\n    /** A URL to send the user to after they are confirmed. */\n    redirectTo?: string\n\n    /**\n     * Verification token received when the user completes the captcha on the site.\n     *\n     * @deprecated\n     */\n    captchaToken?: string\n  }\n}\nexport interface VerifyEmailOtpParams {\n  /** The user's email address. */\n  email: string\n  /** The otp sent to the user's email address. */\n  token: string\n  /** The user's verification type. */\n  type: EmailOtpType\n  options?: {\n    /** A URL to send the user to after they are confirmed. */\n    redirectTo?: string\n\n    /** Verification token received when the user completes the captcha on the site.\n     *\n     * @deprecated\n     */\n    captchaToken?: string\n  }\n}\n\nexport interface VerifyTokenHashParams {\n  /** The token hash used in an email link */\n  token_hash: string\n\n  /** The user's verification type. */\n  type: EmailOtpType\n}\n\nexport type MobileOtpType = 'sms' | 'phone_change'\nexport type EmailOtpType = 'signup' | 'invite' | 'magiclink' | 'recovery' | 'email_change' | 'email'\n\nexport type ResendParams =\n  | {\n      type: Extract<EmailOtpType, 'signup' | 'email_change'>\n      email: string\n      options?: {\n        /** A URL to send the user to after they have signed-in. */\n        emailRedirectTo?: string\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      type: Extract<MobileOtpType, 'sms' | 'phone_change'>\n      phone: string\n      options?: {\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n\nexport type SignInWithSSO =\n  | {\n      /** UUID of the SSO provider to invoke single-sign on to. */\n      providerId: string\n\n      options?: {\n        /** A URL to send the user to after they have signed-in. */\n        redirectTo?: string\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n  | {\n      /** Domain name of the organization for which to invoke single-sign on. */\n      domain: string\n\n      options?: {\n        /** A URL to send the user to after they have signed-in. */\n        redirectTo?: string\n        /** Verification token received when the user completes the captcha on the site. */\n        captchaToken?: string\n      }\n    }\n\nexport type GenerateSignupLinkParams = {\n  type: 'signup'\n  email: string\n  password: string\n  options?: Pick<GenerateLinkOptions, 'data' | 'redirectTo'>\n}\n\nexport type GenerateInviteOrMagiclinkParams = {\n  type: 'invite' | 'magiclink'\n  /** The user's email */\n  email: string\n  options?: Pick<GenerateLinkOptions, 'data' | 'redirectTo'>\n}\n\nexport type GenerateRecoveryLinkParams = {\n  type: 'recovery'\n  /** The user's email */\n  email: string\n  options?: Pick<GenerateLinkOptions, 'redirectTo'>\n}\n\nexport type GenerateEmailChangeLinkParams = {\n  type: 'email_change_current' | 'email_change_new'\n  /** The user's email */\n  email: string\n  /**\n   * The user's new email. Only required if type is 'email_change_current' or 'email_change_new'.\n   */\n  newEmail: string\n  options?: Pick<GenerateLinkOptions, 'redirectTo'>\n}\n\nexport interface GenerateLinkOptions {\n  /**\n   * A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.\n   *\n   * The `data` should be a JSON object that includes user-specific info, such as their first and last name.\n   */\n  data?: object\n  /** The URL which will be appended to the email link generated. */\n  redirectTo?: string\n}\n\nexport type GenerateLinkParams =\n  | GenerateSignupLinkParams\n  | GenerateInviteOrMagiclinkParams\n  | GenerateRecoveryLinkParams\n  | GenerateEmailChangeLinkParams\n\nexport type GenerateLinkResponse =\n  | {\n      data: {\n        properties: GenerateLinkProperties\n        user: User\n      }\n      error: null\n    }\n  | {\n      data: {\n        properties: null\n        user: null\n      }\n      error: AuthError\n    }\n\n/** The properties related to the email link generated  */\nexport type GenerateLinkProperties = {\n  /**\n   * The email link to send to the user.\n   * The action_link follows the following format: auth/v1/verify?type={verification_type}&token={hashed_token}&redirect_to={redirect_to}\n   * */\n  action_link: string\n  /**\n   * The raw email OTP.\n   * You should send this in the email if you want your users to verify using an OTP instead of the action link.\n   * */\n  email_otp: string\n  /**\n   * The hashed token appended to the action link.\n   * */\n  hashed_token: string\n  /** The URL appended to the action link. */\n  redirect_to: string\n  /** The verification type that the email link is associated to. */\n  verification_type: GenerateLinkType\n}\n\nexport type GenerateLinkType =\n  | 'signup'\n  | 'invite'\n  | 'magiclink'\n  | 'recovery'\n  | 'email_change_current'\n  | 'email_change_new'\n\nexport type MFAEnrollParams = MFAEnrollTOTPParams | MFAEnrollPhoneParams\n\nexport type MFAUnenrollParams = {\n  /** ID of the factor being unenrolled. */\n  factorId: string\n}\n\nexport type MFAVerifyParams = {\n  /** ID of the factor being verified. Returned in enroll(). */\n  factorId: string\n\n  /** ID of the challenge being verified. Returned in challenge(). */\n  challengeId: string\n\n  /** Verification code provided by the user. */\n  code: string\n}\n\nexport type MFAChallengeParams = {\n  /** ID of the factor to be challenged. Returned in enroll(). */\n  factorId: string\n  /** Messaging channel to use (e.g. whatsapp or sms). Only relevant for phone factors */\n  channel?: 'sms' | 'whatsapp'\n}\n\nexport type MFAChallengeAndVerifyParams = {\n  /** ID of the factor being verified. Returned in enroll(). */\n  factorId: string\n  /** Verification code provided by the user. */\n  code: string\n}\n\nexport type AuthMFAVerifyResponse =\n  | {\n      data: {\n        /** New access token (JWT) after successful verification. */\n        access_token: string\n\n        /** Type of token, typically `Bearer`. */\n        token_type: string\n\n        /** Number of seconds in which the access token will expire. */\n        expires_in: number\n\n        /** Refresh token you can use to obtain new access tokens when expired. */\n        refresh_token: string\n\n        /** Updated user profile. */\n        user: User\n      }\n      error: null\n    }\n  | {\n      data: null\n      error: AuthError\n    }\n\nexport type AuthMFAEnrollResponse = AuthMFAEnrollTOTPResponse | AuthMFAEnrollPhoneResponse\n\nexport type AuthMFAUnenrollResponse =\n  | {\n      data: {\n        /** ID of the factor that was successfully unenrolled. */\n        id: string\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\nexport type AuthMFAChallengeResponse =\n  | {\n      data: {\n        /** ID of the newly created challenge. */\n        id: string\n\n        /** Factor Type which generated the challenge */\n        type: 'totp' | 'phone'\n\n        /** Timestamp in UNIX seconds when this challenge will no longer be usable. */\n        expires_at: number\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\nexport type AuthMFAListFactorsResponse =\n  | {\n      data: {\n        /** All available factors (verified and unverified). */\n        all: Factor[]\n\n        /** Only verified TOTP factors. (A subset of `all`.) */\n        totp: Factor[]\n        /** Only verified Phone factors. (A subset of `all`.) */\n        phone: Factor[]\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\nexport type AuthenticatorAssuranceLevels = 'aal1' | 'aal2'\n\nexport type AuthMFAGetAuthenticatorAssuranceLevelResponse =\n  | {\n      data: {\n        /** Current AAL level of the session. */\n        currentLevel: AuthenticatorAssuranceLevels | null\n\n        /**\n         * Next possible AAL level for the session. If the next level is higher\n         * than the current one, the user should go through MFA.\n         *\n         * @see {@link GoTrueMFAApi#challenge}\n         */\n        nextLevel: AuthenticatorAssuranceLevels | null\n\n        /**\n         * A list of all authentication methods attached to this session. Use\n         * the information here to detect the last time a user verified a\n         * factor, for example if implementing a step-up scenario.\n         */\n        currentAuthenticationMethods: AMREntry[]\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\n/**\n * Contains the full multi-factor authentication API.\n *\n */\nexport interface GoTrueMFAApi {\n  /**\n   * Starts the enrollment process for a new Multi-Factor Authentication (MFA)\n   * factor. This method creates a new `unverified` factor.\n   * To verify a factor, present the QR code or secret to the user and ask them to add it to their\n   * authenticator app.\n   * The user has to enter the code from their authenticator app to verify it.\n   *\n   * Upon verifying a factor, all other sessions are logged out and the current session's authenticator level is promoted to `aal2`.\n   *\n   */\n  enroll(params: MFAEnrollTOTPParams): Promise<AuthMFAEnrollTOTPResponse>\n  enroll(params: MFAEnrollPhoneParams): Promise<AuthMFAEnrollPhoneResponse>\n  enroll(params: MFAEnrollParams): Promise<AuthMFAEnrollResponse>\n\n  /**\n   * Prepares a challenge used to verify that a user has access to a MFA\n   * factor.\n   */\n  challenge(params: MFAChallengeParams): Promise<AuthMFAChallengeResponse>\n\n  /**\n   * Verifies a code against a challenge. The verification code is\n   * provided by the user by entering a code seen in their authenticator app.\n   */\n  verify(params: MFAVerifyParams): Promise<AuthMFAVerifyResponse>\n\n  /**\n   * Unenroll removes a MFA factor.\n   * A user has to have an `aal2` authenticator level in order to unenroll a `verified` factor.\n   */\n  unenroll(params: MFAUnenrollParams): Promise<AuthMFAUnenrollResponse>\n\n  /**\n   * Helper method which creates a challenge and immediately uses the given code to verify against it thereafter. The verification code is\n   * provided by the user by entering a code seen in their authenticator app.\n   */\n  challengeAndVerify(params: MFAChallengeAndVerifyParams): Promise<AuthMFAVerifyResponse>\n\n  /**\n   * Returns the list of MFA factors enabled for this user.\n   *\n   * @see {@link GoTrueMFAApi#enroll}\n   * @see {@link GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   * @see {@link GoTrueClient#getUser}\n   *\n   */\n  listFactors(): Promise<AuthMFAListFactorsResponse>\n\n  /**\n   * Returns the Authenticator Assurance Level (AAL) for the active session.\n   *\n   * - `aal1` (or `null`) means that the user's identity has been verified only\n   * with a conventional login (email+password, OTP, magic link, social login,\n   * etc.).\n   * - `aal2` means that the user's identity has been verified both with a conventional login and at least one MFA factor.\n   *\n   * Although this method returns a promise, it's fairly quick (microseconds)\n   * and rarely uses the network. You can use this to check whether the current\n   * user needs to be shown a screen to verify their MFA factors.\n   *\n   */\n  getAuthenticatorAssuranceLevel(): Promise<AuthMFAGetAuthenticatorAssuranceLevelResponse>\n}\n\n/**\n * @expermental\n */\nexport type AuthMFAAdminDeleteFactorResponse =\n  | {\n      data: {\n        /** ID of the factor that was successfully deleted. */\n        id: string\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\n/**\n * @expermental\n */\nexport type AuthMFAAdminDeleteFactorParams = {\n  /** ID of the MFA factor to delete. */\n  id: string\n\n  /** ID of the user whose factor is being deleted. */\n  userId: string\n}\n\n/**\n * @expermental\n */\nexport type AuthMFAAdminListFactorsResponse =\n  | {\n      data: {\n        /** All factors attached to the user. */\n        factors: Factor[]\n      }\n      error: null\n    }\n  | { data: null; error: AuthError }\n\n/**\n * @expermental\n */\nexport type AuthMFAAdminListFactorsParams = {\n  /** ID of the user. */\n  userId: string\n}\n\n/**\n * Contains the full multi-factor authentication administration API.\n *\n * @expermental\n */\nexport interface GoTrueAdminMFAApi {\n  /**\n   * Lists all factors associated to a user.\n   *\n   */\n  listFactors(params: AuthMFAAdminListFactorsParams): Promise<AuthMFAAdminListFactorsResponse>\n\n  /**\n   * Deletes a factor on a user. This will log the user out of all active\n   * sessions if the deleted factor was verified.\n   *\n   * @see {@link GoTrueMFAApi#unenroll}\n   *\n   * @expermental\n   */\n  deleteFactor(params: AuthMFAAdminDeleteFactorParams): Promise<AuthMFAAdminDeleteFactorResponse>\n}\n\ntype AnyFunction = (...args: any[]) => any\ntype MaybePromisify<T> = T | Promise<T>\n\ntype PromisifyMethods<T> = {\n  [K in keyof T]: T[K] extends AnyFunction\n    ? (...args: Parameters<T[K]>) => MaybePromisify<ReturnType<T[K]>>\n    : T[K]\n}\n\nexport type SupportedStorage = PromisifyMethods<\n  Pick<Storage, 'getItem' | 'setItem' | 'removeItem'>\n> & {\n  /**\n   * If set to `true` signals to the library that the storage medium is used\n   * on a server and the values may not be authentic, such as reading from\n   * request cookies. Implementations should not set this to true if the client\n   * is used on a server that reads storage information from authenticated\n   * sources, such as a secure database or file.\n   */\n  isServer?: boolean\n}\n\nexport type InitializeResult = { error: AuthError | null }\n\nexport type CallRefreshTokenResult =\n  | {\n      session: Session\n      error: null\n    }\n  | {\n      session: null\n      error: AuthError\n    }\n\nexport type Pagination = {\n  [key: string]: any\n  nextPage: number | null\n  lastPage: number\n  total: number\n}\n\nexport type PageParams = {\n  /** The page number */\n  page?: number\n  /** Number of items returned per page */\n  perPage?: number\n}\n\nexport type SignOut = {\n  /**\n   * Determines which sessions should be\n   * logged out. Global means all\n   * sessions by this account. Local\n   * means only this session. Others\n   * means all other sessions except the\n   * current one. When using others,\n   * there is no sign-out event fired on\n   * the current session!\n   */\n  scope?: 'global' | 'local' | 'others'\n}\n\nexport type MFAEnrollTOTPParams = {\n  /** The type of factor being enrolled. */\n  factorType: 'totp'\n  /** Domain which the user is enrolled with. */\n  issuer?: string\n  /** Human readable name assigned to the factor. */\n  friendlyName?: string\n}\nexport type MFAEnrollPhoneParams = {\n  /** The type of factor being enrolled. */\n  factorType: 'phone'\n  /** Human readable name assigned to the factor. */\n  friendlyName?: string\n  /** Phone number associated with a factor. Number should conform to E.164 format */\n  phone: string\n}\n\nexport type AuthMFAEnrollTOTPResponse =\n  | {\n      data: {\n        /** ID of the factor that was just enrolled (in an unverified state). */\n        id: string\n\n        /** Type of MFA factor.*/\n        type: 'totp'\n\n        /** TOTP enrollment information. */\n        totp: {\n          /** Contains a QR code encoding the authenticator URI. You can\n           * convert it to a URL by prepending `data:image/svg+xml;utf-8,` to\n           * the value. Avoid logging this value to the console. */\n          qr_code: string\n\n          /** The TOTP secret (also encoded in the QR code). Show this secret\n           * in a password-style field to the user, in case they are unable to\n           * scan the QR code. Avoid logging this value to the console. */\n          secret: string\n\n          /** The authenticator URI encoded within the QR code, should you need\n           * to use it. Avoid loggin this value to the console. */\n          uri: string\n        }\n        /** Friendly name of the factor, useful for distinguishing between factors **/\n        friendly_name?: string\n      }\n      error: null\n    }\n  | {\n      data: null\n      error: AuthError\n    }\n\nexport type AuthMFAEnrollPhoneResponse =\n  | {\n      data: {\n        /** ID of the factor that was just enrolled (in an unverified state). */\n        id: string\n\n        /** Type of MFA factor. */\n        type: 'phone'\n\n        /** Friendly name of the factor, useful for distinguishing between factors **/\n        friendly_name?: string\n\n        /** Phone number of the MFA factor in E.164 format. Used to send messages  */\n        phone: string\n      }\n      error: null\n    }\n  | {\n      data: null\n      error: AuthError\n    }\n\nexport type JwtHeader = {\n  alg: 'RS256' | 'ES256' | 'HS256'\n  kid: string\n  typ: string\n}\n\nexport type RequiredClaims = {\n  iss: string\n  sub: string\n  aud: string | string[]\n  exp: number\n  iat: number\n  role: string\n  aal: AuthenticatorAssuranceLevels\n  session_id: string\n}\n\nexport type JwtPayload = RequiredClaims & {\n  [key: string]: any\n}\n\nexport interface JWK {\n  kty: 'RSA' | 'EC' | 'oct'\n  key_ops: string[]\n  alg?: string\n  kid?: string\n  [key: string]: any\n}\n\nexport const SIGN_OUT_SCOPES = ['global', 'local', 'others'] as const\nexport type SignOutScope = typeof SIGN_OUT_SCOPES[number]\n"], "mappings": "AAmwCA,OAAO,MAAMA,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}