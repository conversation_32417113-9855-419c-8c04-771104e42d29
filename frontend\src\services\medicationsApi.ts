import api from './api';

// Medication Types
export interface Medication {
  id: string;
  residentId: string;
  residentName: string;
  medicationName: string;
  dosage: string;
  frequency: string;
  route: 'oral' | 'topical' | 'injection' | 'inhalation' | 'other';
  prescribedBy: string;
  startDate: string;
  endDate?: string;
  instructions: string;
  status: 'active' | 'discontinued' | 'completed';
  sideEffects?: string[];
  interactions?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface MedicationAdministration {
  id: string;
  medicationId: string;
  residentId: string;
  administeredBy: string;
  administeredAt: string;
  dosageGiven: string;
  status: 'given' | 'refused' | 'missed' | 'held';
  notes?: string;
  vitalSigns?: {
    bloodPressure?: string;
    heartRate?: number;
    temperature?: number;
    oxygenSaturation?: number;
  };
}

export interface CreateMedicationRequest {
  residentId: string;
  medicationName: string;
  dosage: string;
  frequency: string;
  route: 'oral' | 'topical' | 'injection' | 'inhalation' | 'other';
  prescribedBy: string;
  startDate: string;
  endDate?: string;
  instructions: string;
  sideEffects?: string[];
  interactions?: string[];
}

// Mock Data
const MOCK_MEDICATIONS: Medication[] = [
  {
    id: 'med-001',
    residentId: 'R001',
    residentName: 'John Smith',
    medicationName: 'Metformin',
    dosage: '500mg',
    frequency: 'Twice daily',
    route: 'oral',
    prescribedBy: 'Dr. Robert Johnson',
    startDate: '2025-01-01',
    endDate: undefined,
    instructions: 'Take with meals to reduce stomach upset',
    status: 'active',
    sideEffects: ['Nausea', 'Diarrhea'],
    interactions: ['Alcohol'],
    createdAt: '2025-01-01T10:00:00Z',
    updatedAt: '2025-01-15T10:00:00Z'
  },
  {
    id: 'med-002',
    residentId: 'R001',
    residentName: 'John Smith',
    medicationName: 'Lisinopril',
    dosage: '10mg',
    frequency: 'Once daily',
    route: 'oral',
    prescribedBy: 'Dr. Robert Johnson',
    startDate: '2025-01-01',
    endDate: undefined,
    instructions: 'Take in the morning',
    status: 'active',
    sideEffects: ['Dry cough', 'Dizziness'],
    interactions: ['Potassium supplements'],
    createdAt: '2025-01-01T10:00:00Z',
    updatedAt: '2025-01-15T10:00:00Z'
  },
  {
    id: 'med-003',
    residentId: 'R002',
    residentName: 'Mary Johnson',
    medicationName: 'Donepezil',
    dosage: '5mg',
    frequency: 'Once daily',
    route: 'oral',
    prescribedBy: 'Dr. Emily Davis',
    startDate: '2025-01-01',
    endDate: undefined,
    instructions: 'Take at bedtime',
    status: 'active',
    sideEffects: ['Nausea', 'Insomnia'],
    interactions: ['NSAIDs'],
    createdAt: '2025-01-01T10:00:00Z',
    updatedAt: '2025-01-15T10:00:00Z'
  }
];

const MOCK_ADMINISTRATIONS: MedicationAdministration[] = [
  {
    id: 'admin-001',
    medicationId: 'med-001',
    residentId: 'R001',
    administeredBy: 'Sarah Johnson',
    administeredAt: '2025-01-15T08:00:00Z',
    dosageGiven: '500mg',
    status: 'given',
    notes: 'Patient took medication with breakfast',
    vitalSigns: {
      bloodPressure: '130/80',
      heartRate: 72,
      temperature: 98.6
    }
  },
  {
    id: 'admin-002',
    medicationId: 'med-001',
    residentId: 'R001',
    administeredBy: 'Sarah Johnson',
    administeredAt: '2025-01-15T18:00:00Z',
    dosageGiven: '500mg',
    status: 'given',
    notes: 'Patient took medication with dinner'
  }
];

// API Service Class
class MedicationsApiService {
  private mockMode = process.env.REACT_APP_MOCK_MODE === 'true';

  async getAllMedications(): Promise<Medication[]> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 500));
      return MOCK_MEDICATIONS;
    }

    try {
      const response = await api.get('/medications');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch medications:', error);
      throw error;
    }
  }

  async getMedicationsByResident(residentId: string): Promise<Medication[]> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 300));
      return MOCK_MEDICATIONS.filter(med => med.residentId === residentId);
    }

    try {
      const response = await api.get(`/medications/resident/${residentId}`);
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch medications for resident ${residentId}:`, error);
      throw error;
    }
  }

  async getMedicationById(id: string): Promise<Medication> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 300));
      const medication = MOCK_MEDICATIONS.find(med => med.id === id);
      if (!medication) {
        throw new Error('Medication not found');
      }
      return medication;
    }

    try {
      const response = await api.get(`/medications/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch medication ${id}:`, error);
      throw error;
    }
  }

  async createMedication(medicationData: CreateMedicationRequest): Promise<Medication> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 800));
      const newMedication: Medication = {
        ...medicationData,
        id: `med-${String(MOCK_MEDICATIONS.length + 1).padStart(3, '0')}`,
        residentName: 'Mock Resident', // In real API, this would be populated from resident data
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      MOCK_MEDICATIONS.push(newMedication);
      return newMedication;
    }

    try {
      const response = await api.post('/medications', medicationData);
      return response.data;
    } catch (error) {
      console.error('Failed to create medication:', error);
      throw error;
    }
  }

  async updateMedication(id: string, medicationData: Partial<CreateMedicationRequest>): Promise<Medication> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 600));
      const index = MOCK_MEDICATIONS.findIndex(med => med.id === id);
      if (index === -1) {
        throw new Error('Medication not found');
      }
      
      MOCK_MEDICATIONS[index] = {
        ...MOCK_MEDICATIONS[index],
        ...medicationData,
        updatedAt: new Date().toISOString()
      };
      return MOCK_MEDICATIONS[index];
    }

    try {
      const response = await api.put(`/medications/${id}`, medicationData);
      return response.data;
    } catch (error) {
      console.error(`Failed to update medication ${id}:`, error);
      throw error;
    }
  }

  async deleteMedication(id: string): Promise<void> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 400));
      const index = MOCK_MEDICATIONS.findIndex(med => med.id === id);
      if (index === -1) {
        throw new Error('Medication not found');
      }
      MOCK_MEDICATIONS.splice(index, 1);
      return;
    }

    try {
      await api.delete(`/medications/${id}`);
    } catch (error) {
      console.error(`Failed to delete medication ${id}:`, error);
      throw error;
    }
  }

  async administerMedication(medicationId: string, administrationData: {
    dosageGiven: string;
    status: 'given' | 'refused' | 'missed' | 'held';
    notes?: string;
    vitalSigns?: {
      bloodPressure?: string;
      heartRate?: number;
      temperature?: number;
      oxygenSaturation?: number;
    };
  }): Promise<MedicationAdministration> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 500));
      const medication = MOCK_MEDICATIONS.find(med => med.id === medicationId);
      if (!medication) {
        throw new Error('Medication not found');
      }

      const newAdministration: MedicationAdministration = {
        id: `admin-${String(MOCK_ADMINISTRATIONS.length + 1).padStart(3, '0')}`,
        medicationId,
        residentId: medication.residentId,
        administeredBy: 'Current User', // In real API, this would come from auth context
        administeredAt: new Date().toISOString(),
        ...administrationData
      };
      
      MOCK_ADMINISTRATIONS.push(newAdministration);
      return newAdministration;
    }

    try {
      const response = await api.post(`/medications/${medicationId}/administer`, administrationData);
      return response.data;
    } catch (error) {
      console.error(`Failed to administer medication ${medicationId}:`, error);
      throw error;
    }
  }

  async getMedicationHistory(medicationId: string): Promise<MedicationAdministration[]> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 300));
      return MOCK_ADMINISTRATIONS.filter(admin => admin.medicationId === medicationId);
    }

    try {
      const response = await api.get(`/medications/${medicationId}/history`);
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch medication history for ${medicationId}:`, error);
      throw error;
    }
  }

  async getDueMedications(date?: string): Promise<Medication[]> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 400));
      // Return active medications for demo
      return MOCK_MEDICATIONS.filter(med => med.status === 'active');
    }

    try {
      const queryParam = date ? `?date=${date}` : '';
      const response = await api.get(`/medications/due${queryParam}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch due medications:', error);
      throw error;
    }
  }

  async checkDrugInteractions(medicationIds: string[]): Promise<{
    interactions: Array<{
      medication1: string;
      medication2: string;
      severity: 'minor' | 'moderate' | 'major';
      description: string;
    }>;
  }> {
    if (this.mockMode) {
      await new Promise(resolve => setTimeout(resolve, 600));
      return {
        interactions: [
          {
            medication1: 'Metformin',
            medication2: 'Alcohol',
            severity: 'moderate',
            description: 'May increase risk of lactic acidosis'
          }
        ]
      };
    }

    try {
      const response = await api.post('/medications/check-interactions', { medicationIds });
      return response.data;
    } catch (error) {
      console.error('Failed to check drug interactions:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const medicationsApi = new MedicationsApiService();
export default medicationsApi;
