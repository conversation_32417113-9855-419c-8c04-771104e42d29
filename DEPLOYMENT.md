# CareSyncAI Deployment Guide 🚀

This guide covers deployment options for CareSyncAI, from development to production environments.

## 📋 Prerequisites

### System Requirements
- **CPU**: 2+ cores (4+ recommended for production)
- **RAM**: 4GB minimum (8GB+ recommended)
- **Storage**: 20GB minimum (50GB+ for production)
- **OS**: Linux, macOS, or Windows with WSL2

### Software Dependencies
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+
- Python 3.11+
- Git

## 🏗️ Deployment Options

### 1. Development Environment

**Quick Setup:**
```bash
# Clone repository
git clone https://github.com/your-org/caresyncai.git
cd caresyncai

# Run setup script
chmod +x scripts/setup.sh
./scripts/setup.sh

# Start development servers
docker-compose up -d
```

**Manual Setup:**
```bash
# Backend
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py

# Frontend
cd frontend
npm install
npm start

# AI Service
cd ai
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python ai_api.py
```

### 2. Production Deployment

#### Option A: Docker Compose (Recommended)

1. **Prepare environment:**
   ```bash
   cp .env.example .env.production
   # Edit with production values
   ```

2. **Deploy:**
   ```bash
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh production
   ```

3. **Verify deployment:**
   ```bash
   docker-compose ps
   docker-compose logs -f
   ```

#### Option B: AWS Lambda Serverless

1. **Install AWS CLI and SAM:**
   ```bash
   pip install awscli aws-sam-cli
   aws configure
   ```

2. **Deploy backend:**
   ```bash
   cd backend
   sam build
   sam deploy --guided
   ```

3. **Deploy frontend to S3/CloudFront:**
   ```bash
   cd frontend
   npm run build
   aws s3 sync build/ s3://your-bucket-name
   ```

#### Option C: Kubernetes

1. **Create namespace:**
   ```bash
   kubectl create namespace caresyncai
   ```

2. **Deploy with Helm:**
   ```bash
   helm install caresyncai ./k8s/helm-chart
   ```

## 🔧 Configuration

### Environment Variables

**Production .env:**
```env
# Application
SECRET_KEY=your-super-secure-secret-key
DEBUG=False
ENVIRONMENT=production
ALLOWED_ORIGINS=https://yourdomain.com

# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Zoho Books
ZOHO_CLIENT_ID=your-zoho-client-id
ZOHO_CLIENT_SECRET=your-zoho-client-secret
ZOHO_REFRESH_TOKEN=your-zoho-refresh-token
ZOHO_ORGANIZATION_ID=your-zoho-org-id

# AWS
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
```

### SSL/TLS Configuration

**Nginx SSL Setup:**
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /api/ {
        proxy_pass http://backend:8000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 Monitoring & Logging

### Prometheus Configuration

**prometheus.yml:**
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'caresyncai-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    
  - job_name: 'caresyncai-ai'
    static_configs:
      - targets: ['ai-service:8001']
    metrics_path: '/metrics'
```

### Grafana Dashboards

Import the pre-configured dashboards:
- `monitoring/grafana/dashboards/caresyncai-overview.json`
- `monitoring/grafana/dashboards/ai-metrics.json`
- `monitoring/grafana/dashboards/system-metrics.json`

### Log Aggregation

**ELK Stack Setup:**
```bash
# Add to docker-compose.yml
elasticsearch:
  image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
  environment:
    - discovery.type=single-node
    - xpack.security.enabled=false

logstash:
  image: docker.elastic.co/logstash/logstash:8.8.0
  volumes:
    - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline

kibana:
  image: docker.elastic.co/kibana/kibana:8.8.0
  ports:
    - "5601:5601"
```

## 🔒 Security

### Security Checklist

- [ ] Change default passwords
- [ ] Enable SSL/TLS encryption
- [ ] Configure firewall rules
- [ ] Set up regular backups
- [ ] Enable audit logging
- [ ] Configure rate limiting
- [ ] Update dependencies regularly
- [ ] Use secrets management
- [ ] Enable CORS properly
- [ ] Configure CSP headers

### Backup Strategy

**Database Backup:**
```bash
# Automated daily backup
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/database"
mkdir -p $BACKUP_DIR

# Supabase backup (using pg_dump)
pg_dump $DATABASE_URL > $BACKUP_DIR/backup_$DATE.sql

# Compress and upload to S3
gzip $BACKUP_DIR/backup_$DATE.sql
aws s3 cp $BACKUP_DIR/backup_$DATE.sql.gz s3://your-backup-bucket/database/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

## 🚨 Troubleshooting

### Common Issues

**1. Database Connection Issues**
```bash
# Check Supabase connection
curl -H "apikey: YOUR_ANON_KEY" https://your-project.supabase.co/rest/v1/

# Check backend logs
docker-compose logs backend
```

**2. Frontend Build Failures**
```bash
# Clear cache and rebuild
cd frontend
rm -rf node_modules package-lock.json
npm install
npm run build
```

**3. AI Service Memory Issues**
```bash
# Increase memory limits in docker-compose.yml
ai-service:
  deploy:
    resources:
      limits:
        memory: 4G
      reservations:
        memory: 2G
```

**4. SSL Certificate Issues**
```bash
# Generate self-signed certificate for testing
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/key.pem \
  -out nginx/ssl/cert.pem
```

### Health Checks

**Service Health:**
```bash
# Check all services
curl http://localhost:8000/health  # Backend
curl http://localhost:3000/health  # Frontend
curl http://localhost:8001/health  # AI Service

# Check database
curl -H "apikey: YOUR_KEY" https://your-project.supabase.co/rest/v1/
```

**Performance Monitoring:**
```bash
# Check resource usage
docker stats

# Check logs
docker-compose logs -f --tail=100

# Check disk space
df -h
```

## 📈 Scaling

### Horizontal Scaling

**Load Balancer Configuration:**
```nginx
upstream backend_servers {
    server backend1:8000;
    server backend2:8000;
    server backend3:8000;
}

upstream ai_servers {
    server ai-service1:8001;
    server ai-service2:8001;
}
```

### Database Scaling

**Read Replicas:**
- Configure Supabase read replicas
- Update connection strings for read operations
- Implement connection pooling

### CDN Setup

**CloudFront Configuration:**
```json
{
  "Origins": [
    {
      "DomainName": "yourdomain.com",
      "OriginPath": "",
      "CustomOriginConfig": {
        "HTTPPort": 80,
        "HTTPSPort": 443,
        "OriginProtocolPolicy": "https-only"
      }
    }
  ],
  "DefaultCacheBehavior": {
    "TargetOriginId": "caresyncai-origin",
    "ViewerProtocolPolicy": "redirect-to-https",
    "CachePolicyId": "managed-caching-optimized"
  }
}
```

## 🔄 CI/CD Pipeline

**GitHub Actions Workflow:**
```yaml
name: Deploy CareSyncAI
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          
      - name: Run tests
        run: |
          cd backend && python -m pytest
          cd frontend && npm test
          
      - name: Deploy to production
        run: ./scripts/deploy.sh production
```

---

For additional support, see the main [README.md](README.md) <NAME_EMAIL>
