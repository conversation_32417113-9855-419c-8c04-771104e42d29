{"ast": null, "code": "import getMapData from './_getMapData.js';\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\nexport default mapCacheGet;", "map": {"version": 3, "names": ["getMapData", "mapCacheGet", "key", "get"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/node_modules/lodash-es/_mapCacheGet.js"], "sourcesContent": ["import getMapData from './_getMapData.js';\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\nexport default mapCacheGet;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOF,UAAU,CAAC,IAAI,EAAEE,GAAG,CAAC,CAACC,GAAG,CAACD,GAAG,CAAC;AACvC;AAEA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}