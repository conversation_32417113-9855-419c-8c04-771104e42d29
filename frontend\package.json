{"name": "caresyncai-frontend", "version": "1.0.0", "description": "CareSyncAI Frontend - Mobile-first React application for homecare management", "private": true, "dependencies": {"@supabase/supabase-js": "^2.38.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "date-fns": "^2.30.0", "formik": "^2.4.5", "yup": "^1.3.3", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "react-datepicker": "^4.24.0", "react-select": "^5.8.0", "react-dropzone": "^14.2.3", "lodash": "^4.17.21", "classnames": "^2.3.2"}, "devDependencies": {"tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}