import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  HeartIcon,
  ShieldCheckIcon,
  CpuChipIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ChartBarIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  StarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

const LandingPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const features = [
    {
      icon: HeartIcon,
      title: 'AI-Enhanced Care Management',
      description: 'Advanced AI algorithms help predict health risks, optimize care plans, and improve patient outcomes with real-time insights.'
    },
    {
      icon: ShieldCheckIcon,
      title: 'HIPAA-Compliant Security',
      description: 'Enterprise-grade security with end-to-end encryption, audit trails, and compliance monitoring for complete data protection.'
    },
    {
      icon: CpuChipIcon,
      title: 'AIMAR System',
      description: 'AI-powered Medication Administration Records system that reduces errors and ensures accurate medication management.'
    },
    {
      icon: UserGroupIcon,
      title: 'Comprehensive Resident Management',
      description: 'Complete resident profiles with medical history, care plans, emergency contacts, and real-time health monitoring.'
    },
    {
      icon: DocumentTextIcon,
      title: 'Digital Document Management',
      description: 'Paperless workflows with e-signatures, document scanning, fax integration, and automated compliance documentation.'
    },
    {
      icon: ChartBarIcon,
      title: 'Advanced Analytics & Reporting',
      description: 'Real-time dashboards, predictive analytics, and comprehensive reporting for data-driven care decisions.'
    }
  ];

  const testimonials = [
    {
      name: 'Serah',
      role: 'Care Provider',
      facility: 'Sun-r Care Home',
      content: 'Care-SolAI has transformed our operations. The AI insights help us provide better care while reducing administrative burden.',
      rating: 5
    },
    {
      name: 'Michael',
      role: 'Administrator',
      facility: 'Golden Years Residence',
      content: 'The HIPAA compliance features and security give us complete confidence. Our staff loves the intuitive interface.',
      rating: 5
    },
    {
      name: 'Priya',
      role: 'Care Coordinator',
      facility: 'Peaceful Gardens Care',
      content: 'The medication management system has virtually eliminated errors. The AI predictions help us stay ahead of health issues.',
      rating: 5
    }
  ];

  const stats = [
    { label: 'Healthcare Facilities', value: '500+' },
    { label: 'Residents Served', value: '25,000+' },
    { label: 'Medication Errors Prevented', value: '99.8%' },
    { label: 'Compliance Rate', value: '100%' }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <div className="h-10 w-10 bg-gradient-to-r from-purple-600 to-amber-600 rounded-xl flex items-center justify-center p-1 shadow-lg">
                  <img
                    src="/care-solai-logo.jpg"
                    alt="Care-SolAI Logo"
                    className="h-8 w-8 object-cover rounded-lg"
                  />
                </div>
                <span className="ml-2 text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Care-SolAI
                </span>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <a href="#features" className="text-gray-600 hover:text-purple-600 px-3 py-2 text-sm font-medium">Features</a>
                <button onClick={() => navigate('/pricing')} className="text-gray-600 hover:text-purple-600 px-3 py-2 text-sm font-medium">Pricing</button>
                <a href="#testimonials" className="text-gray-600 hover:text-purple-600 px-3 py-2 text-sm font-medium">Testimonials</a>
                <a href="#contact" className="text-gray-600 hover:text-purple-600 px-3 py-2 text-sm font-medium">Contact</a>
                {user ? (
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors"
                  >
                    Go to Dashboard
                  </button>
                ) : (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => navigate('/login')}
                      className="text-purple-600 hover:text-purple-700 px-3 py-2 text-sm font-medium transition-colors"
                    >
                      Sign In
                    </button>
                    <button
                      onClick={() => navigate('/signup')}
                      className="bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors"
                    >
                      Sign Up
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Next-Generation
              <span className="block bg-gradient-to-r from-amber-300 to-yellow-400 bg-clip-text text-transparent">
                Residential Healthcare
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              AI-powered healthcare management platform designed specifically for home-based care facilities. 
              Enhance patient care, ensure compliance, and streamline operations with intelligent automation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {user ? (
                <button
                  onClick={() => navigate('/dashboard')}
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-lg font-medium rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105"
                >
                  Go to Dashboard
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </button>
              ) : (
                <>
                  <button
                    onClick={() => navigate('/signup')}
                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-lg font-medium rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105"
                  >
                    Get Started
                    <ArrowRightIcon className="ml-2 h-5 w-5" />
                  </button>
                  <button
                    onClick={() => navigate('/login')}
                    className="inline-flex items-center px-8 py-4 border-2 border-white text-white text-lg font-medium rounded-lg hover:bg-white hover:text-purple-900 transition-all duration-300"
                  >
                    Sign In
                    <ArrowRightIcon className="ml-2 h-5 w-5" />
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-purple-600 mb-2">{stat.value}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Comprehensive Healthcare Management
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to provide exceptional residential healthcare with AI-powered insights and automation.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0">
                    <div className="h-12 w-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <h3 className="ml-4 text-xl font-semibold text-gray-900">{feature.title}</h3>
                </div>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Trusted by Healthcare Professionals
            </h2>
            <p className="text-xl text-gray-600">
              See what healthcare leaders are saying about Care-SolAI
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-lg">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <StarIcon key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">"{testimonial.content}"</p>
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-500">{testimonial.role}</div>
                  <div className="text-sm text-purple-600">{testimonial.facility}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Healthcare Operations?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join hundreds of healthcare facilities already using Care-SolAI to provide better patient care and streamline operations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {user ? (
              <button
                onClick={() => navigate('/dashboard')}
                className="inline-flex items-center px-8 py-4 bg-white text-purple-600 text-lg font-medium rounded-lg hover:bg-gray-100 transition-colors"
              >
                Access Dashboard
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </button>
            ) : (
              <>
                <button
                  onClick={() => navigate('/signup')}
                  className="inline-flex items-center px-8 py-4 bg-white text-purple-600 text-lg font-medium rounded-lg hover:bg-gray-100 transition-colors"
                >
                  Get Started
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </button>
                <button
                  onClick={() => navigate('/login')}
                  className="inline-flex items-center px-8 py-4 border-2 border-white text-white text-lg font-medium rounded-lg hover:bg-white hover:text-purple-600 transition-colors"
                >
                  Sign In
                </button>
              </>
            )}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Get in Touch
            </h2>
            <p className="text-xl text-gray-600">
              Ready to learn more? Contact our healthcare technology experts.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <PhoneIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Phone & Fax</h3>
              <p className="text-gray-600">Landline: +****************</p>
              <p className="text-gray-600">Fax: +****************</p>
              <p className="text-sm text-gray-500">Mon-Fri 8AM-6PM PST</p>
            </div>
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <EnvelopeIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Email</h3>
              <p className="text-gray-600"><EMAIL></p>
              <p className="text-sm text-gray-500">24/7 Support Available</p>
            </div>
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <MapPinIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Office</h3>
              <p className="text-gray-600">Washington State, USA</p>
              <p className="text-sm text-gray-500">Serving Healthcare Facilities Nationwide</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <div className="h-10 w-10 bg-gradient-to-r from-purple-600 to-amber-600 rounded-xl flex items-center justify-center p-1 shadow-lg">
                  <img
                    src="/care-solai-logo.jpg"
                    alt="Care-SolAI Logo"
                    className="h-8 w-8 object-cover rounded-lg"
                  />
                </div>
                <span className="ml-2 text-xl font-bold">Care-SolAI</span>
              </div>
              <p className="text-gray-400">
                Next-generation residential healthcare management platform powered by AI.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">Features</a></li>
                <li><a href="#" className="hover:text-white">Pricing</a></li>
                <li><a href="#" className="hover:text-white">Security</a></li>
                <li><a href="#" className="hover:text-white">Integrations</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">Documentation</a></li>
                <li><a href="#" className="hover:text-white">Training</a></li>
                <li><a href="#" className="hover:text-white">Help Center</a></li>
                <li><a href="#" className="hover:text-white">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">About</a></li>
                <li><a href="#" className="hover:text-white">Careers</a></li>
                <li><a href="#" className="hover:text-white">Privacy</a></li>
                <li><a href="#" className="hover:text-white">Terms</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 Care-SolAI. All rights reserved. HIPAA Compliant Healthcare Technology.</p>
            <p className="mt-2 text-sm text-gray-500">
              Created by <span className="text-amber-400 font-semibold">NYOHAKI</span> and <span className="text-purple-400 font-semibold">SAM INC.</span>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
