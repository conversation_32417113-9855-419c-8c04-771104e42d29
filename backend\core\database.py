"""
Database connection and initialization for CareSyncAI
"""

from supabase import create_client, Client
from typing import Optional, Dict, Any, List
import asyncio
import logging
from core.config import settings

logger = logging.getLogger(__name__)

class SupabaseClient:
    """Supabase client wrapper for CareSyncAI"""
    
    def __init__(self):
        self.client: Optional[Client] = None
        self.service_client: Optional[Client] = None
    
    async def connect(self):
        """Initialize Supabase connections"""
        try:
            # Regular client with anon key (for user operations)
            self.client = create_client(
                settings.SUPABASE_URL,
                settings.SUPABASE_ANON_KEY
            )
            
            # Service client with service role key (for admin operations)
            self.service_client = create_client(
                settings.SUPABASE_URL,
                settings.SUPABASE_SERVICE_ROLE_KEY
            )
            
            logger.info("✅ Supabase clients initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Supabase clients: {e}")
            raise
    
    def get_client(self, use_service_role: bool = False) -> Client:
        """Get Supabase client"""
        if use_service_role:
            if not self.service_client:
                raise RuntimeError("Service client not initialized")
            return self.service_client
        else:
            if not self.client:
                raise RuntimeError("Client not initialized")
            return self.client

# Global Supabase client instance
supabase_client = SupabaseClient()

async def init_db():
    """Initialize database connection"""
    await supabase_client.connect()

def get_db(use_service_role: bool = False) -> Client:
    """Dependency to get database client"""
    return supabase_client.get_client(use_service_role)

class DatabaseService:
    """Base database service class"""
    
    def __init__(self, table_name: str, use_service_role: bool = False):
        self.table_name = table_name
        self.use_service_role = use_service_role
        self.client = get_db(use_service_role)
    
    async def create(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new record"""
        try:
            result = self.client.table(self.table_name).insert(data).execute()
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to create record")
        except Exception as e:
            logger.error(f"Error creating record in {self.table_name}: {e}")
            raise
    
    async def get_by_id(self, record_id: str) -> Optional[Dict[str, Any]]:
        """Get record by ID"""
        try:
            result = self.client.table(self.table_name).select("*").eq("id", record_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting record from {self.table_name}: {e}")
            raise
    
    async def get_all(
        self, 
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 100,
        offset: int = 0,
        order_by: Optional[str] = None,
        ascending: bool = True
    ) -> List[Dict[str, Any]]:
        """Get all records with optional filtering"""
        try:
            query = self.client.table(self.table_name).select("*")
            
            # Apply filters
            if filters:
                for key, value in filters.items():
                    if isinstance(value, list):
                        query = query.in_(key, value)
                    else:
                        query = query.eq(key, value)
            
            # Apply ordering
            if order_by:
                query = query.order(order_by, desc=not ascending)
            
            # Apply pagination
            query = query.range(offset, offset + limit - 1)
            
            result = query.execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error getting records from {self.table_name}: {e}")
            raise
    
    async def update(self, record_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a record"""
        try:
            result = self.client.table(self.table_name).update(data).eq("id", record_id).execute()
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to update record")
        except Exception as e:
            logger.error(f"Error updating record in {self.table_name}: {e}")
            raise
    
    async def delete(self, record_id: str) -> bool:
        """Delete a record"""
        try:
            result = self.client.table(self.table_name).delete().eq("id", record_id).execute()
            return len(result.data) > 0
        except Exception as e:
            logger.error(f"Error deleting record from {self.table_name}: {e}")
            raise
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count records with optional filtering"""
        try:
            query = self.client.table(self.table_name).select("id", count="exact")
            
            if filters:
                for key, value in filters.items():
                    if isinstance(value, list):
                        query = query.in_(key, value)
                    else:
                        query = query.eq(key, value)
            
            result = query.execute()
            return result.count or 0
        except Exception as e:
            logger.error(f"Error counting records in {self.table_name}: {e}")
            raise
    
    async def search(
        self, 
        search_term: str, 
        search_fields: List[str],
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Search records by text in specified fields"""
        try:
            # Build search query using ilike for case-insensitive search
            query = self.client.table(self.table_name).select("*")
            
            # Apply search to each field with OR logic
            search_conditions = []
            for field in search_fields:
                search_conditions.append(f"{field}.ilike.%{search_term}%")
            
            if search_conditions:
                query = query.or_(",".join(search_conditions))
            
            query = query.limit(limit)
            result = query.execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error searching records in {self.table_name}: {e}")
            raise

# Real-time subscription helper
class RealtimeSubscription:
    """Helper class for Supabase real-time subscriptions"""
    
    def __init__(self, table_name: str):
        self.table_name = table_name
        self.client = get_db()
    
    def subscribe_to_changes(self, callback, event_type: str = "*"):
        """Subscribe to real-time changes"""
        try:
            return self.client.table(self.table_name).on(event_type, callback).subscribe()
        except Exception as e:
            logger.error(f"Error subscribing to {self.table_name}: {e}")
            raise
