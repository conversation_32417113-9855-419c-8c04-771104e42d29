import { api } from './api';

// Types
export interface DocumentScanResult {
  id: string;
  originalFileName: string;
  processedImageUrl: string;
  extractedText: string;
  documentType: string;
  confidence: number;
  extractedData: {
    [key: string]: any;
  };
  complianceCheck: {
    isCompliant: boolean;
    issues: string[];
    recommendations: string[];
  };
}

export interface ESignatureRequest {
  documentId: string;
  signerEmail: string;
  signerName: string;
  signerRole: string;
  requiredFields: {
    name: string;
    type: 'signature' | 'initial' | 'date' | 'text';
    position: { x: number; y: number };
    required: boolean;
  }[];
  expirationDate?: string;
  reminderSettings?: {
    enabled: boolean;
    intervalDays: number;
  };
}

export interface ESignatureStatus {
  documentId: string;
  status: 'pending' | 'signed' | 'declined' | 'expired';
  signedAt?: string;
  signedBy?: string;
  ipAddress?: string;
  deviceInfo?: string;
  signatureImageUrl?: string;
}

class DocumentService {
  // Document Scanning with AI
  async scanDocument(file: File): Promise<DocumentScanResult> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      // Mock AI document scanning
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockResult: DocumentScanResult = {
            id: `scan_${Date.now()}`,
            originalFileName: file.name,
            processedImageUrl: URL.createObjectURL(file),
            extractedText: this.getMockExtractedText(file.name),
            documentType: this.detectDocumentType(file.name),
            confidence: 0.95,
            extractedData: this.getMockExtractedData(file.name),
            complianceCheck: {
              isCompliant: true,
              issues: [],
              recommendations: ['Document appears complete and properly formatted']
            }
          };
          resolve(mockResult);
        }, 2000);
      });
    }

    // Real implementation would use services like:
    // - AWS Textract for OCR
    // - Google Document AI
    // - Azure Form Recognizer
    // - Custom AI models
    
    const formData = new FormData();
    formData.append('document', file);
    
    try {
      const response = await api.post<DocumentScanResult>('/documents/scan', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw new Error('Failed to scan document');
    }
  }

  // Batch document scanning
  async scanMultipleDocuments(files: FileList): Promise<DocumentScanResult[]> {
    const results: DocumentScanResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const result = await this.scanDocument(files[i]);
      results.push(result);
    }
    
    return results;
  }

  // E-Signature functionality
  async sendForSignature(request: ESignatureRequest): Promise<{ signatureRequestId: string; signUrl: string }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            signatureRequestId: `sig_req_${Date.now()}`,
            signUrl: `https://mock-esign.caresyncai.com/sign/${request.documentId}`
          });
        }, 1000);
      });
    }

    // Real implementation would integrate with:
    // - DocuSign API
    // - Adobe Sign API
    // - HelloSign API
    // - PandaDoc API
    
    try {
      const response = await api.post('/documents/esign/send', request);
      return response.data;
    } catch (error) {
      throw new Error('Failed to send document for signature');
    }
  }

  // Check signature status
  async getSignatureStatus(documentId: string): Promise<ESignatureStatus> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return {
        documentId,
        status: 'pending',
        // Mock data would vary based on document
      };
    }

    try {
      const response = await api.get<ESignatureStatus>(`/documents/esign/status/${documentId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to get signature status');
    }
  }

  // Document compliance checking
  async checkCompliance(documentId: string): Promise<{
    isCompliant: boolean;
    score: number;
    issues: string[];
    recommendations: string[];
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return {
        isCompliant: true,
        score: 95,
        issues: [],
        recommendations: [
          'Document meets all regulatory requirements',
          'Consider adding digital timestamp for enhanced security'
        ]
      };
    }

    try {
      const response = await api.post(`/documents/compliance/check`, { documentId });
      return response.data;
    } catch (error) {
      throw new Error('Failed to check document compliance');
    }
  }

  // Document search with AI
  async searchDocuments(query: string, filters?: {
    documentType?: string;
    dateRange?: { start: string; end: string };
    residentId?: string;
    status?: string;
  }): Promise<any[]> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      // Mock search results
      return [
        {
          id: '1',
          name: 'Care Plan - Margaret Thompson',
          type: 'care_plan',
          relevanceScore: 0.95,
          matchedContent: 'Care plan for diabetes management...'
        }
      ];
    }

    try {
      const response = await api.post('/documents/search', { query, filters });
      return response.data;
    } catch (error) {
      throw new Error('Failed to search documents');
    }
  }

  // Helper methods for mock data
  private getMockExtractedText(fileName: string): string {
    if (fileName.toLowerCase().includes('care')) {
      return `CARE PLAN ASSESSMENT
      
Resident: Margaret Thompson
Date: January 20, 2024
Care Level: Moderate Assistance

ACTIVITIES OF DAILY LIVING:
- Bathing: Requires assistance
- Dressing: Independent with supervision
- Medication: Requires administration
- Mobility: Uses walker, stable

MEDICAL CONDITIONS:
- Type 2 Diabetes
- Hypertension
- Mild cognitive impairment

CARE GOALS:
1. Maintain current mobility level
2. Monitor blood glucose levels
3. Encourage social activities`;
    }
    
    if (fileName.toLowerCase().includes('medication')) {
      return `MEDICATION CONSENT FORM
      
Resident: William Anderson
Date: January 18, 2024

I hereby consent to the administration of the following medications:
- Lisinopril 10mg daily for blood pressure
- Metformin 500mg twice daily for diabetes

Prescribing Physician: Dr. Sarah Smith
Emergency Contact: Mary Anderson (Daughter)`;
    }
    
    return `RESIDENTIAL CARE DOCUMENT
    
This document contains important information regarding residential care services.
Please review all sections carefully and sign where indicated.`;
  }

  private detectDocumentType(fileName: string): string {
    const name = fileName.toLowerCase();
    if (name.includes('care') || name.includes('plan')) return 'Care Plan';
    if (name.includes('medication') || name.includes('med')) return 'Medication Consent';
    if (name.includes('admission')) return 'Admission Agreement';
    if (name.includes('assessment')) return 'Health Assessment';
    if (name.includes('incident')) return 'Incident Report';
    if (name.includes('insurance')) return 'Insurance Document';
    return 'General Document';
  }

  private getMockExtractedData(fileName: string): any {
    const name = fileName.toLowerCase();
    
    if (name.includes('care')) {
      return {
        residentName: 'Margaret Thompson',
        assessmentDate: '2024-01-20',
        careLevel: 'Moderate Assistance',
        medicalConditions: ['Type 2 Diabetes', 'Hypertension'],
        careGoals: ['Maintain mobility', 'Monitor glucose', 'Social activities']
      };
    }
    
    if (name.includes('medication')) {
      return {
        residentName: 'William Anderson',
        medications: [
          { name: 'Lisinopril', dosage: '10mg', frequency: 'daily' },
          { name: 'Metformin', dosage: '500mg', frequency: 'twice daily' }
        ],
        prescribingPhysician: 'Dr. Sarah Smith'
      };
    }
    
    return {
      documentDate: new Date().toISOString().split('T')[0],
      extractedFields: ['Standard document fields detected']
    };
  }

  // Document templates for residential care
  async getDocumentTemplates(): Promise<any[]> {
    return [
      {
        id: 'admission_agreement',
        name: 'Admission Agreement',
        description: 'Standard admission agreement for new residents',
        category: 'admission',
        requiredFields: ['resident_name', 'emergency_contact', 'medical_conditions'],
        signatures: ['resident', 'family_member', 'administrator']
      },
      {
        id: 'care_plan',
        name: 'Individual Care Plan',
        description: 'Personalized care plan template',
        category: 'care_planning',
        requiredFields: ['care_goals', 'medical_needs', 'preferences'],
        signatures: ['nurse', 'care_coordinator']
      },
      {
        id: 'medication_consent',
        name: 'Medication Administration Consent',
        description: 'Consent for medication administration',
        category: 'medication',
        requiredFields: ['medications', 'allergies', 'physician_orders'],
        signatures: ['resident', 'physician', 'nurse']
      },
      {
        id: 'incident_report',
        name: 'Incident Report Form',
        description: 'Report for any incidents or accidents',
        category: 'incident',
        requiredFields: ['incident_details', 'witnesses', 'actions_taken'],
        signatures: ['reporting_staff', 'supervisor']
      }
    ];
  }
}

export const documentService = new DocumentService();
