import { api } from './api';

// Types for Medical AI
export interface HealthPrediction {
  type: 'fall_risk' | 'health_decline' | 'medication_interaction' | 'emergency_risk';
  probability: number;
  timeframe: string;
  confidence: number;
  factors: string[];
  recommendations: string[];
}

export interface VitalSignsAnalysis {
  residentId: string;
  vitalType: string;
  currentValue: number;
  normalRange: { min: number; max: number };
  trend: 'improving' | 'stable' | 'declining';
  riskScore: number;
  predictions: HealthPrediction[];
  alerts: {
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    actionRequired: boolean;
  }[];
}

export interface FallRiskAssessment {
  residentId: string;
  riskScore: number; // 0-100
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: {
    mobility: number;
    medication: number;
    cognitive: number;
    environmental: number;
    history: number;
  };
  interventions: string[];
  nextAssessment: string;
}

export interface HealthDeclineDetection {
  residentId: string;
  declineScore: number;
  declineRate: 'stable' | 'gradual' | 'moderate' | 'rapid';
  affectedDomains: string[];
  earlyWarningSignals: string[];
  recommendedActions: string[];
  timeToIntervention: string;
}

export interface MedicationInteractionAnalysis {
  residentId: string;
  interactions: {
    medication1: string;
    medication2: string;
    severity: 'minor' | 'moderate' | 'major' | 'contraindicated';
    description: string;
    clinicalEffect: string;
    management: string[];
  }[];
  recommendations: string[];
  monitoringRequired: boolean;
}

class MedicalAIService {
  // Predictive Fall Risk Analysis
  async analyzeFallRisk(residentId: string): Promise<FallRiskAssessment> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockAssessment: FallRiskAssessment = {
            residentId,
            riskScore: Math.floor(Math.random() * 100),
            riskLevel: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
            factors: {
              mobility: Math.floor(Math.random() * 100),
              medication: Math.floor(Math.random() * 100),
              cognitive: Math.floor(Math.random() * 100),
              environmental: Math.floor(Math.random() * 100),
              history: Math.floor(Math.random() * 100),
            },
            interventions: [
              'Implement fall prevention protocol',
              'Increase supervision during mobility',
              'Review medications for sedating effects',
              'Environmental safety assessment',
              'Physical therapy evaluation'
            ],
            nextAssessment: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          };
          resolve(mockAssessment);
        }, 1500);
      });
    }

    try {
      const response = await api.post<FallRiskAssessment>('/ai/fall-risk-analysis', { residentId });
      return response.data;
    } catch (error) {
      throw new Error('Failed to analyze fall risk');
    }
  }

  // Health Decline Detection
  async detectHealthDecline(residentId: string): Promise<HealthDeclineDetection> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockDetection: HealthDeclineDetection = {
            residentId,
            declineScore: Math.floor(Math.random() * 100),
            declineRate: ['stable', 'gradual', 'moderate', 'rapid'][Math.floor(Math.random() * 4)] as any,
            affectedDomains: ['Cognitive Function', 'Physical Mobility', 'Nutritional Status'],
            earlyWarningSignals: [
              'Decreased appetite over 3 days',
              'Increased confusion in evenings',
              'Reduced participation in activities',
              'Changes in sleep patterns'
            ],
            recommendedActions: [
              'Immediate physician consultation',
              'Comprehensive geriatric assessment',
              'Nutritional evaluation',
              'Medication review',
              'Family notification'
            ],
            timeToIntervention: '24-48 hours'
          };
          resolve(mockDetection);
        }, 2000);
      });
    }

    try {
      const response = await api.post<HealthDeclineDetection>('/ai/health-decline-detection', { residentId });
      return response.data;
    } catch (error) {
      throw new Error('Failed to detect health decline');
    }
  }

  // Vital Signs AI Analysis
  async analyzeVitalSigns(residentId: string, vitalData: any): Promise<VitalSignsAnalysis> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockAnalysis: VitalSignsAnalysis = {
            residentId,
            vitalType: vitalData.type || 'blood_pressure',
            currentValue: vitalData.value || 140,
            normalRange: { min: 90, max: 140 },
            trend: ['improving', 'stable', 'declining'][Math.floor(Math.random() * 3)] as any,
            riskScore: Math.floor(Math.random() * 100),
            predictions: [
              {
                type: 'health_decline',
                probability: 0.75,
                timeframe: '48-72 hours',
                confidence: 0.89,
                factors: ['Elevated blood pressure', 'Medication non-compliance', 'Stress indicators'],
                recommendations: ['Increase monitoring frequency', 'Medication review', 'Stress reduction interventions']
              }
            ],
            alerts: [
              {
                severity: 'medium',
                message: 'Blood pressure trending upward over past 3 readings',
                actionRequired: true
              }
            ]
          };
          resolve(mockAnalysis);
        }, 1000);
      });
    }

    try {
      const response = await api.post<VitalSignsAnalysis>('/ai/vital-signs-analysis', { residentId, vitalData });
      return response.data;
    } catch (error) {
      throw new Error('Failed to analyze vital signs');
    }
  }

  // Medication Interaction Analysis
  async analyzeMedicationInteractions(residentId: string, medications: string[]): Promise<MedicationInteractionAnalysis> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockAnalysis: MedicationInteractionAnalysis = {
            residentId,
            interactions: [
              {
                medication1: 'Warfarin',
                medication2: 'Aspirin',
                severity: 'major',
                description: 'Increased risk of bleeding',
                clinicalEffect: 'Enhanced anticoagulant effect leading to increased bleeding risk',
                management: [
                  'Monitor INR more frequently',
                  'Watch for signs of bleeding',
                  'Consider alternative pain management',
                  'Educate staff on bleeding precautions'
                ]
              }
            ],
            recommendations: [
              'Consult with pharmacist for medication review',
              'Implement enhanced monitoring protocol',
              'Consider therapeutic alternatives',
              'Update care plan with interaction precautions'
            ],
            monitoringRequired: true
          };
          resolve(mockAnalysis);
        }, 1500);
      });
    }

    try {
      const response = await api.post<MedicationInteractionAnalysis>('/ai/medication-interactions', { residentId, medications });
      return response.data;
    } catch (error) {
      throw new Error('Failed to analyze medication interactions');
    }
  }

  // Predictive Health Analytics Dashboard
  async getHealthAnalyticsDashboard(facilityId: string): Promise<{
    overallHealthScore: number;
    riskDistribution: { low: number; medium: number; high: number; critical: number };
    trendingConcerns: string[];
    predictiveInsights: {
      fallPrevention: { prevented: number; predicted: number };
      healthDecline: { earlyDetection: number; interventions: number };
      medicationSafety: { interactionsPrevented: number; adherenceImproved: number };
    };
    aiModelPerformance: {
      accuracy: number;
      lastUpdated: string;
      dataPoints: number;
    };
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            overallHealthScore: 87,
            riskDistribution: { low: 12, medium: 4, high: 2, critical: 0 },
            trendingConcerns: [
              'Seasonal flu risk increasing',
              'Medication adherence declining in 3 residents',
              'Fall risk elevated due to weather changes'
            ],
            predictiveInsights: {
              fallPrevention: { prevented: 15, predicted: 23 },
              healthDecline: { earlyDetection: 8, interventions: 12 },
              medicationSafety: { interactionsPrevented: 6, adherenceImproved: 18 }
            },
            aiModelPerformance: {
              accuracy: 94.2,
              lastUpdated: new Date().toISOString(),
              dataPoints: 15847
            }
          });
        }, 2000);
      });
    }

    try {
      const response = await api.get(`/ai/health-analytics-dashboard/${facilityId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to get health analytics dashboard');
    }
  }

  // Real-time Health Monitoring
  async startRealTimeMonitoring(residentId: string): Promise<{ monitoringId: string; status: string }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return {
        monitoringId: `monitor_${Date.now()}`,
        status: 'active'
      };
    }

    try {
      const response = await api.post('/ai/start-monitoring', { residentId });
      return response.data;
    } catch (error) {
      throw new Error('Failed to start real-time monitoring');
    }
  }

  // AI Care Plan Optimization
  async optimizeCarePlan(residentId: string, currentCarePlan: any): Promise<{
    optimizedPlan: any;
    improvements: string[];
    expectedOutcomes: string[];
    confidence: number;
  }> {
    const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
    
    if (isMockMode) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            optimizedPlan: {
              ...currentCarePlan,
              aiOptimizations: [
                'Adjusted medication timing for better efficacy',
                'Added fall prevention exercises',
                'Optimized meal planning for diabetes management'
              ]
            },
            improvements: [
              'Reduced fall risk by 23%',
              'Improved medication adherence prediction',
              'Enhanced quality of life metrics'
            ],
            expectedOutcomes: [
              'Better blood sugar control',
              'Increased mobility and independence',
              'Reduced emergency interventions'
            ],
            confidence: 0.91
          });
        }, 2500);
      });
    }

    try {
      const response = await api.post('/ai/optimize-care-plan', { residentId, currentCarePlan });
      return response.data;
    } catch (error) {
      throw new Error('Failed to optimize care plan');
    }
  }
}

export const medicalAIService = new MedicalAIService();
