# 🏗️ Care-SolAI Backend Setup Guide

## 📋 **Prerequisites**

### **🔧 System Requirements**
- **Python**: 3.9 or higher
- **Node.js**: 16.0 or higher (for Supabase CLI)
- **Git**: Latest version
- **Docker**: Optional, for containerized deployment
- **PostgreSQL**: 13+ (if not using Supabase)

### **☁️ External Services Required**
- **Supabase Account**: For database and authentication
- **Zoho Books Account**: For billing integration (optional)
- **AWS Account**: For S3 storage (optional)
- **Hugging Face Account**: For AI/ML models (optional)

## 🚀 **Step-by-Step Setup**

### **Step 1: Clone and Setup Project**

```bash
# Clone the repository
git clone https://github.com/your-org/care-solai.git
cd care-solai/backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Upgrade pip
python -m pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt
```

### **Step 2: Supabase Setup**

#### **🔐 Create Supabase Project**
1. Go to [supabase.com](https://supabase.com)
2. Click "New Project"
3. Choose organization and enter project details:
   - **Name**: Care-SolAI
   - **Database Password**: Generate strong password
   - **Region**: Choose closest to your users
4. Wait for project creation (2-3 minutes)

#### **📊 Database Schema Setup**
1. Go to **SQL Editor** in Supabase dashboard
2. Create the database schema:

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'nurse', 'caregiver', 'billing');
CREATE TYPE record_type AS ENUM ('medical', 'medication', 'vitals', 'notes', 'assessment');
CREATE TYPE billing_status AS ENUM ('pending', 'paid', 'overdue', 'cancelled');

-- Caregivers table
CREATE TABLE caregivers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role user_role NOT NULL DEFAULT 'caregiver',
    phone VARCHAR(20),
    license_number VARCHAR(50),
    license_expiry DATE,
    is_active BOOLEAN DEFAULT true,
    hire_date DATE DEFAULT CURRENT_DATE,
    department VARCHAR(100),
    certifications TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Patients table
CREATE TABLE patients (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    gender VARCHAR(10),
    ssn_encrypted TEXT, -- Encrypted SSN
    address JSONB,
    phone VARCHAR(20),
    emergency_contact JSONB,
    insurance_info JSONB,
    medical_history JSONB,
    allergies TEXT[],
    medications JSONB,
    admission_date DATE DEFAULT CURRENT_DATE,
    discharge_date DATE,
    room_number VARCHAR(20),
    bed_number VARCHAR(20),
    care_level VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Medical Records table
CREATE TABLE medical_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    caregiver_id UUID REFERENCES caregivers(id),
    record_type record_type NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    vital_signs JSONB,
    medications JSONB,
    observations TEXT,
    metadata JSONB,
    is_confidential BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Documents table
CREATE TABLE records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    caregiver_id UUID REFERENCES caregivers(id),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255),
    file_type VARCHAR(50),
    file_size INTEGER,
    storage_path TEXT,
    document_type VARCHAR(50),
    metadata JSONB,
    is_signed BOOLEAN DEFAULT false,
    signed_by UUID REFERENCES caregivers(id),
    signed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inventory table
CREATE TABLE shop_inventory (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    description TEXT,
    sku VARCHAR(100) UNIQUE,
    quantity INTEGER DEFAULT 0,
    unit_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    supplier VARCHAR(200),
    supplier_contact JSONB,
    reorder_level INTEGER DEFAULT 10,
    max_stock_level INTEGER,
    expiry_date DATE,
    location VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Billing table
CREATE TABLE billing (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status billing_status DEFAULT 'pending',
    due_date DATE,
    paid_date DATE,
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    zoho_invoice_id VARCHAR(100),
    insurance_claim_id VARCHAR(100),
    line_items JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit Log table for HIPAA compliance
CREATE TABLE audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    caregiver_id UUID REFERENCES caregivers(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    patient_id UUID REFERENCES patients(id),
    ip_address INET,
    user_agent TEXT,
    changes JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_caregivers_user_id ON caregivers(user_id);
CREATE INDEX idx_caregivers_role ON caregivers(role);
CREATE INDEX idx_patients_active ON patients(is_active);
CREATE INDEX idx_patients_admission_date ON patients(admission_date);
CREATE INDEX idx_medical_records_patient_id ON medical_records(patient_id);
CREATE INDEX idx_medical_records_caregiver_id ON medical_records(caregiver_id);
CREATE INDEX idx_medical_records_created_at ON medical_records(created_at);
CREATE INDEX idx_records_patient_id ON records(patient_id);
CREATE INDEX idx_billing_patient_id ON billing(patient_id);
CREATE INDEX idx_billing_status ON billing(status);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_patient_id ON audit_logs(patient_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE caregivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE records ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Caregivers can view their own profile and other caregivers (limited info)
CREATE POLICY "Caregivers can view own profile" ON caregivers
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all caregivers" ON caregivers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM caregivers 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Patients access policies
CREATE POLICY "Caregivers can view assigned patients" ON patients
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM caregivers 
            WHERE user_id = auth.uid() AND is_active = true
        )
    );

-- Medical records access policies
CREATE POLICY "Caregivers can view medical records" ON medical_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM caregivers 
            WHERE user_id = auth.uid() AND is_active = true
        )
    );

-- Create functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_caregivers_updated_at BEFORE UPDATE ON caregivers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_patients_updated_at BEFORE UPDATE ON patients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medical_records_updated_at BEFORE UPDATE ON medical_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shop_inventory_updated_at BEFORE UPDATE ON shop_inventory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_updated_at BEFORE UPDATE ON billing
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### **🔐 Authentication Setup**
1. Go to **Authentication** > **Settings**
2. Configure:
   - **Site URL**: `http://localhost:3000` (development)
   - **Redirect URLs**: Add your frontend URLs
3. Enable **Email** provider
4. Optional: Enable **Google**, **GitHub** providers

### **Step 3: Environment Configuration**

#### **📁 Create Backend .env File**
Create `.env` file in the `backend/` directory:

```env
# Application Settings
APP_NAME=Care-SolAI
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# Security
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Supabase Configuration (Replace with your actual values)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Database (Optional - Supabase handles this)
DATABASE_URL=postgresql://postgres:password@localhost:5432/care_solai

# Zoho Books Integration (Optional)
ZOHO_CLIENT_ID=your-zoho-client-id
ZOHO_CLIENT_SECRET=your-zoho-client-secret
ZOHO_REFRESH_TOKEN=your-zoho-refresh-token
ZOHO_ORGANIZATION_ID=your-zoho-organization-id
ZOHO_BASE_URL=https://www.zohoapis.com/books/v3

# AWS Configuration (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=care-solai-documents

# AI/ML Configuration (Optional)
HUGGINGFACE_API_KEY=your-huggingface-api-key
OPENAI_API_KEY=your-openai-api-key
REPLICATE_API_TOKEN=your-replicate-api-token

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:3001","https://your-domain.com"]

# Logging and Monitoring
LOG_LEVEL=INFO
LOG_FORMAT=json
PROMETHEUS_PORT=9090

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Upload Settings
MAX_FILE_SIZE=52428800  # 50MB in bytes
ALLOWED_FILE_TYPES=["application/pdf","image/jpeg","image/png","image/gif","text/plain","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"]

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Cache Settings (Optional)
REDIS_URL=redis://localhost:6379
CACHE_TTL=300

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# AI Model Settings
FALL_RISK_MODEL_VERSION=v1.2.0
NLP_MODEL_NAME=distilbert-base-uncased
PREDICTION_CONFIDENCE_THRESHOLD=0.7
```

### **Step 4: Get Supabase Credentials**

#### **🔑 Find Your Credentials**
1. Go to **Settings** > **API** in your Supabase dashboard
2. Copy the following:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

#### **⚠️ Security Note**
- **Never commit** the service role key to version control
- **Use environment variables** for all sensitive data
- **Rotate keys regularly** in production

### **Step 5: Start the Backend Server**

```bash
# Make sure you're in the backend directory
cd backend

# Activate virtual environment (if not already active)
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Start the development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Alternative: Use the Python script
python main.py
```

#### **✅ Verify Backend is Running**
1. Open browser to `http://localhost:8000`
2. You should see: `{"message": "Care-SolAI Backend API is running"}`
3. Check API documentation: `http://localhost:8000/docs`
4. Health check: `http://localhost:8000/health`

### **Step 6: Test API Endpoints**

#### **🧪 Basic Health Check**
```bash
curl http://localhost:8000/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-01T12:00:00Z",
  "version": "1.0.0",
  "database": "connected"
}
```

#### **🔐 Test Authentication**
```bash
# Register a new user (if registration is enabled)
curl -X POST "http://localhost:8000/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpass123",
    "first_name": "Test",
    "last_name": "User",
    "role": "caregiver"
  }'

# Login
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpass123"
  }'
```

### **Step 7: Frontend Integration**

#### **🔗 Update Frontend Environment**
Update `frontend/.env`:
```env
# Backend API URL
REACT_APP_API_URL=http://localhost:8000

# Supabase Configuration (same as backend)
REACT_APP_SUPABASE_URL=https://your-project-id.supabase.co
REACT_APP_SUPABASE_ANON_KEY=your-supabase-anon-key

# Disable mock authentication
REACT_APP_MOCK_AUTH=false
```

#### **🚀 Start Frontend**
```bash
cd frontend
npm install
npm start
```

### **Step 8: Verify Full Integration**

#### **✅ Test Complete Flow**
1. **Frontend**: Navigate to `http://localhost:3000`
2. **Registration**: Create a new account
3. **Login**: Sign in with your credentials
4. **Dashboard**: Verify you can access the dashboard
5. **API Calls**: Check browser network tab for successful API calls

## 🐳 **Docker Deployment (Optional)**

### **🏗️ Build Docker Image**
```bash
# Build the backend image
docker build -t care-solai-backend .

# Run the container
docker run -p 8000:8000 --env-file .env care-solai-backend
```

### **🔧 Docker Compose**
Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    env_file:
      - ./backend/.env
    depends_on:
      - redis
  
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
  
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

## 🔧 **Troubleshooting**

### **Common Issues**

#### **❌ "Module not found" errors**
```bash
# Reinstall dependencies
pip install -r requirements.txt --force-reinstall
```

#### **❌ Database connection errors**
- Verify Supabase credentials in `.env`
- Check if Supabase project is active
- Ensure database schema is created

#### **❌ CORS errors**
- Add your frontend URL to `ALLOWED_ORIGINS` in `.env`
- Restart the backend server

#### **❌ Authentication errors**
- Verify JWT secret key is set
- Check token expiration settings
- Ensure Supabase Auth is properly configured

### **🔍 Debug Mode**
```bash
# Run with debug logging
LOG_LEVEL=DEBUG uvicorn main:app --reload
```

## 📊 **Production Deployment**

### **🚀 AWS Lambda Deployment**
1. Install Serverless Framework
2. Configure `serverless.yml`
3. Deploy with `serverless deploy`

### **🔒 Production Security**
- Change all default passwords
- Use strong JWT secret keys
- Enable HTTPS/TLS
- Configure proper CORS origins
- Set up monitoring and logging
- Regular security updates

## 📝 **Next Steps**

After successful setup:
1. **Configure Zoho Books** integration (optional)
2. **Set up AWS S3** for file storage (optional)
3. **Configure AI/ML** services (optional)
4. **Set up monitoring** and logging
5. **Configure backup** strategies
6. **Plan production** deployment

**Your Care-SolAI backend is now ready for development and testing!** 🎉

## 💡 **Implementation Examples**

### **🔐 Custom Authentication Middleware**
```python
# middleware/auth_middleware.py
from fastapi import Request, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from core.auth import verify_token
import logging

logger = logging.getLogger(__name__)

class AuthMiddleware:
    def __init__(self):
        self.security = HTTPBearer()

    async def __call__(self, request: Request, credentials: HTTPAuthorizationCredentials):
        try:
            # Verify JWT token
            payload = verify_token(credentials.credentials)

            # Add user info to request state
            request.state.user = payload
            request.state.user_id = payload.get("sub")
            request.state.role = payload.get("role")

            # Log access for audit trail
            logger.info(f"User {payload.get('email')} accessed {request.url.path}")

            return payload

        except Exception as e:
            logger.error(f"Authentication failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
```

### **🏥 Patient Service Implementation**
```python
# services/patient_service.py
from typing import List, Optional, Dict, Any
from uuid import UUID
from core.database import get_db
from schemas.patient import PatientCreate, PatientUpdate, PatientResponse
import logging

logger = logging.getLogger(__name__)

class PatientService:
    def __init__(self):
        self.db = get_db(use_service_role=True)

    async def create_patient(self, patient_data: PatientCreate, caregiver_id: str) -> PatientResponse:
        """Create a new patient record"""
        try:
            # Encrypt sensitive data
            encrypted_ssn = self._encrypt_ssn(patient_data.ssn) if patient_data.ssn else None

            # Prepare patient data
            patient_dict = patient_data.dict(exclude={'ssn'})
            patient_dict['ssn_encrypted'] = encrypted_ssn

            # Insert into database
            result = self.db.table("patients").insert(patient_dict).execute()

            if result.data:
                patient = result.data[0]

                # Log audit trail
                await self._log_audit(
                    caregiver_id=caregiver_id,
                    action="CREATE_PATIENT",
                    resource_id=patient['id'],
                    metadata={"patient_name": f"{patient['first_name']} {patient['last_name']}"}
                )

                return PatientResponse(**patient)

            raise Exception("Failed to create patient")

        except Exception as e:
            logger.error(f"Error creating patient: {str(e)}")
            raise

    async def get_patient(self, patient_id: UUID, caregiver_id: str) -> Optional[PatientResponse]:
        """Get patient by ID"""
        try:
            result = self.db.table("patients").select("*").eq("id", str(patient_id)).execute()

            if result.data:
                patient = result.data[0]

                # Log access for audit
                await self._log_audit(
                    caregiver_id=caregiver_id,
                    action="VIEW_PATIENT",
                    resource_id=patient_id,
                    metadata={"patient_name": f"{patient['first_name']} {patient['last_name']}"}
                )

                # Decrypt sensitive data if needed
                if patient.get('ssn_encrypted'):
                    patient['ssn'] = self._decrypt_ssn(patient['ssn_encrypted'])

                return PatientResponse(**patient)

            return None

        except Exception as e:
            logger.error(f"Error fetching patient {patient_id}: {str(e)}")
            raise

    async def update_patient(self, patient_id: UUID, patient_data: PatientUpdate, caregiver_id: str) -> PatientResponse:
        """Update patient record"""
        try:
            # Get current patient data for audit
            current_patient = await self.get_patient(patient_id, caregiver_id)
            if not current_patient:
                raise Exception("Patient not found")

            # Prepare update data
            update_dict = patient_data.dict(exclude_unset=True)

            # Handle SSN encryption if provided
            if 'ssn' in update_dict:
                update_dict['ssn_encrypted'] = self._encrypt_ssn(update_dict.pop('ssn'))

            # Update in database
            result = self.db.table("patients").update(update_dict).eq("id", str(patient_id)).execute()

            if result.data:
                updated_patient = result.data[0]

                # Log audit trail with changes
                changes = self._get_changes(current_patient.dict(), update_dict)
                await self._log_audit(
                    caregiver_id=caregiver_id,
                    action="UPDATE_PATIENT",
                    resource_id=patient_id,
                    metadata={"changes": changes}
                )

                return PatientResponse(**updated_patient)

            raise Exception("Failed to update patient")

        except Exception as e:
            logger.error(f"Error updating patient {patient_id}: {str(e)}")
            raise

    def _encrypt_ssn(self, ssn: str) -> str:
        """Encrypt SSN for HIPAA compliance"""
        # Implementation would use proper encryption
        # This is a placeholder
        return f"encrypted_{ssn}"

    def _decrypt_ssn(self, encrypted_ssn: str) -> str:
        """Decrypt SSN"""
        # Implementation would use proper decryption
        # This is a placeholder
        return encrypted_ssn.replace("encrypted_", "")

    async def _log_audit(self, caregiver_id: str, action: str, resource_id: UUID, metadata: Dict[str, Any]):
        """Log audit trail for HIPAA compliance"""
        audit_data = {
            "caregiver_id": caregiver_id,
            "action": action,
            "resource_type": "patient",
            "resource_id": str(resource_id),
            "patient_id": str(resource_id),
            "metadata": metadata
        }

        self.db.table("audit_logs").insert(audit_data).execute()

    def _get_changes(self, old_data: dict, new_data: dict) -> dict:
        """Get changes between old and new data"""
        changes = {}
        for key, new_value in new_data.items():
            old_value = old_data.get(key)
            if old_value != new_value:
                changes[key] = {"old": old_value, "new": new_value}
        return changes
```

### **🤖 AI Service Integration**
```python
# services/ai_service.py
from typing import Dict, List, Any, Optional
import httpx
import logging
from core.config import settings

logger = logging.getLogger(__name__)

class AIService:
    def __init__(self):
        self.huggingface_api_key = settings.HUGGINGFACE_API_KEY
        self.openai_api_key = settings.OPENAI_API_KEY

    async def predict_fall_risk(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict fall risk for a patient"""
        try:
            # Prepare features for ML model
            features = self._prepare_fall_risk_features(patient_data)

            # Call AI model (example with Hugging Face)
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api-inference.huggingface.co/models/your-fall-risk-model",
                    headers={"Authorization": f"Bearer {self.huggingface_api_key}"},
                    json={"inputs": features}
                )

                if response.status_code == 200:
                    result = response.json()

                    # Process and return prediction
                    return {
                        "risk_score": result.get("score", 0),
                        "risk_level": self._categorize_risk(result.get("score", 0)),
                        "recommendations": self._get_fall_prevention_recommendations(result),
                        "confidence": result.get("confidence", 0),
                        "model_version": settings.FALL_RISK_MODEL_VERSION
                    }

                raise Exception(f"AI API error: {response.status_code}")

        except Exception as e:
            logger.error(f"Fall risk prediction failed: {str(e)}")
            # Return default safe prediction
            return {
                "risk_score": 0.5,
                "risk_level": "moderate",
                "recommendations": ["Regular assessment needed"],
                "confidence": 0.0,
                "error": str(e)
            }

    async def analyze_medication_interactions(self, medications: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze potential medication interactions"""
        try:
            # Prepare medication data
            med_names = [med.get("name", "") for med in medications]

            # Call drug interaction API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.fda.gov/drug/interaction.json",
                    json={"medications": med_names}
                )

                if response.status_code == 200:
                    result = response.json()

                    return {
                        "interactions": result.get("interactions", []),
                        "severity_levels": result.get("severity", {}),
                        "recommendations": self._get_medication_recommendations(result),
                        "review_required": len(result.get("interactions", [])) > 0
                    }

        except Exception as e:
            logger.error(f"Medication interaction analysis failed: {str(e)}")
            return {
                "interactions": [],
                "severity_levels": {},
                "recommendations": ["Manual review recommended"],
                "review_required": True,
                "error": str(e)
            }

    def _prepare_fall_risk_features(self, patient_data: Dict[str, Any]) -> List[float]:
        """Prepare features for fall risk model"""
        # Extract relevant features
        age = self._calculate_age(patient_data.get("date_of_birth"))
        medications_count = len(patient_data.get("medications", []))
        has_mobility_issues = "mobility" in str(patient_data.get("medical_history", {})).lower()

        return [age, medications_count, int(has_mobility_issues)]

    def _categorize_risk(self, score: float) -> str:
        """Categorize risk score"""
        if score < 0.3:
            return "low"
        elif score < 0.7:
            return "moderate"
        else:
            return "high"

    def _get_fall_prevention_recommendations(self, prediction_result: Dict[str, Any]) -> List[str]:
        """Get fall prevention recommendations based on prediction"""
        recommendations = []
        score = prediction_result.get("score", 0)

        if score > 0.7:
            recommendations.extend([
                "Immediate fall risk assessment required",
                "Consider bed alarm or monitoring system",
                "Review medications for sedating effects",
                "Physical therapy consultation recommended"
            ])
        elif score > 0.3:
            recommendations.extend([
                "Regular fall risk monitoring",
                "Ensure clear pathways and adequate lighting",
                "Review mobility aids and assistive devices"
            ])
        else:
            recommendations.append("Continue routine fall prevention measures")

        return recommendations
```

### **📊 API Response Models**
```python
# schemas/responses.py
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID

class APIResponse(BaseModel):
    """Standard API response format"""
    success: bool = True
    message: str = "Operation completed successfully"
    data: Optional[Any] = None
    errors: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class PaginatedResponse(BaseModel):
    """Paginated response format"""
    items: List[Any]
    total: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool

class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str
    database: str = "connected"
    services: Dict[str, str] = {}

class ErrorResponse(BaseModel):
    """Error response format"""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
```

## 🔄 **Development Workflow**

### **🧪 Testing Setup**
```bash
# Install testing dependencies
pip install pytest pytest-asyncio pytest-cov httpx

# Run tests
pytest tests/ -v --cov=.

# Run specific test file
pytest tests/test_auth.py -v
```

### **📝 Code Quality**
```bash
# Format code
black . --line-length 88

# Lint code
flake8 . --max-line-length 88

# Type checking
mypy . --ignore-missing-imports
```

### **🔄 Database Migrations**
```bash
# Install Supabase CLI
npm install -g @supabase/cli

# Initialize Supabase
supabase init

# Create migration
supabase migration new add_new_table

# Apply migrations
supabase db push
```

## 📈 **Performance Optimization**

### **🚀 Caching Strategy**
```python
# utils/cache.py
import redis
import json
from typing import Any, Optional
from core.config import settings

redis_client = redis.from_url(settings.REDIS_URL) if settings.REDIS_URL else None

async def cache_get(key: str) -> Optional[Any]:
    """Get value from cache"""
    if not redis_client:
        return None

    try:
        value = redis_client.get(key)
        return json.loads(value) if value else None
    except Exception:
        return None

async def cache_set(key: str, value: Any, ttl: int = settings.CACHE_TTL):
    """Set value in cache"""
    if not redis_client:
        return

    try:
        redis_client.setex(key, ttl, json.dumps(value))
    except Exception:
        pass
```

### **📊 Database Optimization**
```sql
-- Add database indexes for better performance
CREATE INDEX CONCURRENTLY idx_patients_search
ON patients USING gin(to_tsvector('english', first_name || ' ' || last_name));

CREATE INDEX CONCURRENTLY idx_medical_records_date_range
ON medical_records(patient_id, created_at DESC);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM patients WHERE first_name ILIKE '%john%';
```

**Your Care-SolAI backend is now fully documented and ready for production deployment!** 🚀
