[{"C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Billing.tsx": "4", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Dashboard.tsx": "5", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Patients.tsx": "6", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\MedicalRecords.tsx": "7", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Login.tsx": "8", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Inventory.tsx": "9", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\PatientDetail.tsx": "10", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Profile.tsx": "11", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\contexts\\AuthContext.tsx": "12", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\contexts\\SupabaseContext.tsx": "13", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\components\\Layout\\AuthLayout.tsx": "14", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\components\\Layout\\Layout.tsx": "15", "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\services\\authService.ts": "16"}, {"size": 554, "mtime": 1751340198561, "results": "17", "hashOfConfig": "18"}, {"size": 425, "mtime": 1751340196884, "results": "19", "hashOfConfig": "18"}, {"size": 6023, "mtime": 1751344671686, "results": "20", "hashOfConfig": "18"}, {"size": 1207, "mtime": 1751345042702, "results": "21", "hashOfConfig": "18"}, {"size": 7315, "mtime": 1751344976396, "results": "22", "hashOfConfig": "18"}, {"size": 2769, "mtime": 1751344998143, "results": "23", "hashOfConfig": "18"}, {"size": 1205, "mtime": 1751345024325, "results": "24", "hashOfConfig": "18"}, {"size": 5345, "mtime": 1751344944524, "results": "25", "hashOfConfig": "18"}, {"size": 1180, "mtime": 1751345033100, "results": "26", "hashOfConfig": "18"}, {"size": 819, "mtime": 1751345013346, "results": "27", "hashOfConfig": "18"}, {"size": 2246, "mtime": 1751345056494, "results": "28", "hashOfConfig": "18"}, {"size": 4282, "mtime": 1751344751176, "results": "29", "hashOfConfig": "18"}, {"size": 1212, "mtime": 1751344732539, "results": "30", "hashOfConfig": "18"}, {"size": 1224, "mtime": 1751344922340, "results": "31", "hashOfConfig": "18"}, {"size": 6648, "mtime": 1751344912158, "results": "32", "hashOfConfig": "18"}, {"size": 4604, "mtime": 1751344772032, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1pjvy19", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Billing.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Patients.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\MedicalRecords.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Inventory.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\PatientDetail.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\pages\\Profile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\contexts\\AuthContext.tsx", ["82"], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\contexts\\SupabaseContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\components\\Layout\\AuthLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\components\\Layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\src\\services\\authService.ts", [], [], {"ruleId": "83", "severity": 1, "message": "84", "line": 66, "column": 6, "nodeType": "85", "endLine": 66, "endColumn": 21, "suggestions": "86"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUserProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["87"], {"desc": "88", "fix": "89"}, "Update the dependencies array to be: [loadUserProfile, supabase.auth]", {"range": "90", "text": "91"}, [1868, 1883], "[loadUserProfile, supabase.auth]"]