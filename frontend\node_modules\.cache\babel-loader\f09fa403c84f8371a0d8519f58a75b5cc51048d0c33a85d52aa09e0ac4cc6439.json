{"ast": null, "code": "\"use client\";\n\n// src/QueryErrorResetBoundary.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createValue() {\n  let isReset = false;\n  return {\n    clearReset: () => {\n      isReset = false;\n    },\n    reset: () => {\n      isReset = true;\n    },\n    isReset: () => {\n      return isReset;\n    }\n  };\n}\nvar QueryErrorResetBoundaryContext = React.createContext(createValue());\nvar useQueryErrorResetBoundary = () => React.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({\n  children\n}) => {\n  const [value] = React.useState(() => createValue());\n  return /* @__PURE__ */jsx(QueryErrorResetBoundaryContext.Provider, {\n    value,\n    children: typeof children === \"function\" ? children(value) : children\n  });\n};\nexport { QueryErrorResetBoundary, useQueryErrorResetBoundary };", "map": {"version": 3, "names": ["React", "jsx", "createValue", "isReset", "clear<PERSON><PERSON>t", "reset", "QueryErrorResetBoundaryContext", "createContext", "useQueryErrorResetBoundary", "useContext", "QueryErrorResetBoundary", "children", "value", "useState", "Provider"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@tanstack\\react-query\\src\\QueryErrorResetBoundary.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\n// CONTEXT\nexport type QueryErrorResetFunction = () => void\nexport type QueryErrorIsResetFunction = () => boolean\nexport type QueryErrorClearResetFunction = () => void\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: QueryErrorClearResetFunction\n  isReset: QueryErrorIsResetFunction\n  reset: QueryErrorResetFunction\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport type QueryErrorResetBoundaryFunction = (\n  value: QueryErrorResetBoundaryValue,\n) => React.ReactNode\n\nexport interface QueryErrorResetBoundaryProps {\n  children: QueryErrorResetBoundaryFunction | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function' ? children(value) : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n"], "mappings": ";;;AACA,YAAYA,KAAA,MAAW;AAkDnB,SAAAC,GAAA;AArCJ,SAASC,YAAA,EAA4C;EACnD,IAAIC,OAAA,GAAU;EACd,OAAO;IACLC,UAAA,EAAYA,CAAA,KAAM;MAChBD,OAAA,GAAU;IACZ;IACAE,KAAA,EAAOA,CAAA,KAAM;MACXF,OAAA,GAAU;IACZ;IACAA,OAAA,EAASA,CAAA,KAAM;MACb,OAAOA,OAAA;IACT;EACF;AACF;AAEA,IAAMG,8BAAA,GAAuCN,KAAA,CAAAO,aAAA,CAAcL,WAAA,CAAY,CAAC;AAIjE,IAAMM,0BAAA,GAA6BA,CAAA,KAClCR,KAAA,CAAAS,UAAA,CAAWH,8BAA8B;AAY1C,IAAMI,uBAAA,GAA0BA,CAAC;EACtCC;AACF,MAAoC;EAClC,MAAM,CAACC,KAAK,IAAUZ,KAAA,CAAAa,QAAA,CAAS,MAAMX,WAAA,CAAY,CAAC;EAClD,OACE,eAAAD,GAAA,CAACK,8BAAA,CAA+BQ,QAAA,EAA/B;IAAwCF,KAAA;IACtCD,QAAA,SAAOA,QAAA,KAAa,aAAaA,QAAA,CAASC,KAAK,IAAID;EAAA,CACtD;AAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}