import React, { useRef, useEffect, useState } from 'react';
import { TrashIcon, CheckIcon } from '@heroicons/react/24/outline';

interface SignatureCanvasProps {
  onSignatureChange: (signature: string | null) => void;
  width?: number;
  height?: number;
  penColor?: string;
  penWidth?: number;
}

const SignatureCanvas: React.FC<SignatureCanvasProps> = ({
  onSignatureChange,
  width = 400,
  height = 200,
  penColor = '#000000',
  penWidth = 2
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const context = canvas.getContext('2d');
    if (!context) return;

    // Set up canvas
    context.lineCap = 'round';
    context.lineJoin = 'round';
    context.strokeStyle = penColor;
    context.lineWidth = penWidth;

    // Clear canvas
    context.clearRect(0, 0, width, height);
    
    // Add signature line
    context.beginPath();
    context.strokeStyle = '#e5e7eb';
    context.lineWidth = 1;
    context.moveTo(20, height - 20);
    context.lineTo(width - 20, height - 20);
    context.stroke();
    
    // Add "Sign here" text
    context.fillStyle = '#9ca3af';
    context.font = '14px Arial';
    context.textAlign = 'center';
    context.fillText('Sign here', width / 2, height - 30);
    
    // Reset drawing style
    context.strokeStyle = penColor;
    context.lineWidth = penWidth;
  }, [width, height, penColor, penWidth]);

  const startDrawing = (event: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = 'touches' in event 
      ? event.touches[0].clientX - rect.left
      : event.clientX - rect.left;
    const y = 'touches' in event 
      ? event.touches[0].clientY - rect.top
      : event.clientY - rect.top;

    const context = canvas.getContext('2d');
    if (!context) return;

    setIsDrawing(true);
    context.beginPath();
    context.moveTo(x, y);
  };

  const draw = (event: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = 'touches' in event 
      ? event.touches[0].clientX - rect.left
      : event.clientX - rect.left;
    const y = 'touches' in event 
      ? event.touches[0].clientY - rect.top
      : event.clientY - rect.top;

    const context = canvas.getContext('2d');
    if (!context) return;

    context.lineTo(x, y);
    context.stroke();
    
    setHasSignature(true);
    
    // Convert to base64 and notify parent
    const signatureData = canvas.toDataURL();
    onSignatureChange(signatureData);
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const context = canvas.getContext('2d');
    if (!context) return;

    // Clear canvas
    context.clearRect(0, 0, width, height);
    
    // Redraw signature line and text
    context.beginPath();
    context.strokeStyle = '#e5e7eb';
    context.lineWidth = 1;
    context.moveTo(20, height - 20);
    context.lineTo(width - 20, height - 20);
    context.stroke();
    
    context.fillStyle = '#9ca3af';
    context.font = '14px Arial';
    context.textAlign = 'center';
    context.fillText('Sign here', width / 2, height - 30);
    
    // Reset drawing style
    context.strokeStyle = penColor;
    context.lineWidth = penWidth;
    
    setHasSignature(false);
    onSignatureChange(null);
  };

  return (
    <div className="signature-canvas-container">
      <div className="border-2 border-gray-300 rounded-lg p-4 bg-white">
        <canvas
          ref={canvasRef}
          width={width}
          height={height}
          className="border border-gray-200 rounded cursor-crosshair touch-none"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          onTouchStart={startDrawing}
          onTouchMove={draw}
          onTouchEnd={stopDrawing}
          style={{ touchAction: 'none' }}
        />
        
        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-gray-600">
            {hasSignature ? (
              <span className="flex items-center text-green-600">
                <CheckIcon className="h-4 w-4 mr-1" />
                Signature captured
              </span>
            ) : (
              'Please sign above'
            )}
          </div>
          
          <button
            onClick={clearSignature}
            className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <TrashIcon className="h-4 w-4 mr-1" />
            Clear
          </button>
        </div>
      </div>
      
      <div className="mt-2 text-xs text-gray-500">
        <p>• Use your mouse, finger, or stylus to sign</p>
        <p>• Your signature will be securely encrypted and stored</p>
        <p>• This signature has the same legal validity as a handwritten signature</p>
      </div>
    </div>
  );
};

export default SignatureCanvas;
