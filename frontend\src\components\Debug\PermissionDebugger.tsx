import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole, ROLE_PERMISSIONS, canAccessModule, hasPermission } from '../../utils/roleBasedAccess';
import {
  BugAntIcon,
  UserIcon,
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

const PermissionDebugger: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { user, loading } = useAuth();

  // Only show in development
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (!isDevelopment) {
    return null;
  }

  const userRole = (user?.role as UserRole) || 'caregiver';
  const permissions = ROLE_PERMISSIONS[userRole];

  const modules = [
    'dashboard',
    'residents',
    'medicalRecords',
    'medications',
    'scheduling',
    'inventory',
    'purchasing',
    'billing',
    'reports',
    'userManagement',
    'systemSettings',
    'auditLogs',
    'hipaaCompliance',
    'faxManagement',
    'documentManagement',
  ] as const;

  return (
    <div className="fixed bottom-6 left-6 z-50">
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="group relative p-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-full shadow-2xl hover:from-green-500 hover:to-emerald-500 transform hover:scale-110 transition-all duration-300 border-4 border-white"
      >
        <BugAntIcon className="h-6 w-6 group-hover:rotate-180 transition-transform duration-500" />
        <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-gradient-to-r from-blue-500 to-purple-500 rounded-full">
          DEBUG
        </span>
      </button>

      {/* Debug Panel */}
      {isOpen && (
        <div className="absolute bottom-16 left-0 w-96 max-h-96 bg-white rounded-2xl shadow-2xl border-4 border-green-200 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-green-600 to-emerald-600 p-4 text-white">
            <h3 className="text-lg font-bold">Permission Debugger</h3>
            <p className="text-sm opacity-90">Current User Access Analysis</p>
          </div>

          {/* Content */}
          <div className="p-4 space-y-4 max-h-80 overflow-y-auto">
            {/* User Info */}
            <div className="bg-blue-50 rounded-xl p-4 border-2 border-blue-200">
              <div className="flex items-center mb-2">
                <UserIcon className="h-5 w-5 text-blue-600 mr-2" />
                <span className="font-bold text-blue-900">User Information</span>
              </div>
              <div className="text-sm space-y-1">
                <div><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</div>
                <div><strong>User:</strong> {user ? `${user.first_name} ${user.last_name}` : 'Not logged in'}</div>
                <div><strong>Role:</strong> {userRole}</div>
                <div><strong>Email:</strong> {user?.email || 'N/A'}</div>
              </div>
            </div>

            {/* Module Permissions */}
            <div className="bg-gray-50 rounded-xl p-4 border-2 border-gray-200">
              <div className="flex items-center mb-3">
                <InformationCircleIcon className="h-5 w-5 text-gray-600 mr-2" />
                <span className="font-bold text-gray-900">Module Access</span>
              </div>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {modules.map((module) => {
                  const hasAccess = canAccessModule(userRole, module);
                  const modulePerms = permissions[module];
                  
                  return (
                    <div key={module} className="flex items-center justify-between text-sm">
                      <span className="font-medium capitalize">{module}</span>
                      <div className="flex items-center space-x-2">
                        {hasAccess ? (
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircleIcon className="h-4 w-4 text-red-500" />
                        )}
                        <span className="text-xs text-gray-500">
                          R:{modulePerms.read ? '✓' : '✗'} 
                          W:{modulePerms.write ? '✓' : '✗'} 
                          D:{modulePerms.delete ? '✓' : '✗'}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Permission Tests */}
            <div className="bg-yellow-50 rounded-xl p-4 border-2 border-yellow-200">
              <div className="flex items-center mb-3">
                <BugAntIcon className="h-5 w-5 text-yellow-600 mr-2" />
                <span className="font-bold text-yellow-900">Quick Tests</span>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Dashboard Access:</span>
                  <span className={canAccessModule(userRole, 'dashboard') ? 'text-green-600' : 'text-red-600'}>
                    {canAccessModule(userRole, 'dashboard') ? 'PASS' : 'FAIL'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Medications Read:</span>
                  <span className={hasPermission(userRole, 'medications', 'read') ? 'text-green-600' : 'text-red-600'}>
                    {hasPermission(userRole, 'medications', 'read') ? 'PASS' : 'FAIL'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Purchasing Access:</span>
                  <span className={canAccessModule(userRole, 'purchasing') ? 'text-green-600' : 'text-red-600'}>
                    {canAccessModule(userRole, 'purchasing') ? 'PASS' : 'FAIL'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Billing Access:</span>
                  <span className={canAccessModule(userRole, 'billing') ? 'text-green-600' : 'text-red-600'}>
                    {canAccessModule(userRole, 'billing') ? 'PASS' : 'FAIL'}
                  </span>
                </div>
              </div>
            </div>

            {/* Current Route Info */}
            <div className="bg-purple-50 rounded-xl p-4 border-2 border-purple-200">
              <div className="flex items-center mb-2">
                <InformationCircleIcon className="h-5 w-5 text-purple-600 mr-2" />
                <span className="font-bold text-purple-900">Current Route</span>
              </div>
              <div className="text-sm">
                <div><strong>Path:</strong> {window.location.pathname}</div>
                <div><strong>Search:</strong> {window.location.search || 'None'}</div>
                <div><strong>Hash:</strong> {window.location.hash || 'None'}</div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 p-3 border-t border-gray-200">
            <div className="text-xs text-gray-600 text-center">
              Development Debug Tool - Hidden in Production
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 -z-10"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default PermissionDebugger;
