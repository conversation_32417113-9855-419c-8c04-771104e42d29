import React, { useState, useEffect } from 'react';
import {
  PhoneIcon,
  InboxIcon,
  PaperAirplaneIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  EyeIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';
import { faxService, FaxHistory, InboundFax } from '../services/faxService';

const FaxManagement: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'outbound' | 'inbound' | 'history'>('outbound');
  const [faxHistory, setFaxHistory] = useState<FaxHistory[]>([]);
  const [inboundFaxes, setInboundFaxes] = useState<InboundFax[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadFaxData();
  }, [selectedTab]);

  const loadFaxData = async () => {
    setLoading(true);
    try {
      if (selectedTab === 'history') {
        const history = await faxService.getFaxHistory();
        setFaxHistory(history);
      } else if (selectedTab === 'inbound') {
        const inbound = await faxService.getInboundFaxes();
        setInboundFaxes(inbound);
      }
    } catch (error) {
      console.error('Failed to load fax data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
      case 'received':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'sending':
      case 'queued':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
      case 'received':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'sending':
      case 'queued':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleRetryFax = async (faxId: string) => {
    try {
      await faxService.retryFax(faxId);
      alert('Fax retry initiated successfully');
      loadFaxData();
    } catch (error) {
      alert('Failed to retry fax');
    }
  };

  const handleProcessInboundFax = async (faxId: string) => {
    try {
      const result = await faxService.processInboundFax(faxId);
      alert(`Fax processed successfully! Document type: ${result.aiAnalysis.documentType}`);
      loadFaxData();
    } catch (error) {
      alert('Failed to process fax');
    }
  };

  const tabs = [
    { id: 'outbound', name: 'Outbound Faxes', icon: PaperAirplaneIcon },
    { id: 'inbound', name: 'Inbound Faxes', icon: InboxIcon },
    { id: 'history', name: 'Fax History', icon: ClockIcon },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Fax Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          Secure fax communication for residential healthcare documentation
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            📠 HIPAA Compliant Faxing
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            🤖 AI Document Processing
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            📋 Automated Workflows
          </span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <PaperAirplaneIcon className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Sent Today</p>
              <p className="text-2xl font-semibold text-gray-900">12</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <InboxIcon className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Received Today</p>
              <p className="text-2xl font-semibold text-gray-900">8</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-2xl font-semibold text-gray-900">3</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <XCircleIcon className="h-8 w-8 text-red-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Failed</p>
              <p className="text-2xl font-semibold text-gray-900">1</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : (
            <>
              {selectedTab === 'inbound' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Inbound Faxes</h3>
                  {inboundFaxes.map((fax) => (
                    <div key={fax.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <InboxIcon className="h-5 w-5 text-green-500" />
                            <div>
                              <h4 className="font-medium text-gray-900">
                                From: {fax.fromName || fax.fromNumber}
                              </h4>
                              <p className="text-sm text-gray-600">
                                Received: {new Date(fax.receivedAt).toLocaleString()}
                              </p>
                              <p className="text-sm text-gray-600">Pages: {fax.pages}</p>
                            </div>
                          </div>
                          
                          {fax.aiAnalysis && (
                            <div className="mt-3 p-3 bg-purple-50 border border-purple-200 rounded-md">
                              <div className="flex items-start">
                                <DocumentTextIcon className="h-5 w-5 text-purple-600 mt-0.5" />
                                <div className="ml-3">
                                  <h5 className="text-sm font-medium text-purple-800">AI Analysis</h5>
                                  <p className="text-sm text-purple-700">
                                    Document Type: {fax.aiAnalysis.documentType} 
                                    (Confidence: {Math.round(fax.aiAnalysis.confidence * 100)}%)
                                  </p>
                                  {fax.aiAnalysis.requiresAttention && (
                                    <p className="text-sm text-red-600 mt-1">⚠️ Requires immediate attention</p>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                        
                        <div className="ml-4 flex flex-col space-y-2">
                          <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                            <EyeIcon className="h-4 w-4 inline mr-1" />
                            View
                          </button>
                          {!fax.processed && (
                            <button
                              onClick={() => handleProcessInboundFax(fax.id)}
                              className="px-3 py-1 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700"
                            >
                              Process with AI
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {selectedTab === 'history' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Fax History</h3>
                  {faxHistory.map((fax) => (
                    <div key={fax.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(fax.status)}
                            <div>
                              <h4 className="font-medium text-gray-900">{fax.subject}</h4>
                              <p className="text-sm text-gray-600">
                                {fax.direction === 'outbound' ? 'To:' : 'From:'} {fax.direction === 'outbound' ? fax.toNumber : fax.fromNumber}
                              </p>
                              <p className="text-sm text-gray-600">
                                {new Date(fax.timestamp).toLocaleString()} • {fax.pages} pages
                              </p>
                              {fax.residentName && (
                                <p className="text-sm text-gray-600">Resident: {fax.residentName}</p>
                              )}
                            </div>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(fax.status)}`}>
                              {fax.status.toUpperCase()}
                            </span>
                          </div>
                        </div>
                        
                        <div className="ml-4 flex flex-col space-y-2">
                          <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                            <EyeIcon className="h-4 w-4 inline mr-1" />
                            View
                          </button>
                          {fax.status === 'failed' && fax.direction === 'outbound' && (
                            <button
                              onClick={() => handleRetryFax(fax.id)}
                              className="px-3 py-1 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700"
                            >
                              <ArrowPathIcon className="h-4 w-4 inline mr-1" />
                              Retry
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {selectedTab === 'outbound' && (
                <div className="text-center py-8">
                  <PhoneIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No outbound faxes</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Send faxes directly from the Documents section
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default FaxManagement;
