import React, { useState, useEffect } from 'react';
import {
  PhoneIcon,
  InboxIcon,
  PaperAirplaneIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  EyeIcon,
  DocumentTextIcon,
  BeakerIcon,
  ArrowDownTrayIcon,
  PrinterIcon,
  CogIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import { faxService, FaxHistory, InboundFax } from '../services/faxService';

const FaxManagement: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'outbound' | 'inbound' | 'history'>('outbound');
  const [faxHistory, setFaxHistory] = useState<FaxHistory[]>([]);
  const [inboundFaxes, setInboundFaxes] = useState<InboundFax[]>([]);
  const [outboundFaxes, setOutboundFaxes] = useState<FaxHistory[]>([]);
  const [loading, setLoading] = useState(false);

  // View modal states
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedFax, setSelectedFax] = useState<InboundFax | FaxHistory | null>(null);

  useEffect(() => {
    loadFaxData();
  }, [selectedTab]);

  const loadFaxData = async () => {
    setLoading(true);
    try {
      if (selectedTab === 'history') {
        const history = await faxService.getFaxHistory();
        setFaxHistory(history);
      } else if (selectedTab === 'inbound') {
        const inbound = await faxService.getInboundFaxes();
        setInboundFaxes(inbound);
      } else if (selectedTab === 'outbound') {
        const outbound = await faxService.getFaxHistory({ direction: 'outbound' });
        setOutboundFaxes(outbound);
      }
    } catch (error) {
      console.error('Failed to load fax data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
      case 'received':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'sending':
      case 'queued':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
      case 'received':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'sending':
      case 'queued':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleRetryFax = async (faxId: string) => {
    try {
      await faxService.retryFax(faxId);
      alert('Fax retry initiated successfully');
      loadFaxData();
    } catch (error) {
      alert('Failed to retry fax');
    }
  };

  const handleProcessInboundFax = async (faxId: string) => {
    try {
      const result = await faxService.processInboundFax(faxId);
      alert(`Fax processed successfully! Document type: ${result.aiAnalysis.documentType}`);
      loadFaxData();
    } catch (error) {
      alert('Failed to process fax');
    }
  };

  const handleViewFax = (fax: InboundFax | FaxHistory) => {
    setSelectedFax(fax);
    setShowViewModal(true);
  };

  const tabs = [
    { id: 'outbound', name: 'Outbound Faxes', icon: PaperAirplaneIcon },
    { id: 'inbound', name: 'Inbound Faxes', icon: InboxIcon },
    { id: 'history', name: 'Fax History', icon: ClockIcon },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Fax Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          Secure fax communication for residential healthcare documentation
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            📠 HIPAA Compliant Faxing
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            🤖 AI Document Processing
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            📋 Automated Workflows
          </span>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <PaperAirplaneIcon className="h-8 w-8 text-blue-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Sent Today</p>
              <p className="text-2xl font-semibold text-gray-900">12</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <InboxIcon className="h-8 w-8 text-green-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Received Today</p>
              <p className="text-2xl font-semibold text-gray-900">8</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-yellow-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Pending</p>
              <p className="text-2xl font-semibold text-gray-900">3</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center">
            <XCircleIcon className="h-8 w-8 text-red-500" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Failed</p>
              <p className="text-2xl font-semibold text-gray-900">1</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <div className="flex items-center justify-between px-6">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setSelectedTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    selectedTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
            {selectedTab === 'outbound' && (
              <button
                onClick={() => alert('Send New Fax functionality - Integrate with Documents section')}
                className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors duration-200"
              >
                <PlusIcon className="h-4 w-4 inline mr-2" />
                Send New Fax
              </button>
            )}
          </div>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              {selectedTab === 'inbound' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Inbound Faxes</h3>
                  {inboundFaxes.map((fax) => (
                    <div key={fax.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <InboxIcon className="h-5 w-5 text-green-500" />
                            <div>
                              <h4 className="font-medium text-gray-900">
                                From: {fax.fromName || fax.fromNumber}
                              </h4>
                              <p className="text-sm text-gray-600">
                                Received: {new Date(fax.receivedAt).toLocaleString()}
                              </p>
                              <p className="text-sm text-gray-600">Pages: {fax.pages}</p>
                            </div>
                          </div>
                          
                          {fax.aiAnalysis && (
                            <div className="mt-3 p-3 bg-purple-50 border border-purple-200 rounded-md">
                              <div className="flex items-start">
                                <DocumentTextIcon className="h-5 w-5 text-purple-600 mt-0.5" />
                                <div className="ml-3">
                                  <h5 className="text-sm font-medium text-purple-800">AI Analysis</h5>
                                  <p className="text-sm text-purple-700">
                                    Document Type: {fax.aiAnalysis.documentType} 
                                    (Confidence: {Math.round(fax.aiAnalysis.confidence * 100)}%)
                                  </p>
                                  {fax.aiAnalysis.requiresAttention && (
                                    <p className="text-sm text-red-600 mt-1">⚠️ Requires immediate attention</p>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                        
                        <div className="ml-4 flex flex-col space-y-2">
                          <button
                            onClick={() => handleViewFax(fax)}
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                          >
                            <EyeIcon className="h-4 w-4 inline mr-1" />
                            View
                          </button>
                          {!fax.processed && (
                            <button
                              onClick={() => handleProcessInboundFax(fax.id)}
                              className="px-3 py-1 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700"
                            >
                              Process with AI
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {selectedTab === 'history' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Fax History</h3>
                  {faxHistory.map((fax) => (
                    <div key={fax.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(fax.status)}
                            <div>
                              <h4 className="font-medium text-gray-900">{fax.subject}</h4>
                              <p className="text-sm text-gray-600">
                                {fax.direction === 'outbound' ? 'To:' : 'From:'} {fax.direction === 'outbound' ? fax.toNumber : fax.fromNumber}
                              </p>
                              <p className="text-sm text-gray-600">
                                {new Date(fax.timestamp).toLocaleString()} • {fax.pages} pages
                              </p>
                              {fax.residentName && (
                                <p className="text-sm text-gray-600">Resident: {fax.residentName}</p>
                              )}
                            </div>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(fax.status)}`}>
                              {fax.status.toUpperCase()}
                            </span>
                          </div>
                        </div>
                        
                        <div className="ml-4 flex flex-col space-y-2">
                          <button
                            onClick={() => handleViewFax(fax)}
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                          >
                            <EyeIcon className="h-4 w-4 inline mr-1" />
                            View
                          </button>
                          {fax.status === 'failed' && fax.direction === 'outbound' && (
                            <button
                              onClick={() => handleRetryFax(fax.id)}
                              className="px-3 py-1 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700"
                            >
                              <ArrowPathIcon className="h-4 w-4 inline mr-1" />
                              Retry
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {selectedTab === 'outbound' && (
                <div className="space-y-4">
                  {outboundFaxes.length === 0 ? (
                    <div className="text-center py-8">
                      <PhoneIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No outbound faxes</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Send faxes directly from the Documents section
                      </p>
                    </div>
                  ) : (
                    outboundFaxes.map((fax) => (
                      <div key={fax.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                                  <PhoneIcon className="h-5 w-5 text-blue-600" />
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 truncate">
                                  To: {fax.toNumber}
                                </p>
                                <p className="text-sm text-gray-500 truncate">
                                  {fax.subject}
                                </p>
                                <div className="flex items-center space-x-4 mt-1">
                                  <span className="text-xs text-gray-500">
                                    {new Date(fax.timestamp).toLocaleString()}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {fax.pages} page{fax.pages !== 1 ? 's' : ''}
                                  </span>
                                  {fax.residentName && (
                                    <span className="text-xs text-gray-500">
                                      Patient: {fax.residentName}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(fax.status)}`}>
                              {fax.status.toUpperCase()}
                            </span>
                            <button
                              onClick={() => handleViewFax(fax)}
                              className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                            >
                              <EyeIcon className="h-4 w-4 inline mr-1" />
                              View
                            </button>
                            {fax.status === 'failed' && (
                              <button
                                onClick={() => handleRetryFax(fax.id)}
                                className="px-3 py-1 bg-orange-600 text-white text-sm rounded-md hover:bg-orange-700"
                              >
                                <ArrowPathIcon className="h-4 w-4 inline mr-1" />
                                Retry
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* View Fax Modal */}
      {showViewModal && selectedFax && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowViewModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <EyeIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Fax Details
                      </h3>
                      <p className="text-sm text-gray-500">
                        {'direction' in selectedFax ? `${selectedFax.direction.charAt(0).toUpperCase() + selectedFax.direction.slice(1)} Fax` : 'Inbound Fax'} - {selectedFax.id}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowViewModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Fax Information */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Fax Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700">Fax ID:</p>
                        <p className="text-gray-600">{selectedFax.id}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Status:</p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor('status' in selectedFax ? selectedFax.status : (selectedFax.processed ? 'processed' : 'received'))}`}>
                          {'status' in selectedFax ? selectedFax.status.toUpperCase() : (selectedFax.processed ? 'PROCESSED' : 'RECEIVED')}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Date & Time:</p>
                        <p className="text-gray-600">{new Date('timestamp' in selectedFax ? selectedFax.timestamp : selectedFax.receivedAt).toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Pages:</p>
                        <p className="text-gray-600">{selectedFax.pages} page{selectedFax.pages !== 1 ? 's' : ''}</p>
                      </div>
                    </div>
                  </div>

                  {/* Sender/Recipient Information */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-900 mb-3">
                      {'direction' in selectedFax && selectedFax.direction === 'outbound' ? 'Recipient' : 'Sender'} Information
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-blue-700">
                          {'direction' in selectedFax && selectedFax.direction === 'outbound' ? 'To:' : 'From:'}
                        </p>
                        <p className="text-blue-600">
                          {'direction' in selectedFax && selectedFax.direction === 'outbound'
                            ? 'Unknown Recipient'
                            : ('fromName' in selectedFax ? selectedFax.fromName : 'Unknown Sender') || 'Unknown Sender'
                          }
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-blue-700">Fax Number:</p>
                        <p className="text-blue-600">
                          {'direction' in selectedFax && selectedFax.direction === 'outbound'
                            ? selectedFax.toNumber || 'Unknown'
                            : ('fromNumber' in selectedFax ? selectedFax.fromNumber : 'Unknown Number')
                          }
                        </p>
                      </div>
                      {('residentName' in selectedFax && selectedFax.residentName) && (
                        <div className="md:col-span-2">
                          <p className="text-sm font-medium text-blue-700">Related Resident:</p>
                          <p className="text-blue-600">{selectedFax.residentName}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* AI Analysis (for inbound faxes) */}
                  {'aiAnalysis' in selectedFax && selectedFax.aiAnalysis && (
                    <div className="bg-purple-50 rounded-lg p-4">
                      <h4 className="text-md font-medium text-purple-900 mb-3 flex items-center">
                        <BeakerIcon className="h-5 w-5 mr-2" />
                        AI Analysis
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-purple-700">Document Type:</p>
                          <p className="text-purple-600">{selectedFax.aiAnalysis.documentType}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-purple-700">Confidence:</p>
                          <div className="flex items-center">
                            <div className="w-20 bg-purple-200 rounded-full h-2">
                              <div
                                className="h-2 rounded-full bg-purple-600"
                                style={{ width: `${selectedFax.aiAnalysis.confidence * 100}%` }}
                              ></div>
                            </div>
                            <span className="ml-2 text-sm text-purple-600">{Math.round(selectedFax.aiAnalysis.confidence * 100)}%</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-purple-700">Requires Attention:</p>
                          <p className="text-purple-600">{selectedFax.aiAnalysis.requiresAttention ? 'Yes' : 'No'}</p>
                        </div>
                        {selectedFax.aiAnalysis.extractedData && Object.keys(selectedFax.aiAnalysis.extractedData).length > 0 && (
                          <div className="md:col-span-2">
                            <p className="text-sm font-medium text-purple-700 mb-2">Extracted Data:</p>
                            <div className="bg-white rounded p-2 text-xs">
                              {Object.entries(selectedFax.aiAnalysis.extractedData).map(([key, value]) => (
                                <div key={key} className="flex justify-between py-1">
                                  <span className="font-medium">{key}:</span>
                                  <span>{String(value)}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Document Preview */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Document Preview</h4>
                    <div className="bg-white border-2 border-dashed border-gray-300 rounded-lg h-64 flex items-center justify-center">
                      <div className="text-center">
                        <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-500">Document preview would be displayed here</p>
                        <p className="text-sm text-gray-400">PDF viewer or image display</p>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-green-900 mb-3">Available Actions</h4>
                    <div className="flex flex-wrap gap-2">
                      <button className="inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-sm font-medium rounded text-green-700 bg-white hover:bg-green-50">
                        <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                        Download
                      </button>
                      <button className="inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-sm font-medium rounded text-green-700 bg-white hover:bg-green-50">
                        <PrinterIcon className="h-4 w-4 mr-1" />
                        Print
                      </button>
                      <button className="inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-sm font-medium rounded text-green-700 bg-white hover:bg-green-50">
                        <PaperAirplaneIcon className="h-4 w-4 mr-1" />
                        Forward
                      </button>
                      {'aiAnalysis' in selectedFax && !selectedFax.processed && (
                        <button
                          onClick={() => {
                            handleProcessInboundFax(selectedFax.id);
                            setShowViewModal(false);
                          }}
                          className="inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-sm font-medium rounded text-green-700 bg-white hover:bg-green-50"
                        >
                          <CogIcon className="h-4 w-4 mr-1" />
                          Process with AI
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => setShowViewModal(false)}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FaxManagement;
