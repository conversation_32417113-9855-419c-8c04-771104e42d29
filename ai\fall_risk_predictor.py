"""
Fall Risk Prediction Model for CareSyncAI
Uses patient data to predict fall risk probability
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, roc_auc_score
import joblib
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime, date

logger = logging.getLogger(__name__)

class FallRiskPredictor:
    """Fall risk prediction model for homecare patients"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = [
            'age', 'mobility_score', 'cognitive_score', 'medication_count',
            'has_diabetes', 'has_hypertension', 'has_parkinsons', 'has_dementia',
            'has_stroke_history', 'bmi', 'recent_falls', 'vision_impairment',
            'hearing_impairment', 'lives_alone', 'home_hazards_score'
        ]
        self.model_version = "v1.2.0"
        self.confidence_threshold = 0.7
    
    def prepare_features(self, patient_data: Dict) -> np.ndarray:
        """Prepare features from patient data"""
        try:
            # Calculate age from date of birth
            if isinstance(patient_data.get('date_of_birth'), str):
                birth_date = datetime.strptime(patient_data['date_of_birth'], '%Y-%m-%d').date()
            else:
                birth_date = patient_data.get('date_of_birth', date.today())
            
            age = (date.today() - birth_date).days / 365.25
            
            # Extract medical conditions
            conditions = patient_data.get('medical_conditions', [])
            has_diabetes = 'diabetes' in [c.lower() for c in conditions]
            has_hypertension = 'hypertension' in [c.lower() for c in conditions]
            has_parkinsons = 'parkinsons' in [c.lower() for c in conditions]
            has_dementia = 'dementia' in [c.lower() for c in conditions]
            has_stroke_history = 'stroke' in [c.lower() for c in conditions]
            
            # Count medications
            medications = patient_data.get('medications', [])
            medication_count = len(medications) if isinstance(medications, list) else 0
            
            # Calculate BMI (mock calculation - would need height/weight data)
            bmi = patient_data.get('bmi', 25.0)  # Default normal BMI
            
            # Mock additional risk factors (in real implementation, these would come from assessments)
            recent_falls = patient_data.get('recent_falls', 0)
            vision_impairment = patient_data.get('vision_impairment', 0)  # 0-3 scale
            hearing_impairment = patient_data.get('hearing_impairment', 0)  # 0-3 scale
            lives_alone = patient_data.get('lives_alone', 0)  # 0 or 1
            home_hazards_score = patient_data.get('home_hazards_score', 2)  # 0-10 scale
            
            features = np.array([
                age,
                patient_data.get('mobility_score', 5),
                patient_data.get('cognitive_score', 10),
                medication_count,
                int(has_diabetes),
                int(has_hypertension),
                int(has_parkinsons),
                int(has_dementia),
                int(has_stroke_history),
                bmi,
                recent_falls,
                vision_impairment,
                hearing_impairment,
                lives_alone,
                home_hazards_score
            ]).reshape(1, -1)
            
            return features
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            raise
    
    def train_model(self, training_data: List[Dict]) -> Dict:
        """Train the fall risk prediction model"""
        try:
            # Prepare training data
            X = []
            y = []
            
            for patient in training_data:
                features = self.prepare_features(patient).flatten()
                X.append(features)
                y.append(patient.get('fall_risk_label', 0))  # 0 = low risk, 1 = high risk
            
            X = np.array(X)
            y = np.array(y)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train model
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                class_weight='balanced'
            )
            
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            y_pred = self.model.predict(X_test_scaled)
            y_pred_proba = self.model.predict_proba(X_test_scaled)[:, 1]
            
            auc_score = roc_auc_score(y_test, y_pred_proba)
            classification_rep = classification_report(y_test, y_pred, output_dict=True)
            
            # Feature importance
            feature_importance = dict(zip(
                self.feature_names,
                self.model.feature_importances_
            ))
            
            training_results = {
                'auc_score': auc_score,
                'accuracy': classification_rep['accuracy'],
                'precision': classification_rep['weighted avg']['precision'],
                'recall': classification_rep['weighted avg']['recall'],
                'f1_score': classification_rep['weighted avg']['f1-score'],
                'feature_importance': feature_importance,
                'model_version': self.model_version,
                'training_date': datetime.now().isoformat()
            }
            
            logger.info(f"Model trained successfully. AUC: {auc_score:.3f}")
            return training_results
            
        except Exception as e:
            logger.error(f"Error training model: {e}")
            raise
    
    def predict_fall_risk(self, patient_data: Dict) -> Dict:
        """Predict fall risk for a patient"""
        try:
            if self.model is None:
                # Load pre-trained model or use rule-based fallback
                return self._rule_based_prediction(patient_data)
            
            # Prepare features
            features = self.prepare_features(patient_data)
            features_scaled = self.scaler.transform(features)
            
            # Make prediction
            risk_probability = self.model.predict_proba(features_scaled)[0, 1]
            risk_class = self.model.predict(features_scaled)[0]
            
            # Get feature contributions (simplified)
            feature_values = features.flatten()
            feature_contributions = {}
            
            for i, (name, value) in enumerate(zip(self.feature_names, feature_values)):
                # Simplified contribution calculation
                importance = self.model.feature_importances_[i]
                contribution = importance * (value / np.mean(feature_values))
                feature_contributions[name] = contribution
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                patient_data, risk_probability, feature_contributions
            )
            
            return {
                'patient_id': patient_data.get('id'),
                'risk_probability': float(risk_probability),
                'risk_level': 'high' if risk_probability > 0.6 else 'medium' if risk_probability > 0.3 else 'low',
                'confidence': float(min(risk_probability, 1 - risk_probability) * 2),
                'contributing_factors': feature_contributions,
                'recommendations': recommendations,
                'model_version': self.model_version,
                'prediction_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error predicting fall risk: {e}")
            raise
    
    def _rule_based_prediction(self, patient_data: Dict) -> Dict:
        """Fallback rule-based prediction when ML model is not available"""
        try:
            # Calculate age
            if isinstance(patient_data.get('date_of_birth'), str):
                birth_date = datetime.strptime(patient_data['date_of_birth'], '%Y-%m-%d').date()
            else:
                birth_date = patient_data.get('date_of_birth', date.today())
            
            age = (date.today() - birth_date).days / 365.25
            
            # Rule-based scoring
            risk_score = 0.0
            factors = {}
            
            # Age factor (0-0.3)
            age_factor = min(0.3, max(0, (age - 65) * 0.01))
            risk_score += age_factor
            factors['age_factor'] = age_factor
            
            # Mobility factor (0-0.4)
            mobility_score = patient_data.get('mobility_score', 5)
            mobility_factor = (10 - mobility_score) * 0.04
            risk_score += mobility_factor
            factors['mobility_factor'] = mobility_factor
            
            # Cognitive factor (0-0.2)
            cognitive_score = patient_data.get('cognitive_score', 10)
            cognitive_factor = (10 - cognitive_score) * 0.02
            risk_score += cognitive_factor
            factors['cognitive_factor'] = cognitive_factor
            
            # Medication factor (0-0.1)
            medications = patient_data.get('medications', [])
            med_count = len(medications) if isinstance(medications, list) else 0
            medication_factor = min(0.1, med_count * 0.01)
            risk_score += medication_factor
            factors['medication_factor'] = medication_factor
            
            # Medical conditions factor (0-0.2)
            conditions = patient_data.get('medical_conditions', [])
            high_risk_conditions = ['dementia', 'parkinsons', 'stroke', 'diabetes']
            condition_factor = 0.0
            for condition in conditions:
                if any(hrc in condition.lower() for hrc in high_risk_conditions):
                    condition_factor = 0.2
                    break
            risk_score += condition_factor
            factors['condition_factor'] = condition_factor
            
            # Cap at 1.0
            risk_score = min(1.0, risk_score)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(patient_data, risk_score, factors)
            
            return {
                'patient_id': patient_data.get('id'),
                'risk_probability': float(risk_score),
                'risk_level': 'high' if risk_score > 0.6 else 'medium' if risk_score > 0.3 else 'low',
                'confidence': 0.8,  # Rule-based confidence
                'contributing_factors': factors,
                'recommendations': recommendations,
                'model_version': 'rule_based_v1.0',
                'prediction_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in rule-based prediction: {e}")
            raise
    
    def _generate_recommendations(
        self, 
        patient_data: Dict, 
        risk_score: float, 
        factors: Dict
    ) -> List[str]:
        """Generate personalized recommendations based on risk factors"""
        recommendations = []
        
        if risk_score > 0.6:
            recommendations.append("High fall risk detected. Implement immediate fall prevention measures.")
            recommendations.append("Consider home safety assessment and modifications.")
            recommendations.append("Increase supervision and monitoring frequency.")
        
        if patient_data.get('mobility_score', 5) < 5:
            recommendations.append("Consider physical therapy evaluation for mobility improvement.")
            recommendations.append("Implement balance and strength training exercises.")
        
        if patient_data.get('cognitive_score', 10) < 7:
            recommendations.append("Monitor for confusion and disorientation.")
            recommendations.append("Consider cognitive assessment and support strategies.")
        
        medications = patient_data.get('medications', [])
        if len(medications) > 5:
            recommendations.append("Review medication list for fall-risk medications.")
            recommendations.append("Consider medication reconciliation with physician.")
        
        conditions = patient_data.get('medical_conditions', [])
        if any('diabetes' in c.lower() for c in conditions):
            recommendations.append("Monitor blood sugar levels to prevent hypoglycemic episodes.")
        
        if any('hypertension' in c.lower() for c in conditions):
            recommendations.append("Monitor blood pressure and watch for orthostatic hypotension.")
        
        if not recommendations:
            recommendations.append("Continue current care plan and monitor for changes.")
            recommendations.append("Maintain regular exercise and mobility activities.")
        
        return recommendations
    
    def save_model(self, filepath: str) -> None:
        """Save the trained model"""
        try:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'feature_names': self.feature_names,
                'model_version': self.model_version,
                'save_date': datetime.now().isoformat()
            }
            joblib.dump(model_data, filepath)
            logger.info(f"Model saved to {filepath}")
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise
    
    def load_model(self, filepath: str) -> None:
        """Load a trained model"""
        try:
            model_data = joblib.load(filepath)
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.feature_names = model_data['feature_names']
            self.model_version = model_data['model_version']
            logger.info(f"Model loaded from {filepath}")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise

# Example usage
if __name__ == "__main__":
    # Initialize predictor
    predictor = FallRiskPredictor()
    
    # Example patient data
    patient_data = {
        'id': 'patient_123',
        'date_of_birth': '1935-03-15',
        'mobility_score': 6,
        'cognitive_score': 8,
        'medical_conditions': ['diabetes', 'hypertension', 'arthritis'],
        'medications': [
            {'name': 'Metformin', 'dosage': '500mg'},
            {'name': 'Lisinopril', 'dosage': '10mg'}
        ]
    }
    
    # Make prediction
    prediction = predictor.predict_fall_risk(patient_data)
    print(f"Fall risk prediction: {prediction}")
