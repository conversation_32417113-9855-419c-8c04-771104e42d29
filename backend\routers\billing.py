"""
Billing management API endpoints with Zoho Books integration
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query
from typing import List, Optional, Dict, Any
from datetime import date
from pydantic import BaseModel
import uuid

from services.billing_service import BillingService
from core.auth import get_current_user, check_billing_permission
from utils.pagination import PaginationParams

router = APIRouter()
billing_service = BillingService()

class BillingCreateRequest(BaseModel):
    patient_id: uuid.UUID
    caregiver_id: uuid.UUID
    billing_period_start: date
    billing_period_end: date
    services_provided: List[Dict[str, Any]]
    due_date: Optional[date] = None
    notes: Optional[str] = None

class InvoiceCreateRequest(BaseModel):
    send_email: bool = True

class PaymentRequest(BaseModel):
    payment_date: Optional[date] = None
    payment_method: Optional[str] = None

@router.get("/")
async def get_billing_records(
    pagination: PaginationParams = Depends(),
    patient_id: Optional[uuid.UUID] = Query(None),
    caregiver_id: Optional[uuid.UUID] = Query(None),
    status: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """Get billing records with filtering"""
    if not check_billing_permission(current_user.get("role")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to access billing records"
        )

    try:
        filters = {}
        if patient_id:
            filters["patient_id"] = str(patient_id)
        if caregiver_id:
            filters["caregiver_id"] = str(caregiver_id)
        if status:
            filters["status"] = status

        billing_records = await billing_service.get_billing_records(
            filters=filters,
            limit=pagination.limit,
            offset=pagination.offset
        )

        return {
            "billing_records": billing_records,
            "page": pagination.page,
            "page_size": pagination.limit
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve billing records: {str(e)}"
        )

@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_billing_record(
    billing_data: BillingCreateRequest,
    current_user: dict = Depends(get_current_user)
):
    """Create a new billing record"""
    if not check_billing_permission(current_user.get("role")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create billing records"
        )

    try:
        billing_record = await billing_service.create_billing_record(
            billing_data.model_dump(),
            current_user["caregiver_id"]
        )
        return billing_record
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create billing record: {str(e)}"
        )

@router.get("/{billing_id}")
async def get_billing_record(
    billing_id: uuid.UUID,
    current_user: dict = Depends(get_current_user)
):
    """Get billing record by ID"""
    if not check_billing_permission(current_user.get("role")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to access billing records"
        )

    try:
        billing_record = await billing_service.db_service.get_by_id(str(billing_id))
        if not billing_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Billing record not found"
            )
        return billing_record
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve billing record: {str(e)}"
        )

@router.post("/{billing_id}/create-invoice")
async def create_invoice(
    billing_id: uuid.UUID,
    invoice_data: InvoiceCreateRequest,
    current_user: dict = Depends(get_current_user)
):
    """Create and optionally send invoice via Zoho Books"""
    if not check_billing_permission(current_user.get("role")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create invoices"
        )

    try:
        result = await billing_service.create_and_send_invoice(
            str(billing_id),
            invoice_data.send_email
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create invoice: {str(e)}"
        )

@router.post("/{billing_id}/mark-paid")
async def mark_invoice_paid(
    billing_id: uuid.UUID,
    payment_data: PaymentRequest,
    current_user: dict = Depends(get_current_user)
):
    """Mark invoice as paid"""
    if not check_billing_permission(current_user.get("role")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update billing records"
        )

    try:
        success = await billing_service.mark_invoice_paid(
            str(billing_id),
            payment_data.payment_date,
            payment_data.payment_method
        )

        if success:
            return {"message": "Invoice marked as paid successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to mark invoice as paid"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark invoice as paid: {str(e)}"
        )

@router.get("/patient/{patient_id}/summary")
async def get_patient_billing_summary(
    patient_id: uuid.UUID,
    current_user: dict = Depends(get_current_user)
):
    """Get billing summary for a patient"""
    if not check_billing_permission(current_user.get("role")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to access billing summaries"
        )

    try:
        summary = await billing_service.get_patient_billing_summary(str(patient_id))
        return summary
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve billing summary: {str(e)}"
        )

@router.post("/generate-monthly/{year}/{month}")
async def generate_monthly_invoices(
    year: int,
    month: int,
    current_user: dict = Depends(get_current_user)
):
    """Generate monthly invoices for all active patients"""
    if current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can generate monthly invoices"
        )

    try:
        invoices = await billing_service.generate_monthly_invoices(month, year)
        return {
            "message": f"Generated {len(invoices)} monthly invoices",
            "invoices": invoices
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate monthly invoices: {str(e)}"
        )
