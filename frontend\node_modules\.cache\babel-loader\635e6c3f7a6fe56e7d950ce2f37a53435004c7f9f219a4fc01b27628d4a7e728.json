{"ast": null, "code": "'use strict';\n\nvar isArray = Array.isArray;\nvar keyList = Object.keys;\nvar hasProp = Object.prototype.hasOwnProperty;\nvar hasElementType = typeof Element !== 'undefined';\nfunction equal(a, b) {\n  // fast-deep-equal index.js 2.0.1\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    var arrA = isArray(a),\n      arrB = isArray(b),\n      i,\n      length,\n      key;\n    if (arrA && arrB) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n    if (arrA != arrB) return false;\n    var dateA = a instanceof Date,\n      dateB = b instanceof Date;\n    if (dateA != dateB) return false;\n    if (dateA && dateB) return a.getTime() == b.getTime();\n    var regexpA = a instanceof RegExp,\n      regexpB = b instanceof RegExp;\n    if (regexpA != regexpB) return false;\n    if (regexpA && regexpB) return a.toString() == b.toString();\n    var keys = keyList(a);\n    length = keys.length;\n    if (length !== keyList(b).length) return false;\n    for (i = length; i-- !== 0;) if (!hasProp.call(b, keys[i])) return false;\n    // end fast-deep-equal\n\n    // start react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element && b instanceof Element) return a === b;\n\n    // custom handling for React\n    for (i = length; i-- !== 0;) {\n      key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of a react element\n        continue;\n      } else {\n        // all other properties should be traversed as usual\n        if (!equal(a[key], b[key])) return false;\n      }\n    }\n    // end react-fast-compare\n\n    // fast-deep-equal index.js 2.0.1\n    return true;\n  }\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function exportedEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if (error.message && error.message.match(/stack|recursion/i) || error.number === -2146828260) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('Warning: react-fast-compare does not handle circular references.', error.name, error.message);\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["isArray", "Array", "keyList", "Object", "keys", "hasProp", "prototype", "hasOwnProperty", "hasElementType", "Element", "equal", "a", "b", "arrA", "arrB", "i", "length", "key", "dateA", "Date", "dateB", "getTime", "regexpA", "RegExp", "regexpB", "toString", "call", "$$typeof", "module", "exports", "exportedEqual", "error", "message", "match", "number", "console", "warn", "name"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/node_modules/react-fast-compare/index.js"], "sourcesContent": ["'use strict';\n\nvar isArray = Array.isArray;\nvar keyList = Object.keys;\nvar hasProp = Object.prototype.hasOwnProperty;\nvar hasElementType = typeof Element !== 'undefined';\n\nfunction equal(a, b) {\n  // fast-deep-equal index.js 2.0.1\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    var arrA = isArray(a)\n      , arrB = isArray(b)\n      , i\n      , length\n      , key;\n\n    if (arrA && arrB) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    if (arrA != arrB) return false;\n\n    var dateA = a instanceof Date\n      , dateB = b instanceof Date;\n    if (dateA != dateB) return false;\n    if (dateA && dateB) return a.getTime() == b.getTime();\n\n    var regexpA = a instanceof RegExp\n      , regexpB = b instanceof RegExp;\n    if (regexpA != regexpB) return false;\n    if (regexpA && regexpB) return a.toString() == b.toString();\n\n    var keys = keyList(a);\n    length = keys.length;\n\n    if (length !== keyList(b).length)\n      return false;\n\n    for (i = length; i-- !== 0;)\n      if (!hasProp.call(b, keys[i])) return false;\n    // end fast-deep-equal\n\n    // start react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element && b instanceof Element)\n      return a === b;\n\n    // custom handling for React\n    for (i = length; i-- !== 0;) {\n      key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of a react element\n        continue;\n      } else {\n        // all other properties should be traversed as usual\n        if (!equal(a[key], b[key])) return false;\n      }\n    }\n    // end react-fast-compare\n\n    // fast-deep-equal index.js 2.0.1\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function exportedEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if ((error.message && error.message.match(/stack|recursion/i)) || (error.number === -2146828260)) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('Warning: react-fast-compare does not handle circular references.', error.name, error.message);\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,KAAK,CAACD,OAAO;AAC3B,IAAIE,OAAO,GAAGC,MAAM,CAACC,IAAI;AACzB,IAAIC,OAAO,GAAGF,MAAM,CAACG,SAAS,CAACC,cAAc;AAC7C,IAAIC,cAAc,GAAG,OAAOC,OAAO,KAAK,WAAW;AAEnD,SAASC,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACnB;EACA,IAAID,CAAC,KAAKC,CAAC,EAAE,OAAO,IAAI;EAExB,IAAID,CAAC,IAAIC,CAAC,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,EAAE;IAC1D,IAAIC,IAAI,GAAGb,OAAO,CAACW,CAAC,CAAC;MACjBG,IAAI,GAAGd,OAAO,CAACY,CAAC,CAAC;MACjBG,CAAC;MACDC,MAAM;MACNC,GAAG;IAEP,IAAIJ,IAAI,IAAIC,IAAI,EAAE;MAChBE,MAAM,GAAGL,CAAC,CAACK,MAAM;MACjB,IAAIA,MAAM,IAAIJ,CAAC,CAACI,MAAM,EAAE,OAAO,KAAK;MACpC,KAAKD,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,KAAK,CAAC,GACxB,IAAI,CAACL,KAAK,CAACC,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;MACtC,OAAO,IAAI;IACb;IAEA,IAAIF,IAAI,IAAIC,IAAI,EAAE,OAAO,KAAK;IAE9B,IAAII,KAAK,GAAGP,CAAC,YAAYQ,IAAI;MACzBC,KAAK,GAAGR,CAAC,YAAYO,IAAI;IAC7B,IAAID,KAAK,IAAIE,KAAK,EAAE,OAAO,KAAK;IAChC,IAAIF,KAAK,IAAIE,KAAK,EAAE,OAAOT,CAAC,CAACU,OAAO,CAAC,CAAC,IAAIT,CAAC,CAACS,OAAO,CAAC,CAAC;IAErD,IAAIC,OAAO,GAAGX,CAAC,YAAYY,MAAM;MAC7BC,OAAO,GAAGZ,CAAC,YAAYW,MAAM;IACjC,IAAID,OAAO,IAAIE,OAAO,EAAE,OAAO,KAAK;IACpC,IAAIF,OAAO,IAAIE,OAAO,EAAE,OAAOb,CAAC,CAACc,QAAQ,CAAC,CAAC,IAAIb,CAAC,CAACa,QAAQ,CAAC,CAAC;IAE3D,IAAIrB,IAAI,GAAGF,OAAO,CAACS,CAAC,CAAC;IACrBK,MAAM,GAAGZ,IAAI,CAACY,MAAM;IAEpB,IAAIA,MAAM,KAAKd,OAAO,CAACU,CAAC,CAAC,CAACI,MAAM,EAC9B,OAAO,KAAK;IAEd,KAAKD,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,KAAK,CAAC,GACxB,IAAI,CAACV,OAAO,CAACqB,IAAI,CAACd,CAAC,EAAER,IAAI,CAACW,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;IAC7C;;IAEA;IACA;IACA,IAAIP,cAAc,IAAIG,CAAC,YAAYF,OAAO,IAAIG,CAAC,YAAYH,OAAO,EAChE,OAAOE,CAAC,KAAKC,CAAC;;IAEhB;IACA,KAAKG,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,KAAK,CAAC,GAAG;MAC3BE,GAAG,GAAGb,IAAI,CAACW,CAAC,CAAC;MACb,IAAIE,GAAG,KAAK,QAAQ,IAAIN,CAAC,CAACgB,QAAQ,EAAE;QAClC;QACA;QACA;QACA;QACA;MACF,CAAC,MAAM;QACL;QACA,IAAI,CAACjB,KAAK,CAACC,CAAC,CAACM,GAAG,CAAC,EAAEL,CAAC,CAACK,GAAG,CAAC,CAAC,EAAE,OAAO,KAAK;MAC1C;IACF;IACA;;IAEA;IACA,OAAO,IAAI;EACb;EAEA,OAAON,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC;AAC3B;AACA;;AAEAgB,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAACnB,CAAC,EAAEC,CAAC,EAAE;EAC5C,IAAI;IACF,OAAOF,KAAK,CAACC,CAAC,EAAEC,CAAC,CAAC;EACpB,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACd,IAAKA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC,kBAAkB,CAAC,IAAMF,KAAK,CAACG,MAAM,KAAK,CAAC,UAAW,EAAE;MAChG;MACA;MACA;MACA;MACA;MACAC,OAAO,CAACC,IAAI,CAAC,kEAAkE,EAAEL,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACC,OAAO,CAAC;MAC3G,OAAO,KAAK;IACd;IACA;IACA,MAAMD,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}