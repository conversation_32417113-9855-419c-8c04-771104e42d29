import React, { useState, useEffect, useRef } from 'react';
import {
  DevicePhoneMobileIcon,
  WifiIcon,
  CheckCircleIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  SignalIcon,
} from '@heroicons/react/24/outline';

interface SignatureRequest {
  request_id: string;
  document_id: string;
  signature_area_id: string;
  document_title: string;
  signature_area_label: string;
  viewer_device: string;
}

const MobileSigningApp: React.FC = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [deviceName, setDeviceName] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [pendingRequest, setPendingRequest] = useState<SignatureRequest | null>(null);
  const [showSignatureCanvas, setShowSignatureCanvas] = useState(false);
  const [notifications, setNotifications] = useState<string[]>([]);
  const [signature, setSignature] = useState<string>('');
  const wsRef = useRef<WebSocket | null>(null);
  const messageQueueRef = useRef<string[]>([]);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);

  useEffect(() => {
    // Set default device name based on user agent
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const defaultName = isMobile ? 'Mobile Device' : 'Tablet Device';
    setDeviceName(defaultName);

    // Check if accessed via QR code with URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const documentId = urlParams.get('documentId');
    const signatureAreaId = urlParams.get('signatureAreaId');
    const serverUrl = urlParams.get('server');

    if (documentId && signatureAreaId && serverUrl) {
      // Auto-connect when accessed via QR code
      addNotification(`QR Code detected for document: ${documentId}`);
      // Auto-connect after a short delay
      setTimeout(() => {
        if (!isConnected) {
          connectToServer();
        }
      }, 1000);
    }
  }, []);

  const sendMessage = (message: any) => {
    const messageStr = JSON.stringify(message);

    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(messageStr);
    } else {
      // Queue message for when connection is ready
      messageQueueRef.current.push(messageStr);
    }
  };

  const processMessageQueue = () => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      while (messageQueueRef.current.length > 0) {
        const message = messageQueueRef.current.shift();
        if (message) {
          wsRef.current.send(message);
        }
      }
    }
  };

  const connectToServer = () => {
    if (connectionStatus === 'connecting' || isConnected) return;
    
    setConnectionStatus('connecting');
    
    try {
      const ws = new WebSocket('ws://localhost:8001/ws/signing');
      wsRef.current = ws;

      ws.onopen = () => {
        setConnectionStatus('connected');
        setIsConnected(true);
        addNotification('Connected to signing server');

        // Process any queued messages
        processMessageQueue();

        // Register as a signing device
        sendMessage({
          type: 'register_device',
          device_name: deviceName,
          device_type: 'signer'
        });
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      };

      ws.onclose = () => {
        setConnectionStatus('disconnected');
        setIsConnected(false);
        addNotification('Disconnected from server');
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('disconnected');
        setIsConnected(false);
        addNotification('Connection error occurred');
      };
    } catch (error) {
      console.error('Failed to connect:', error);
      setConnectionStatus('disconnected');
      addNotification('Failed to connect to server');
    }
  };

  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'registration_confirmed':
        addNotification(`Registered as ${data.device_name}`);
        break;
      
      case 'signature_request_received':
        setPendingRequest({
          request_id: data.request_id,
          document_id: data.document_id,
          signature_area_id: data.signature_area_id,
          document_title: data.document_title,
          signature_area_label: data.signature_area_label,
          viewer_device: data.viewer_device
        });
        addNotification(`New signature request from ${data.viewer_device}`);
        break;
      
      case 'ping':
        sendMessage({ type: 'pong' });
        break;
    }
  };

  const addNotification = (message: string) => {
    setNotifications(prev => [message, ...prev.slice(0, 4)]);
  };

  const handleSignatureRequest = (accept: boolean) => {
    if (!pendingRequest) return;
    
    if (accept) {
      setShowSignatureCanvas(true);
      initializeCanvas();
    } else {
      // Reject the signature request
      sendMessage({
        type: 'signature_response',
        request_id: pendingRequest.request_id,
        response_type: 'rejected'
      });
      setPendingRequest(null);
      addNotification('Signature request rejected');
    }
  };

  const initializeCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = 400;
    canvas.height = 200;

    // Set drawing styles
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Clear canvas
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  };

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
    
    const x = clientX - rect.left;
    const y = clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.beginPath();
      ctx.moveTo(x, y);
    }
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
    
    const x = clientX - rect.left;
    const y = clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.lineTo(x, y);
      ctx.stroke();
    }
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    }
  };

  const handleSignatureComplete = () => {
    if (!pendingRequest || !canvasRef.current) return;

    // Convert canvas to base64 image
    const signatureData = canvasRef.current.toDataURL('image/png');

    // Send signature to server
    sendMessage({
      type: 'signature_response',
      request_id: pendingRequest.request_id,
      response_type: 'completed',
      signature_data: signatureData
    });

    setShowSignatureCanvas(false);
    setPendingRequest(null);
    addNotification('Signature sent successfully');
  };

  const handleSignatureReject = () => {
    if (!pendingRequest) return;

    sendMessage({
      type: 'signature_response',
      request_id: pendingRequest.request_id,
      response_type: 'rejected'
    });

    setShowSignatureCanvas(false);
    setPendingRequest(null);
    addNotification('Signature request rejected');
  };

  const disconnect = () => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    setIsConnected(false);
    setConnectionStatus('disconnected');
    setPendingRequest(null);
    addNotification('Disconnected from server');
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-4">
          <div className="flex items-center justify-center mb-4">
            <DevicePhoneMobileIcon className="h-12 w-12 text-blue-600" />
          </div>
          <h1 className="text-xl font-bold text-gray-900 text-center mb-2">
            CareSyncAI Mobile Signing
          </h1>
          <p className="text-sm text-gray-600 text-center">
            Wireless document signing device
          </p>
        </div>

        {/* Connection Status */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900">Connection Status</h2>
            <div className={`flex items-center ${
              isConnected ? 'text-green-600' : 'text-gray-400'
            }`}>
              <div className={`h-3 w-3 rounded-full mr-2 ${
                isConnected ? 'bg-green-500' : 'bg-gray-300'
              }`} />
              <SignalIcon className="h-5 w-5" />
            </div>
          </div>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Device Name
              </label>
              <input
                type="text"
                value={deviceName}
                onChange={(e) => setDeviceName(e.target.value)}
                disabled={isConnected}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                placeholder="Enter device name"
              />
            </div>
            
            <div className="flex space-x-3">
              {!isConnected ? (
                <button
                  onClick={connectToServer}
                  disabled={connectionStatus === 'connecting' || !deviceName.trim()}
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <WifiIcon className="h-4 w-4 mr-2" />
                  {connectionStatus === 'connecting' ? 'Connecting...' : 'Connect'}
                </button>
              ) : (
                <button
                  onClick={disconnect}
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                  <XMarkIcon className="h-4 w-4 mr-2" />
                  Disconnect
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Pending Signature Request */}
        {pendingRequest && !showSignatureCanvas && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-4 border-l-4 border-blue-500">
            <div className="flex items-start">
              <ExclamationTriangleIcon className="h-6 w-6 text-blue-600 mr-3 mt-1" />
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Signature Request
                </h3>
                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <p><strong>Document:</strong> {pendingRequest.document_title}</p>
                  <p><strong>Signature Area:</strong> {pendingRequest.signature_area_label}</p>
                  <p><strong>From:</strong> {pendingRequest.viewer_device}</p>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleSignatureRequest(true)}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    Accept & Sign
                  </button>
                  <button
                    onClick={() => handleSignatureRequest(false)}
                    className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  >
                    <XMarkIcon className="h-4 w-4 mr-2" />
                    Reject
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Signature Canvas */}
        {showSignatureCanvas && pendingRequest && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Sign Document: {pendingRequest.document_title}
            </h3>
            
            <div className="border border-gray-300 rounded-lg p-4 mb-4">
              <canvas
                ref={canvasRef}
                className="w-full border border-gray-200 rounded cursor-crosshair touch-none"
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={stopDrawing}
                onMouseLeave={stopDrawing}
                onTouchStart={startDrawing}
                onTouchMove={draw}
                onTouchEnd={stopDrawing}
                style={{ touchAction: 'none' }}
              />
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={clearSignature}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                Clear
              </button>
              <button
                onClick={handleSignatureComplete}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Complete Signature
              </button>
              <button
                onClick={handleSignatureReject}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Notifications */}
        {notifications.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Recent Activity</h3>
            <div className="space-y-2">
              {notifications.map((notification, index) => (
                <div key={index} className="text-sm text-gray-600 p-2 bg-gray-50 rounded">
                  {notification}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileSigningApp;
