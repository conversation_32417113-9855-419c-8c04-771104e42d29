"""
Authentication and authorization for CareSyncAI
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTTPException, status
from core.config import settings
from core.database import get_db
import logging

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Dict[str, Any]:
    """Verify JWT token and return payload"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

class AuthService:
    """Authentication service using Supabase Auth"""
    
    def __init__(self):
        self.client = get_db()
    
    async def authenticate_user(self, email: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user with email and password"""
        try:
            # Use Supabase Auth for authentication
            auth_response = self.client.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            
            if auth_response.user:
                # Get caregiver details
                caregiver_result = self.client.table("caregivers").select("*").eq(
                    "user_id", auth_response.user.id
                ).execute()
                
                if caregiver_result.data:
                    caregiver = caregiver_result.data[0]
                    return {
                        "user_id": auth_response.user.id,
                        "caregiver_id": caregiver["id"],
                        "email": auth_response.user.email,
                        "role": caregiver["role"],
                        "first_name": caregiver["first_name"],
                        "last_name": caregiver["last_name"],
                        "is_active": caregiver["is_active"]
                    }
            
            return None
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    async def register_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Register a new user"""
        try:
            # Create user in Supabase Auth
            auth_response = self.client.auth.sign_up({
                "email": user_data["email"],
                "password": user_data["password"]
            })
            
            if auth_response.user:
                # Create caregiver record
                caregiver_data = {
                    "user_id": auth_response.user.id,
                    "first_name": user_data["first_name"],
                    "last_name": user_data["last_name"],
                    "email": user_data["email"],
                    "phone": user_data.get("phone"),
                    "role": user_data.get("role", "caregiver"),
                    "license_number": user_data.get("license_number"),
                    "license_expiry": user_data.get("license_expiry"),
                    "address": user_data.get("address"),
                    "emergency_contact": user_data.get("emergency_contact"),
                    "certifications": user_data.get("certifications", [])
                }
                
                caregiver_result = self.client.table("caregivers").insert(caregiver_data).execute()
                
                if caregiver_result.data:
                    return {
                        "user_id": auth_response.user.id,
                        "caregiver_id": caregiver_result.data[0]["id"],
                        "email": auth_response.user.email,
                        "message": "User registered successfully"
                    }
            
            raise Exception("Failed to create user")
        except Exception as e:
            logger.error(f"Registration error: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Registration failed: {str(e)}"
            )
    
    async def get_current_user_details(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get current user details"""
        try:
            caregiver_result = self.client.table("caregivers").select("*").eq(
                "user_id", user_id
            ).execute()
            
            if caregiver_result.data:
                return caregiver_result.data[0]
            return None
        except Exception as e:
            logger.error(f"Error getting user details: {e}")
            return None
    
    async def update_user_profile(self, user_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update user profile"""
        try:
            result = self.client.table("caregivers").update(update_data).eq(
                "user_id", user_id
            ).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise Exception("Failed to update profile")
        except Exception as e:
            logger.error(f"Error updating profile: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Profile update failed: {str(e)}"
            )
    
    async def change_password(self, user_id: str, new_password: str) -> bool:
        """Change user password"""
        try:
            # Use Supabase Auth to update password
            auth_response = self.client.auth.update_user({
                "password": new_password
            })
            
            return auth_response.user is not None
        except Exception as e:
            logger.error(f"Error changing password: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Password change failed: {str(e)}"
            )
    
    async def reset_password(self, email: str) -> bool:
        """Send password reset email"""
        try:
            self.client.auth.reset_password_email(email)
            return True
        except Exception as e:
            logger.error(f"Error sending password reset: {e}")
            return False

# Permission checking functions
def check_admin_permission(user_role: str) -> bool:
    """Check if user has admin permissions"""
    return user_role == "admin"

def check_supervisor_permission(user_role: str) -> bool:
    """Check if user has supervisor permissions"""
    return user_role in ["admin", "supervisor"]

def check_billing_permission(user_role: str) -> bool:
    """Check if user has billing permissions"""
    return user_role in ["admin", "billing"]

def check_patient_access(user_id: str, patient_id: str, user_role: str) -> bool:
    """Check if user has access to specific patient"""
    if user_role in ["admin", "supervisor"]:
        return True
    
    # Check if user is assigned to patient
    try:
        client = get_db()
        result = client.table("patients").select("primary_caregiver_id", "backup_caregiver_id").eq(
            "id", patient_id
        ).execute()
        
        if result.data:
            patient = result.data[0]
            return (patient["primary_caregiver_id"] == user_id or 
                   patient["backup_caregiver_id"] == user_id)
        return False
    except Exception as e:
        logger.error(f"Error checking patient access: {e}")
        return False
