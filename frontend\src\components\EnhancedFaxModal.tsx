import React, { useState } from 'react';
import {
  PhoneIcon,
  UserIcon,
  PaperAirplaneIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { FaxRecipient } from '../services/faxService';

interface Document {
  id: string;
  name: string;
  residentName?: string;
  type: string;
}

interface EnhancedFaxModalProps {
  isOpen: boolean;
  onClose: () => void;
  document: Document;
  recipients: FaxRecipient[];
  onSendFax: (recipients: FaxRecipient[], faxData: any) => void;
}

const EnhancedFaxModal: React.FC<EnhancedFaxModalProps> = ({
  isOpen,
  onClose,
  document,
  recipients,
  onSendFax
}) => {
  const [selectedRecipients, setSelectedRecipients] = useState<FaxRecipient[]>([]);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [faxSubject, setFaxSubject] = useState(`${document.name} - ${document.residentName || 'Care-SolAI'}`);
  const [faxMessage, setFaxMessage] = useState('Please find attached document for your review.');
  const [faxUrgent, setFaxUrgent] = useState(false);
  const [faxConfidential, setFaxConfidential] = useState(true);
  
  const [manualRecipient, setManualRecipient] = useState<Partial<FaxRecipient>>({
    name: '',
    faxNumber: '',
    email: '',
    phone: '',
    organization: '',
    type: 'other',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA'
    }
  });

  const toggleRecipientSelection = (recipient: FaxRecipient) => {
    setSelectedRecipients(prev => {
      const isSelected = prev.some(r => r.faxNumber === recipient.faxNumber);
      if (isSelected) {
        return prev.filter(r => r.faxNumber !== recipient.faxNumber);
      } else {
        return [...prev, recipient];
      }
    });
  };

  const addManualRecipient = () => {
    if (!manualRecipient.name || !manualRecipient.faxNumber) {
      alert('Please fill in at least the name and fax number');
      return;
    }

    const newRecipient: FaxRecipient = {
      name: manualRecipient.name,
      faxNumber: manualRecipient.faxNumber,
      email: manualRecipient.email,
      phone: manualRecipient.phone,
      organization: manualRecipient.organization,
      type: manualRecipient.type || 'other',
      address: manualRecipient.address,
      department: manualRecipient.department,
      title: manualRecipient.title,
      notes: manualRecipient.notes
    };

    setSelectedRecipients(prev => [...prev, newRecipient]);
    setShowManualEntry(false);
    setManualRecipient({
      name: '',
      faxNumber: '',
      email: '',
      phone: '',
      organization: '',
      type: 'other',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA'
      }
    });
  };

  const handleSendFax = () => {
    if (selectedRecipients.length === 0) {
      alert('Please select at least one recipient');
      return;
    }

    if (!faxSubject.trim()) {
      alert('Please enter a subject');
      return;
    }

    const faxData = {
      subject: faxSubject,
      message: faxMessage,
      urgent: faxUrgent,
      confidential: faxConfidential
    };

    onSendFax(selectedRecipients, faxData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <PhoneIcon className="h-6 w-6 text-indigo-600 mr-3" />
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Enhanced Fax System
                </h3>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-500">
                  {selectedRecipients.length} recipient(s) selected
                </div>
                <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>
            </div>

            <div className="mb-4 p-3 bg-gray-50 rounded-md">
              <p className="text-sm text-gray-600">
                <strong>Document:</strong> {document.name}
              </p>
              {document.residentName && (
                <p className="text-sm text-gray-600">
                  <strong>Resident:</strong> {document.residentName}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column - Recipients */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700">
                    Recipients & Contact Information
                  </label>
                  <button
                    onClick={() => setShowManualEntry(!showManualEntry)}
                    className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <UserIcon className="h-3 w-3 mr-1" />
                    {showManualEntry ? 'Hide Manual Entry' : 'Add Manual Entry'}
                  </button>
                </div>

                {/* Common Recipients */}
                <div className="space-y-2 max-h-48 overflow-y-auto mb-4">
                  {recipients.map((recipient, index) => (
                    <label key={index} className={`flex items-start p-3 border rounded-md cursor-pointer transition-colors ${
                      selectedRecipients.some(r => r.faxNumber === recipient.faxNumber)
                        ? 'border-indigo-500 bg-indigo-50'
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}>
                      <input
                        type="checkbox"
                        checked={selectedRecipients.some(r => r.faxNumber === recipient.faxNumber)}
                        onChange={() => toggleRecipientSelection(recipient)}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded mt-1"
                      />
                      <div className="ml-3 flex-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-900">{recipient.name}</span>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            recipient.type === 'doctor' ? 'bg-blue-100 text-blue-800' :
                            recipient.type === 'hospital' ? 'bg-red-100 text-red-800' :
                            recipient.type === 'pharmacy' ? 'bg-green-100 text-green-800' :
                            recipient.type === 'insurance' ? 'bg-purple-100 text-purple-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {recipient.type.toUpperCase()}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500 mt-1 space-y-1">
                          <div><strong>Organization:</strong> {recipient.organization}</div>
                          <div className="flex items-center space-x-3">
                            <span><strong>📠 Fax:</strong> {recipient.faxNumber}</span>
                            {recipient.email && <span><strong>✉️ Email:</strong> {recipient.email}</span>}
                          </div>
                          {recipient.phone && (
                            <div><strong>📞 Phone:</strong> {recipient.phone}</div>
                          )}
                          {recipient.address && (
                            <div className="text-xs text-gray-400 mt-1">
                              <strong>📍 Address:</strong> {recipient.address.street}, {recipient.address.city}, {recipient.address.state} {recipient.address.zipCode}
                            </div>
                          )}
                          {recipient.department && (
                            <div><strong>Department:</strong> {recipient.department}</div>
                          )}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>

                {/* Manual Entry Form */}
                {showManualEntry && (
                  <div className="border border-indigo-300 rounded-lg p-4 bg-indigo-50">
                    <h4 className="text-sm font-medium text-indigo-900 mb-3 flex items-center">
                      <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
                      Add New Recipient - Complete Contact Information
                    </h4>
                    <div className="grid grid-cols-1 gap-3">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-700">Full Name *</label>
                          <input
                            type="text"
                            value={manualRecipient.name || ''}
                            onChange={(e) => setManualRecipient(prev => ({ ...prev, name: e.target.value }))}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                            placeholder="Dr. John Smith"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700">Fax Number *</label>
                          <input
                            type="tel"
                            value={manualRecipient.faxNumber || ''}
                            onChange={(e) => setManualRecipient(prev => ({ ...prev, faxNumber: e.target.value }))}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                            placeholder="******-0123"
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-700">Email Address</label>
                          <input
                            type="email"
                            value={manualRecipient.email || ''}
                            onChange={(e) => setManualRecipient(prev => ({ ...prev, email: e.target.value }))}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                            placeholder="<EMAIL>"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700">Phone Number</label>
                          <input
                            type="tel"
                            value={manualRecipient.phone || ''}
                            onChange={(e) => setManualRecipient(prev => ({ ...prev, phone: e.target.value }))}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                            placeholder="******-0124"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700">Organization/Clinic</label>
                        <input
                          type="text"
                          value={manualRecipient.organization || ''}
                          onChange={(e) => setManualRecipient(prev => ({ ...prev, organization: e.target.value }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                          placeholder="Springfield Medical Center"
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-700">Recipient Type</label>
                          <select
                            value={manualRecipient.type || 'other'}
                            onChange={(e) => setManualRecipient(prev => ({ ...prev, type: e.target.value as any }))}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                          >
                            <option value="doctor">Doctor/Physician</option>
                            <option value="hospital">Hospital</option>
                            <option value="pharmacy">Pharmacy</option>
                            <option value="insurance">Insurance Company</option>
                            <option value="family">Family Member</option>
                            <option value="legal">Legal/Attorney</option>
                            <option value="government">Government Agency</option>
                            <option value="other">Other</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700">Department</label>
                          <input
                            type="text"
                            value={manualRecipient.department || ''}
                            onChange={(e) => setManualRecipient(prev => ({ ...prev, department: e.target.value }))}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                            placeholder="Medical Records"
                          />
                        </div>
                      </div>

                      {/* Address Fields - Emphasized */}
                      <div className="border-t border-indigo-200 pt-3">
                        <label className="block text-xs font-medium text-indigo-700 mb-2 flex items-center">
                          <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                          Complete Mailing Address (Highly Recommended for Legal/Medical Faxes)
                        </label>
                        <div className="grid grid-cols-1 gap-2">
                          <input
                            type="text"
                            value={manualRecipient.address?.street || ''}
                            onChange={(e) => setManualRecipient(prev => ({ 
                              ...prev, 
                              address: { ...prev.address!, street: e.target.value }
                            }))}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                            placeholder="Street Address"
                          />
                          <div className="grid grid-cols-3 gap-2">
                            <input
                              type="text"
                              value={manualRecipient.address?.city || ''}
                              onChange={(e) => setManualRecipient(prev => ({ 
                                ...prev, 
                                address: { ...prev.address!, city: e.target.value }
                              }))}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                              placeholder="City"
                            />
                            <input
                              type="text"
                              value={manualRecipient.address?.state || ''}
                              onChange={(e) => setManualRecipient(prev => ({ 
                                ...prev, 
                                address: { ...prev.address!, state: e.target.value }
                              }))}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                              placeholder="State"
                            />
                            <input
                              type="text"
                              value={manualRecipient.address?.zipCode || ''}
                              onChange={(e) => setManualRecipient(prev => ({ 
                                ...prev, 
                                address: { ...prev.address!, zipCode: e.target.value }
                              }))}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                              placeholder="ZIP Code"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-end space-x-2 pt-2">
                        <button
                          onClick={() => setShowManualEntry(false)}
                          className="px-3 py-1 text-xs border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={addManualRecipient}
                          className="px-3 py-1 text-xs bg-indigo-600 text-white rounded hover:bg-indigo-700"
                        >
                          Add Recipient
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Selected Recipients Summary */}
                {selectedRecipients.length > 0 && (
                  <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                    <h4 className="text-sm font-medium text-green-800 mb-2 flex items-center">
                      <CheckCircleIcon className="h-4 w-4 mr-2" />
                      Selected Recipients ({selectedRecipients.length})
                    </h4>
                    <div className="space-y-1">
                      {selectedRecipients.map((recipient, index) => (
                        <div key={index} className="flex items-center justify-between text-xs text-green-700">
                          <span>{recipient.name} - {recipient.faxNumber}</span>
                          <button
                            onClick={() => setSelectedRecipients(prev => prev.filter((_, i) => i !== index))}
                            className="text-red-600 hover:text-red-800"
                          >
                            ✕
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Right Column - Fax Details */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Fax Content & Settings</h4>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Subject Line *</label>
                    <input
                      type="text"
                      value={faxSubject}
                      onChange={(e) => setFaxSubject(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      placeholder="Document subject"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Cover Page Message</label>
                    <textarea
                      rows={4}
                      value={faxMessage}
                      onChange={(e) => setFaxMessage(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      placeholder="Please include any special instructions or context for the recipient..."
                    />
                  </div>

                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={faxUrgent}
                        onChange={(e) => setFaxUrgent(e.target.checked)}
                        className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Mark as urgent/priority</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={faxConfidential}
                        onChange={(e) => setFaxConfidential(e.target.checked)}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Mark as confidential/HIPAA protected</span>
                    </label>
                  </div>

                  {/* Sender Information */}
                  <div className="border-t border-gray-200 pt-4">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Sender Information</h5>
                    <div className="text-xs text-gray-600 space-y-1 bg-gray-50 p-3 rounded">
                      <div><strong>Organization:</strong> CareSyncAI Care Facility</div>
                      <div><strong>Fax Number:</strong> ******-0100</div>
                      <div><strong>Phone:</strong> ******-0101</div>
                      <div><strong>Email:</strong> <EMAIL></div>
                      <div><strong>Address:</strong> 123 Care Lane, Springfield, IL 62701</div>
                    </div>
                  </div>

                  {/* Key Functionalities */}
                  <div className="border-t border-gray-200 pt-4">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Enhanced Features</h5>
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>✅ Delivery confirmation tracking</div>
                      <div>✅ HIPAA-compliant transmission</div>
                      <div>✅ Automatic retry on failure</div>
                      <div>✅ Digital signature verification</div>
                      <div>✅ Audit trail logging</div>
                      <div>✅ Multi-recipient support</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={handleSendFax}
              disabled={selectedRecipients.length === 0 || !faxSubject.trim()}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm"
            >
              <PaperAirplaneIcon className="h-4 w-4 mr-2" />
              Send Fax to {selectedRecipients.length} Recipient{selectedRecipients.length !== 1 ? 's' : ''}
            </button>
            <button
              onClick={onClose}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedFaxModal;
