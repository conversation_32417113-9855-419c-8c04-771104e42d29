{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\medisyn\\\\frontend\\\\src\\\\pages\\\\PatientDetail.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PatientDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-2\",\n            children: \"Patient Detail View\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-4\",\n            children: [\"Patient ID: \", id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"This page will show detailed patient information, medical records, care plans, and AI predictions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(PatientDetail, \"yQgCIz/jJfqV1l9s2yoba81MT5A=\", false, function () {\n  return [useParams];\n});\n_c = PatientDetail;\nexport default PatientDetail;\nvar _c;\n$RefreshReg$(_c, \"PatientDetail\");", "map": {"version": 3, "names": ["React", "useParams", "jsxDEV", "_jsxDEV", "PatientDetail", "_s", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/src/pages/PatientDetail.tsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\n\nconst PatientDetail: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"card\">\n        <div className=\"card-body\">\n          <div className=\"text-center py-12\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Patient Detail View\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              Patient ID: {id}\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              This page will show detailed patient information, medical records,\n              care plans, and AI predictions.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PatientDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAG,CAAC,GAAGL,SAAS,CAAiB,CAAC;EAE1C,oBACEE,OAAA;IAAKI,SAAS,EAAC,WAAW;IAAAC,QAAA,eACxBL,OAAA;MAAKI,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBL,OAAA;QAAKI,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBL,OAAA;UAAKI,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCL,OAAA;YAAII,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLT,OAAA;YAAGI,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,cACpB,EAACF,EAAE;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACJT,OAAA;YAAGI,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAGrC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACP,EAAA,CAvBID,aAAuB;EAAA,QACZH,SAAS;AAAA;AAAAY,EAAA,GADpBT,aAAuB;AAyB7B,eAAeA,aAAa;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}