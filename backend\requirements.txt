# FastAPI and Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database and ORM
supabase==2.3.0
sqlalchemy==2.0.23
asyncpg==0.29.0

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP Client and API Integration
httpx==0.25.2
requests==2.31.0
aiohttp==3.9.1

# Zoho Books Integration
zoho-books-python==1.0.0

# AI/ML Dependencies
tensorflow==2.15.0
transformers==4.36.0
torch==2.1.1
scikit-learn==1.3.2
numpy==1.24.3
pandas==2.1.4

# NLP and Text Processing
nltk==3.8.1
spacy==3.7.2

# AWS Integration
boto3==1.34.0
botocore==1.34.0

# Environment and Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Logging and Monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Date and Time
python-dateutil==2.8.2

# File Processing
python-magic==0.4.27
Pillow==10.1.0

# Async Support
asyncio==3.4.3
aiofiles==23.2.1

# CORS and Middleware
python-cors==1.7.0

# Validation and Serialization
marshmallow==3.20.1
