import React, { useState, useRef } from 'react';
import {
  DocumentTextIcon,
  CameraIcon,
  PencilIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  PhoneIcon,
  PaperAirplaneIcon,
  InboxIcon,
  ArrowPathIcon,
  DevicePhoneMobileIcon,
  WifiIcon,
  DocumentDuplicateIcon,
  CogIcon,
  SparklesIcon,
  CloudArrowUpIcon,
} from '@heroicons/react/24/outline';
import SignatureCanvas from '../components/SignatureCanvas';
import WirelessSigningManager from '../components/WirelessSigning/WirelessSigningManager';
import EnhancedFaxModal from '../components/EnhancedFaxModal';
import { documentService } from '../services/documentService';
import { faxService, FaxRecipient } from '../services/faxService';

interface Document {
  id: string;
  name: string;
  type: 'admission' | 'care_plan' | 'medication_consent' | 'incident_report' | 'insurance' | 'assessment' | 'other';
  residentId?: string;
  residentName?: string;
  status: 'draft' | 'pending_signature' | 'signed' | 'expired';
  createdDate: string;
  lastModified: string;
  signedBy?: string[];
  requiredSignatures?: string[];
  fileUrl?: string;
  thumbnailUrl?: string;
  aiAnalysis?: {
    documentType: string;
    confidence: number;
    extractedData: any;
    compliance: boolean;
  };
}

interface DocumentTemplate {
  id: string;
  name: string;
  description: string;
  type: 'admission' | 'care_plan' | 'medication_consent' | 'incident_report' | 'insurance' | 'assessment' | 'other';
  fileUrl: string;
  thumbnailUrl?: string;
  variables: TemplateVariable[];
  createdDate: string;
  lastUsed?: string;
  usageCount: number;
  isActive: boolean;
}

interface TemplateVariable {
  name: string;
  label: string;
  type: 'text' | 'date' | 'number' | 'select' | 'resident' | 'staff';
  required: boolean;
  defaultValue?: string;
  options?: string[]; // For select type
  placeholder?: string;
}

const Documents: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: '1',
      name: 'Admission Agreement - Margaret Thompson',
      type: 'admission',
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      status: 'signed',
      createdDate: '2024-01-15',
      lastModified: '2024-01-15',
      signedBy: ['Margaret Thompson', 'John Thompson (Son)', 'Sarah Johnson (Administrator)'],
      requiredSignatures: ['Resident', 'Family Member', 'Administrator'],
      aiAnalysis: {
        documentType: 'Admission Agreement',
        confidence: 0.98,
        extractedData: { residentName: 'Margaret Thompson', admissionDate: '2024-01-20' },
        compliance: true
      }
    },
    {
      id: '2',
      name: 'Medication Consent Form - William Anderson',
      type: 'medication_consent',
      residentId: 'R002',
      residentName: 'William Anderson',
      status: 'pending_signature',
      createdDate: '2024-01-18',
      lastModified: '2024-01-18',
      signedBy: ['Dr. Smith'],
      requiredSignatures: ['Resident', 'Doctor', 'Nurse'],
      aiAnalysis: {
        documentType: 'Medication Consent',
        confidence: 0.95,
        extractedData: { medications: ['Lisinopril 10mg', 'Metformin 500mg'] },
        compliance: true
      }
    },
    {
      id: '3',
      name: 'Monthly Care Assessment - Dorothy Garcia',
      type: 'assessment',
      residentId: 'R003',
      residentName: 'Dorothy Garcia',
      status: 'draft',
      createdDate: '2024-01-20',
      lastModified: '2024-01-20',
      requiredSignatures: ['Nurse', 'Care Coordinator'],
      aiAnalysis: {
        documentType: 'Care Assessment',
        confidence: 0.92,
        extractedData: { assessmentDate: '2024-01-20', careLevel: 'Moderate Assistance' },
        compliance: false
      }
    }
  ]);

  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showScanner, setShowScanner] = useState(false);
  const [showESignModal, setShowESignModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [signature, setSignature] = useState<string | null>(null);
  const [showFaxModal, setShowFaxModal] = useState(false);
  const [faxRecipients, setFaxRecipients] = useState<FaxRecipient[]>([]);
  const [selectedFaxDocument, setSelectedFaxDocument] = useState<Document | null>(null);
  const [selectedRecipients, setSelectedRecipients] = useState<FaxRecipient[]>([]);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [manualRecipient, setManualRecipient] = useState<Partial<FaxRecipient>>({
    name: '',
    faxNumber: '',
    email: '',
    phone: '',
    organization: '',
    type: 'other',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA'
    }
  });
  const [faxSubject, setFaxSubject] = useState('');
  const [faxMessage, setFaxMessage] = useState('');
  const [faxUrgent, setFaxUrgent] = useState(false);
  const [faxConfidential, setFaxConfidential] = useState(true);
  const [showWirelessSigning, setShowWirelessSigning] = useState(false);
  const [wirelessSigningDocument, setWirelessSigningDocument] = useState<Document | null>(null);
  const [signatureAreaId, setSignatureAreaId] = useState<string>('');

  // Document Creation States
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newDocument, setNewDocument] = useState({
    name: '',
    type: 'other' as Document['type'],
    residentId: '',
    residentName: '',
    description: '',
    category: 'general'
  });

  // Template Management States
  const [activeTab, setActiveTab] = useState<'documents' | 'templates' | 'generate' | 'inbound-faxes'>('documents');
  const [templates, setTemplates] = useState<DocumentTemplate[]>([
    {
      id: 'template-1',
      name: 'Admission Agreement Template',
      description: 'Standard admission agreement for new residents',
      type: 'admission',
      fileUrl: '/templates/admission-template.pdf',
      variables: [
        { name: 'residentName', label: 'Resident Name', type: 'resident', required: true, placeholder: 'Select resident' },
        { name: 'admissionDate', label: 'Admission Date', type: 'date', required: true, placeholder: 'Select date' },
        { name: 'roomNumber', label: 'Room Number', type: 'text', required: true, placeholder: 'Enter room number' },
        { name: 'emergencyContact', label: 'Emergency Contact', type: 'text', required: true, placeholder: 'Enter contact name' },
        { name: 'emergencyPhone', label: 'Emergency Phone', type: 'text', required: true, placeholder: 'Enter phone number' }
      ],
      createdDate: '2024-01-15',
      lastUsed: '2024-01-20',
      usageCount: 15,
      isActive: true
    },
    {
      id: 'template-2',
      name: 'Care Plan Template',
      description: 'Comprehensive care plan for resident care management',
      type: 'care_plan',
      fileUrl: '/templates/care-plan-template.pdf',
      variables: [
        { name: 'residentName', label: 'Resident Name', type: 'resident', required: true, placeholder: 'Select resident' },
        { name: 'careLevel', label: 'Care Level', type: 'select', required: true, options: ['Independent', 'Assisted', 'Memory Care', 'Skilled Nursing'], placeholder: 'Select care level' },
        { name: 'primaryPhysician', label: 'Primary Physician', type: 'text', required: true, placeholder: 'Enter physician name' },
        { name: 'medications', label: 'Current Medications', type: 'text', required: false, placeholder: 'List current medications' },
        { name: 'allergies', label: 'Known Allergies', type: 'text', required: false, placeholder: 'List allergies' },
        { name: 'careCoordinator', label: 'Care Coordinator', type: 'staff', required: true, placeholder: 'Select staff member' }
      ],
      createdDate: '2024-01-10',
      lastUsed: '2024-01-22',
      usageCount: 28,
      isActive: true
    },
    {
      id: 'template-3',
      name: 'Medication Consent Form',
      description: 'Consent form for medication administration',
      type: 'medication_consent',
      fileUrl: '/templates/medication-consent-template.pdf',
      variables: [
        { name: 'residentName', label: 'Resident Name', type: 'resident', required: true, placeholder: 'Select resident' },
        { name: 'medicationName', label: 'Medication Name', type: 'text', required: true, placeholder: 'Enter medication name' },
        { name: 'dosage', label: 'Dosage', type: 'text', required: true, placeholder: 'Enter dosage' },
        { name: 'frequency', label: 'Frequency', type: 'select', required: true, options: ['Once daily', 'Twice daily', 'Three times daily', 'Four times daily', 'As needed'], placeholder: 'Select frequency' },
        { name: 'prescribingPhysician', label: 'Prescribing Physician', type: 'text', required: true, placeholder: 'Enter physician name' },
        { name: 'startDate', label: 'Start Date', type: 'date', required: true, placeholder: 'Select start date' }
      ],
      createdDate: '2024-01-12',
      lastUsed: '2024-01-21',
      usageCount: 42,
      isActive: true
    }
  ]);
  const [showTemplateUpload, setShowTemplateUpload] = useState(false);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [templateVariables, setTemplateVariables] = useState<{[key: string]: string}>({});

  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const templateFileInputRef = useRef<HTMLInputElement>(null);

  const documentTypes = [
    { value: 'all', label: 'All Documents' },
    { value: 'admission', label: 'Admission Forms' },
    { value: 'care_plan', label: 'Care Plans' },
    { value: 'medication_consent', label: 'Medication Consent' },
    { value: 'incident_report', label: 'Incident Reports' },
    { value: 'insurance', label: 'Insurance Documents' },
    { value: 'assessment', label: 'Assessments' },
    { value: 'other', label: 'Other' }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'draft', label: 'Draft' },
    { value: 'pending_signature', label: 'Pending Signature' },
    { value: 'signed', label: 'Signed' },
    { value: 'expired', label: 'Expired' }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'signed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'pending_signature':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'expired':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <DocumentTextIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'signed':
        return 'bg-green-100 text-green-800';
      case 'pending_signature':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'admission':
        return 'bg-blue-100 text-blue-800';
      case 'care_plan':
        return 'bg-green-100 text-green-800';
      case 'medication_consent':
        return 'bg-purple-100 text-purple-800';
      case 'incident_report':
        return 'bg-red-100 text-red-800';
      case 'insurance':
        return 'bg-indigo-100 text-indigo-800';
      case 'assessment':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      try {
        const file = files[0];
        const result = await documentService.scanDocument(file);

        // Add the processed document to the list
        const newDocument: Document = {
          id: result.id,
          name: result.originalFileName,
          type: result.documentType.toLowerCase().replace(' ', '_') as any,
          status: 'draft',
          createdDate: new Date().toISOString().split('T')[0],
          lastModified: new Date().toISOString().split('T')[0],
          requiredSignatures: ['Administrator'],
          aiAnalysis: {
            documentType: result.documentType,
            confidence: result.confidence,
            extractedData: result.extractedData,
            compliance: result.complianceCheck.isCompliant
          }
        };

        setDocuments(prev => [newDocument, ...prev]);
        alert(`Document processed successfully! AI detected: ${result.documentType} with ${Math.round(result.confidence * 100)}% confidence`);
      } catch (error) {
        alert('Failed to process document. Please try again.');
      }
    }
  };

  const handleCameraCapture = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      try {
        const file = files[0];
        const result = await documentService.scanDocument(file);

        const newDocument: Document = {
          id: result.id,
          name: `Scanned - ${result.documentType}`,
          type: result.documentType.toLowerCase().replace(' ', '_') as any,
          status: 'draft',
          createdDate: new Date().toISOString().split('T')[0],
          lastModified: new Date().toISOString().split('T')[0],
          requiredSignatures: ['Staff Member'],
          aiAnalysis: {
            documentType: result.documentType,
            confidence: result.confidence,
            extractedData: result.extractedData,
            compliance: result.complianceCheck.isCompliant
          }
        };

        setDocuments(prev => [newDocument, ...prev]);
        alert(`Document scanned successfully! AI detected: ${result.documentType}`);
      } catch (error) {
        alert('Failed to scan document. Please try again.');
      }
    }
  };

  const handleESign = (document: Document) => {
    setSelectedDocument(document);
    setShowESignModal(true);
  };

  const handleFax = async (document: Document) => {
    setSelectedFaxDocument(document);
    setSelectedRecipients([]);
    setFaxSubject(`${document.name} - ${document.residentName || 'CareSyncAI'}`);
    setFaxMessage('Please find attached document for your review.');
    setFaxUrgent(false);
    setFaxConfidential(true);
    setShowManualEntry(false);
    setManualRecipient({
      name: '',
      faxNumber: '',
      email: '',
      phone: '',
      organization: '',
      type: 'other',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA'
      }
    });

    try {
      const recipients = await faxService.getCommonRecipients();
      setFaxRecipients(recipients);
      setShowFaxModal(true);
    } catch (error) {
      alert('Failed to load fax recipients');
    }
  };

  const toggleRecipientSelection = (recipient: FaxRecipient) => {
    setSelectedRecipients(prev => {
      const isSelected = prev.some(r => r.faxNumber === recipient.faxNumber);
      if (isSelected) {
        return prev.filter(r => r.faxNumber !== recipient.faxNumber);
      } else {
        return [...prev, recipient];
      }
    });
  };

  const addManualRecipient = () => {
    if (!manualRecipient.name || !manualRecipient.faxNumber) {
      alert('Please fill in at least the name and fax number');
      return;
    }

    const newRecipient: FaxRecipient = {
      name: manualRecipient.name,
      faxNumber: manualRecipient.faxNumber,
      email: manualRecipient.email,
      phone: manualRecipient.phone,
      organization: manualRecipient.organization,
      type: manualRecipient.type || 'other',
      address: manualRecipient.address,
      department: manualRecipient.department,
      title: manualRecipient.title,
      notes: manualRecipient.notes
    };

    setSelectedRecipients(prev => [...prev, newRecipient]);
    setShowManualEntry(false);
    setManualRecipient({
      name: '',
      faxNumber: '',
      email: '',
      phone: '',
      organization: '',
      type: 'other',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA'
      }
    });
  };

  const sendFax = async (recipients: FaxRecipient[], faxData: any) => {
    if (!selectedFaxDocument) return;

    try {
      const faxRequest = {
        recipients,
        documents: [{
          documentId: selectedFaxDocument.id,
          documentName: selectedFaxDocument.name,
          documentType: selectedFaxDocument.type,
          pages: 1,
          fileUrl: '/mock-document.pdf'
        }],
        coverPage: {
          enabled: true,
          subject: faxData.subject,
          message: faxData.message,
          urgent: faxData.urgent,
          confidential: faxData.confidential
        },
        priority: (faxData.urgent ? 'urgent' : 'normal') as 'normal' | 'high' | 'urgent',
        residentId: selectedFaxDocument.residentId,
        residentName: selectedFaxDocument.residentName,
        senderInfo: {
          name: 'CareSyncAI Residential Care',
          organization: 'CareSyncAI Care Facility',
          faxNumber: '******-0100',
          phone: '******-0101',
          email: '<EMAIL>'
        }
      };

      const result = await faxService.sendFax(faxRequest);
      alert(`Fax sent successfully to ${recipients.length} recipient(s)! Fax ID: ${result.faxId}\n\nRecipients:\n${recipients.map(r => `• ${r.name} (${r.faxNumber})`).join('\n')}`);
      setShowFaxModal(false);
    } catch (error) {
      alert('Failed to send fax. Please try again.');
    }
  };

  const handleWirelessSigning = (document: Document, areaId: string = 'signature-area-1') => {
    setWirelessSigningDocument(document);
    setSignatureAreaId(areaId);
    setShowWirelessSigning(true);
  };

  const handleWirelessSignatureReceived = (signatureData: string) => {
    if (wirelessSigningDocument) {
      // Update document with signature
      setDocuments(prev => prev.map(doc =>
        doc.id === wirelessSigningDocument.id
          ? {
              ...doc,
              status: 'signed' as const,
              signedBy: [...(doc.signedBy || []), 'Remote Device'],
              lastModified: new Date().toISOString().split('T')[0]
            }
          : doc
      ));

      // Close wireless signing modal
      setShowWirelessSigning(false);
      setWirelessSigningDocument(null);
      setSignatureAreaId('');

      alert('Document signed successfully via wireless device!');
    }
  };

  // Template Management Functions
  const handleTemplateUpload = () => {
    templateFileInputRef.current?.click();
  };

  const handleTemplateFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      // In a real implementation, you would upload the file to your server
      // and extract template variables using AI/OCR
      const newTemplate: DocumentTemplate = {
        id: `template-${Date.now()}`,
        name: file.name.replace(/\.[^/.]+$/, ''), // Remove file extension
        description: 'Uploaded template document',
        type: 'other',
        fileUrl: URL.createObjectURL(file),
        variables: [
          { name: 'residentName', label: 'Resident Name', type: 'resident', required: true, placeholder: 'Select resident' },
          { name: 'date', label: 'Date', type: 'date', required: true, placeholder: 'Select date' },
          { name: 'staffMember', label: 'Staff Member', type: 'staff', required: true, placeholder: 'Select staff member' }
        ],
        createdDate: new Date().toISOString().split('T')[0],
        usageCount: 0,
        isActive: true
      };

      setTemplates(prev => [...prev, newTemplate]);
      alert('Template uploaded successfully! You can now customize the variables.');
    } catch (error) {
      alert('Failed to upload template');
    }
  };

  const handleGenerateDocument = (template: DocumentTemplate) => {
    setSelectedTemplate(template);
    setTemplateVariables({});
    setShowGenerateModal(true);
  };

  const generateDocumentFromTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      // Validate required fields
      const missingFields = selectedTemplate.variables
        .filter(variable => variable.required && !templateVariables[variable.name])
        .map(variable => variable.label);

      if (missingFields.length > 0) {
        alert(`Please fill in the following required fields: ${missingFields.join(', ')}`);
        return;
      }

      // Generate new document
      const newDocument: Document = {
        id: `doc-${Date.now()}`,
        name: `${selectedTemplate.name} - ${templateVariables.residentName || 'New Document'}`,
        type: selectedTemplate.type,
        residentId: templateVariables.residentName ? `R${Math.floor(Math.random() * 1000)}` : undefined,
        residentName: templateVariables.residentName,
        status: 'draft',
        createdDate: new Date().toISOString().split('T')[0],
        lastModified: new Date().toISOString().split('T')[0],
        requiredSignatures: ['Resident', 'Staff Member'],
        aiAnalysis: {
          documentType: selectedTemplate.name,
          confidence: 0.95,
          extractedData: templateVariables,
          compliance: true
        }
      };

      setDocuments(prev => [newDocument, ...prev]);

      // Update template usage
      setTemplates(prev => prev.map(template =>
        template.id === selectedTemplate.id
          ? {
              ...template,
              usageCount: template.usageCount + 1,
              lastUsed: new Date().toISOString().split('T')[0]
            }
          : template
      ));

      setShowGenerateModal(false);
      setSelectedTemplate(null);
      setTemplateVariables({});
      setActiveTab('documents');

      alert('Document generated successfully!');
    } catch (error) {
      alert('Failed to generate document');
    }
  };

  const toggleTemplateStatus = (templateId: string) => {
    setTemplates(prev => prev.map(template =>
      template.id === templateId
        ? { ...template, isActive: !template.isActive }
        : template
    ));
  };

  const deleteTemplate = (templateId: string) => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      setTemplates(prev => prev.filter(template => template.id !== templateId));
    }
  };

  // Document Creation Functions
  const handleCreateNew = () => {
    setNewDocument({
      name: '',
      type: 'other',
      residentId: '',
      residentName: '',
      description: '',
      category: 'general'
    });
    setShowCreateModal(true);
  };

  const createDocument = () => {
    if (!newDocument.name.trim()) {
      alert('Please enter a document name');
      return;
    }

    const document: Document = {
      id: `doc-${Date.now()}`,
      name: newDocument.name,
      type: newDocument.type,
      residentId: newDocument.residentId || undefined,
      residentName: newDocument.residentName || undefined,
      status: 'draft',
      createdDate: new Date().toISOString().split('T')[0],
      lastModified: new Date().toISOString().split('T')[0],
      requiredSignatures: ['Staff Member'],
      aiAnalysis: {
        documentType: newDocument.type,
        confidence: 0.85,
        extractedData: {
          category: newDocument.category,
          description: newDocument.description
        },
        compliance: true
      }
    };

    setDocuments(prev => [document, ...prev]);
    setShowCreateModal(false);
    alert('Document created successfully!');
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesType = selectedType === 'all' || doc.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || doc.status === selectedStatus;
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (doc.residentName && doc.residentName.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesType && matchesStatus && matchesSearch;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Document Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          AI-Enhanced document scanning, e-signing, and management for residential care
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            📄 Smart Document Processing
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            ✍️ Digital Signatures
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            🤖 AI Document Analysis
          </span>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('documents')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'documents'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <DocumentTextIcon className="h-5 w-5 inline mr-2" />
              Documents
            </button>
            <button
              onClick={() => setActiveTab('templates')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'templates'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <DocumentDuplicateIcon className="h-5 w-5 inline mr-2" />
              Templates
            </button>
            <button
              onClick={() => setActiveTab('generate')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'generate'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <SparklesIcon className="h-5 w-5 inline mr-2" />
              Auto-Generate
            </button>
            <button
              onClick={() => setActiveTab('inbound-faxes')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'inbound-faxes'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <InboxIcon className="h-5 w-5 inline mr-2" />
              Inbound Faxes
            </button>
          </nav>
        </div>
      </div>

      {/* Documents Tab Content */}
      {activeTab === 'documents' && (
        <>
          {/* Action Bar */}
          <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => fileInputRef.current?.click()}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Upload Document
            </button>
            
            <button
              onClick={() => cameraInputRef.current?.click()}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <CameraIcon className="h-4 w-4 mr-2" />
              Scan Document
            </button>
            
            <button
              onClick={handleCreateNew}
              className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create New
            </button>


          </div>
          
          <div className="text-sm text-gray-600">
            <span className="font-medium">{filteredDocuments.length}</span> documents
          </div>
        </div>

        {/* Hidden file inputs */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          onChange={handleFileUpload}
          className="hidden"
        />
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleCameraCapture}
          className="hidden"
        />
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
              Search Documents
            </label>
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name or resident..."
                className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
              Document Type
            </label>
            <select
              id="type"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              {documentTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              id="status"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Documents List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Documents</h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredDocuments.map((document) => (
            <div key={document.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(document.status)}
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">{document.name}</h4>
                      {document.residentName && (
                        <p className="text-sm text-gray-600">Resident: {document.residentName}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-4 flex flex-wrap gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(document.type)}`}>
                      {document.type.replace('_', ' ').toUpperCase()}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(document.status)}`}>
                      {document.status.replace('_', ' ').toUpperCase()}
                    </span>
                    {document.aiAnalysis && (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
                        AI Confidence: {Math.round(document.aiAnalysis.confidence * 100)}%
                      </span>
                    )}
                  </div>

                  <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Created:</span> {document.createdDate}
                    </div>
                    <div>
                      <span className="font-medium">Last Modified:</span> {document.lastModified}
                    </div>
                    {document.signedBy && document.signedBy.length > 0 && (
                      <div className="sm:col-span-2">
                        <span className="font-medium">Signed by:</span> {document.signedBy.join(', ')}
                      </div>
                    )}
                  </div>

                  {/* AI Analysis */}
                  {document.aiAnalysis && (
                    <div className="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-md">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <span className="text-purple-600">🤖</span>
                        </div>
                        <div className="ml-3">
                          <h5 className="text-sm font-medium text-purple-800">AI Analysis</h5>
                          <p className="mt-1 text-sm text-purple-700">
                            Detected: {document.aiAnalysis.documentType} 
                            {!document.aiAnalysis.compliance && (
                              <span className="ml-2 text-red-600">⚠️ Compliance issues detected</span>
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="ml-6 flex flex-col space-y-2">
                  <button
                    onClick={() => {
                      if (document.fileUrl) {
                        window.open(document.fileUrl, '_blank');
                      } else {
                        alert('No file available for viewing.');
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View
                  </button>

                  <button
                    onClick={() => {
                      if (document.fileUrl) {
                        const link = window.document.createElement('a');
                        link.href = document.fileUrl;
                        link.download = document.name || 'document';
                        window.document.body.appendChild(link);
                        link.click();
                        window.document.body.removeChild(link);
                      } else {
                        alert('No file available for download.');
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Download
                  </button>

                  <button
                    onClick={() => {
                      if (document.fileUrl) {
                        const printWindow = window.open(document.fileUrl, '_blank');
                        printWindow?.addEventListener('load', () => printWindow.print());
                      } else {
                        alert('No file available to print.');
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <DocumentTextIcon className="h-4 w-4 mr-2" />
                    Print
                  </button>

                  <button
                    onClick={() => {
                      if (document.fileUrl) {
                        window.location.href = `mailto:?subject=Document: ${encodeURIComponent(document.name)}&body=Please see the attached document.\n${encodeURIComponent(document.fileUrl)}`;
                      } else {
                        alert('No file available to email.');
                      }
                    }}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                    Email
                  </button>

                  <button
                    onClick={() => handleFax(document)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <PhoneIcon className="h-4 w-4 mr-2" />
                    Fax
                  </button>

                  {document.status === 'pending_signature' && (
                    <>
                      <button
                        onClick={() => handleESign(document)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                      >
                        <PencilIcon className="h-4 w-4 mr-2" />
                        E-Sign
                      </button>

                      <button
                        onClick={() => handleWirelessSigning(document)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <DevicePhoneMobileIcon className="h-4 w-4 mr-2" />
                        Wireless Sign
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* E-Signature Modal */}
      {showESignModal && selectedDocument && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowESignModal(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Electronic Signature Required
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Document: {selectedDocument.name}
                </p>
                
                {/* Signature Canvas */}
                <SignatureCanvas
                  onSignatureChange={setSignature}
                  width={400}
                  height={200}
                />
              </div>
              
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    if (!signature) {
                      alert('Please provide your signature first.');
                      return;
                    }

                    // Update document status
                    setDocuments(prev => prev.map(doc =>
                      doc.id === selectedDocument.id
                        ? { ...doc, status: 'signed' as const, signedBy: ['Current User'] }
                        : doc
                    ));

                    alert('Document signed successfully!');
                    setShowESignModal(false);
                    setSignature(null);
                  }}
                  disabled={!signature}
                  className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    signature
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  Sign Document
                </button>
                <button
                  onClick={() => {
                    setShowESignModal(false);
                    setSignature(null);
                  }}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Fax Modal */}
      {showFaxModal && selectedFaxDocument && (
        <EnhancedFaxModal
          isOpen={showFaxModal}
          onClose={() => setShowFaxModal(false)}
          document={selectedFaxDocument}
          recipients={faxRecipients}
          onSendFax={sendFax}
        />
      )}
        </>
      )}

      {/* Templates Tab Content */}
      {activeTab === 'templates' && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-lg font-medium text-gray-900">Document Templates</h2>
              <p className="text-sm text-gray-600">Manage templates for auto-generating documents</p>
            </div>
            <button
              onClick={handleTemplateUpload}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
            >
              <CloudArrowUpIcon className="h-4 w-4 mr-2" />
              Upload Template
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.map((template) => (
              <div key={template.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-900">{template.name}</h3>
                    <p className="text-xs text-gray-500 mt-1">{template.description}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => toggleTemplateStatus(template.id)}
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        template.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {template.isActive ? 'Active' : 'Inactive'}
                    </button>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Type: {template.type.replace('_', ' ')}</span>
                    <span>Used: {template.usageCount} times</span>
                  </div>
                  <div className="text-xs text-gray-500">
                    Variables: {template.variables.length}
                  </div>
                  {template.lastUsed && (
                    <div className="text-xs text-gray-500">
                      Last used: {template.lastUsed}
                    </div>
                  )}
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handleGenerateDocument(template)}
                    disabled={!template.isActive}
                    className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <SparklesIcon className="h-3 w-3 mr-1" />
                    Generate
                  </button>
                  <button
                    onClick={() => deleteTemplate(template.id)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>

          {templates.length === 0 && (
            <div className="text-center py-12">
              <DocumentDuplicateIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No templates</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by uploading your first template.</p>
              <div className="mt-6">
                <button
                  onClick={handleTemplateUpload}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
                >
                  <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                  Upload Template
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Auto-Generate Tab Content */}
      {activeTab === 'generate' && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-900">Auto-Generate Documents</h2>
            <p className="text-sm text-gray-600">Select a template and fill in the details to generate a new document</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.filter(template => template.isActive).map((template) => (
              <div key={template.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                   onClick={() => handleGenerateDocument(template)}>
                <div className="flex items-center mb-3">
                  <div className="flex-shrink-0">
                    <DocumentDuplicateIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-gray-900">{template.name}</h3>
                    <p className="text-xs text-gray-500">{template.description}</p>
                  </div>
                </div>

                <div className="space-y-1 mb-4">
                  <div className="text-xs text-gray-500">
                    {template.variables.length} variables to fill
                  </div>
                  <div className="text-xs text-gray-500">
                    Used {template.usageCount} times
                  </div>
                </div>

                <button className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                  <SparklesIcon className="h-4 w-4 mr-2" />
                  Generate Document
                </button>
              </div>
            ))}
          </div>

          {templates.filter(template => template.isActive).length === 0 && (
            <div className="text-center py-12">
              <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No active templates</h3>
              <p className="mt-1 text-sm text-gray-500">Upload and activate templates to start auto-generating documents.</p>
              <div className="mt-6">
                <button
                  onClick={() => setActiveTab('templates')}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700"
                >
                  <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
                  Manage Templates
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Hidden file inputs */}
      <input
        type="file"
        ref={templateFileInputRef}
        onChange={handleTemplateFileChange}
        accept=".pdf,.doc,.docx"
        className="hidden"
      />

      {/* Wireless Signing Modal */}
      {showWirelessSigning && wirelessSigningDocument && (
        <WirelessSigningManager
          documentId={wirelessSigningDocument.id}
          signatureAreaId={signatureAreaId}
          onSignatureReceived={handleWirelessSignatureReceived}
          onClose={() => {
            setShowWirelessSigning(false);
            setWirelessSigningDocument(null);
            setSignatureAreaId('');
          }}
        />
      )}

      {/* Generate Document Modal */}
      {showGenerateModal && selectedTemplate && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowGenerateModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                    <SparklesIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Generate Document
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Fill in the template variables to generate: {selectedTemplate.name}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-4">
                  {selectedTemplate.variables.map((variable) => (
                    <div key={variable.name}>
                      <label className="block text-sm font-medium text-gray-700">
                        {variable.label}
                        {variable.required && <span className="text-red-500 ml-1">*</span>}
                      </label>

                      {variable.type === 'select' ? (
                        <select
                          value={templateVariables[variable.name] || ''}
                          onChange={(e) => setTemplateVariables(prev => ({
                            ...prev,
                            [variable.name]: e.target.value
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="">{variable.placeholder}</option>
                          {variable.options?.map((option) => (
                            <option key={option} value={option}>{option}</option>
                          ))}
                        </select>
                      ) : variable.type === 'date' ? (
                        <input
                          type="date"
                          value={templateVariables[variable.name] || ''}
                          onChange={(e) => setTemplateVariables(prev => ({
                            ...prev,
                            [variable.name]: e.target.value
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      ) : variable.type === 'resident' ? (
                        <select
                          value={templateVariables[variable.name] || ''}
                          onChange={(e) => setTemplateVariables(prev => ({
                            ...prev,
                            [variable.name]: e.target.value
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="">{variable.placeholder}</option>
                          <option value="John Smith">John Smith</option>
                          <option value="Mary Johnson">Mary Johnson</option>
                          <option value="Robert Brown">Robert Brown</option>
                          <option value="Dorothy Garcia">Dorothy Garcia</option>
                        </select>
                      ) : variable.type === 'staff' ? (
                        <select
                          value={templateVariables[variable.name] || ''}
                          onChange={(e) => setTemplateVariables(prev => ({
                            ...prev,
                            [variable.name]: e.target.value
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="">{variable.placeholder}</option>
                          <option value="Dr. Sarah Wilson">Dr. Sarah Wilson</option>
                          <option value="Nurse Jennifer Davis">Nurse Jennifer Davis</option>
                          <option value="Care Coordinator Mike Chen">Care Coordinator Mike Chen</option>
                        </select>
                      ) : (
                        <input
                          type={variable.type === 'number' ? 'number' : 'text'}
                          value={templateVariables[variable.name] || ''}
                          onChange={(e) => setTemplateVariables(prev => ({
                            ...prev,
                            [variable.name]: e.target.value
                          }))}
                          placeholder={variable.placeholder}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={generateDocumentFromTemplate}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  <SparklesIcon className="h-4 w-4 mr-2" />
                  Generate Document
                </button>
                <button
                  onClick={() => setShowGenerateModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create Document Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowCreateModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 sm:mx-0 sm:h-10 sm:w-10">
                    <PlusIcon className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Create New Document
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Create a new document for your residential care facility
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Document Name *
                    </label>
                    <input
                      type="text"
                      value={newDocument.name}
                      onChange={(e) => setNewDocument(prev => ({ ...prev, name: e.target.value }))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                      placeholder="Enter document name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Document Type
                    </label>
                    <select
                      value={newDocument.type}
                      onChange={(e) => setNewDocument(prev => ({ ...prev, type: e.target.value as Document['type'] }))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                    >
                      <option value="admission">Admission Agreement</option>
                      <option value="care_plan">Care Plan</option>
                      <option value="medication_consent">Medication Consent</option>
                      <option value="incident_report">Incident Report</option>
                      <option value="insurance">Insurance Document</option>
                      <option value="assessment">Assessment Form</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Resident (Optional)
                      </label>
                      <select
                        value={newDocument.residentName}
                        onChange={(e) => {
                          const selectedName = e.target.value;
                          setNewDocument(prev => ({
                            ...prev,
                            residentName: selectedName,
                            residentId: selectedName ? `R${Math.floor(Math.random() * 1000)}` : ''
                          }));
                        }}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                      >
                        <option value="">Select resident (optional)</option>
                        <option value="John Smith">John Smith</option>
                        <option value="Mary Johnson">Mary Johnson</option>
                        <option value="Robert Brown">Robert Brown</option>
                        <option value="Dorothy Garcia">Dorothy Garcia</option>
                        <option value="Margaret Thompson">Margaret Thompson</option>
                        <option value="William Anderson">William Anderson</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Category
                      </label>
                      <select
                        value={newDocument.category}
                        onChange={(e) => setNewDocument(prev => ({ ...prev, category: e.target.value }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                      >
                        <option value="general">General</option>
                        <option value="medical">Medical</option>
                        <option value="administrative">Administrative</option>
                        <option value="legal">Legal</option>
                        <option value="financial">Financial</option>
                        <option value="emergency">Emergency</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Description (Optional)
                    </label>
                    <textarea
                      value={newDocument.description}
                      onChange={(e) => setNewDocument(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                      placeholder="Brief description of the document..."
                    />
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center">
                      <DocumentTextIcon className="h-5 w-5 text-blue-600 mr-2" />
                      <span className="text-sm font-medium text-blue-800">Document Creation Options</span>
                    </div>
                    <div className="mt-2 text-xs text-blue-700">
                      <p>• Document will be created in draft status</p>
                      <p>• You can upload content after creation</p>
                      <p>• AI analysis will be performed automatically</p>
                      <p>• Digital signatures can be added as needed</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={createDocument}
                  disabled={!newDocument.name.trim()}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create Document
                </button>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Inbound Faxes Tab Content */}
      {activeTab === 'inbound-faxes' && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900">Inbound Faxes</h3>
            <p className="text-sm text-gray-500">Received fax documents and communications</p>
          </div>

          <div className="space-y-4">
            {/* Mock inbound fax data */}
            {[
              {
                id: 'fax-001',
                from: 'Springfield Medical Center',
                fromNumber: '******-0199',
                subject: 'Lab Results - John Smith',
                receivedDate: '2024-01-15',
                receivedTime: '10:30 AM',
                pages: 3,
                status: 'new'
              },
              {
                id: 'fax-002',
                from: 'Insurance Provider',
                fromNumber: '******-0288',
                subject: 'Authorization Request',
                receivedDate: '2024-01-15',
                receivedTime: '09:15 AM',
                pages: 2,
                status: 'read'
              },
              {
                id: 'fax-003',
                from: 'Pharmacy Services',
                fromNumber: '******-0377',
                subject: 'Medication Delivery Confirmation',
                receivedDate: '2024-01-14',
                receivedTime: '04:45 PM',
                pages: 1,
                status: 'processed'
              }
            ].map((fax) => (
              <div key={fax.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className={`w-3 h-3 rounded-full ${
                        fax.status === 'new' ? 'bg-green-400' :
                        fax.status === 'read' ? 'bg-yellow-400' :
                        'bg-gray-400'
                      }`} />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{fax.subject}</h4>
                      <p className="text-sm text-gray-500">From: {fax.from} ({fax.fromNumber})</p>
                      <p className="text-xs text-gray-400">
                        Received: {fax.receivedDate} at {fax.receivedTime} • {fax.pages} page{fax.pages > 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      fax.status === 'new' ? 'bg-green-100 text-green-800' :
                      fax.status === 'read' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {fax.status.charAt(0).toUpperCase() + fax.status.slice(1)}
                    </span>
                    <div className="flex space-x-1">
                      <button
                        onClick={() => alert(`Viewing fax: ${fax.subject}`)}
                        className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <EyeIcon className="h-3 w-3 mr-1" />
                        View
                      </button>
                      <button
                        onClick={() => alert(`Downloading fax: ${fax.subject}`)}
                        className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <ArrowDownTrayIcon className="h-3 w-3 mr-1" />
                        Download
                      </button>
                      <button
                        onClick={() => alert(`Processing fax: ${fax.subject}`)}
                        className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <CheckCircleIcon className="h-3 w-3 mr-1" />
                        Process
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Empty state if no faxes */}
          <div className="text-center py-12">
            <InboxIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No recent faxes</h3>
            <p className="mt-1 text-sm text-gray-500">
              Inbound faxes will appear here when received.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Documents;
