import React, { useState, useRef } from 'react';
import {
  DocumentTextIcon,
  CameraIcon,
  PencilIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  PhoneIcon,
  PaperAirplaneIcon,
  InboxIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import SignatureCanvas from '../components/SignatureCanvas';
import { documentService } from '../services/documentService';
import { faxService, FaxRecipient } from '../services/faxService';

interface Document {
  id: string;
  name: string;
  type: 'admission' | 'care_plan' | 'medication_consent' | 'incident_report' | 'insurance' | 'assessment' | 'other';
  residentId?: string;
  residentName?: string;
  status: 'draft' | 'pending_signature' | 'signed' | 'expired';
  createdDate: string;
  lastModified: string;
  signedBy?: string[];
  requiredSignatures?: string[];
  fileUrl?: string;
  thumbnailUrl?: string;
  aiAnalysis?: {
    documentType: string;
    confidence: number;
    extractedData: any;
    compliance: boolean;
  };
}

const Documents: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: '1',
      name: 'Admission Agreement - <PERSON>',
      type: 'admission',
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      status: 'signed',
      createdDate: '2024-01-15',
      lastModified: '2024-01-15',
      signedBy: ['Margaret Thompson', 'John Thompson (Son)', 'Sarah Johnson (Administrator)'],
      requiredSignatures: ['Resident', 'Family Member', 'Administrator'],
      aiAnalysis: {
        documentType: 'Admission Agreement',
        confidence: 0.98,
        extractedData: { residentName: 'Margaret Thompson', admissionDate: '2024-01-20' },
        compliance: true
      }
    },
    {
      id: '2',
      name: 'Medication Consent Form - William Anderson',
      type: 'medication_consent',
      residentId: 'R002',
      residentName: 'William Anderson',
      status: 'pending_signature',
      createdDate: '2024-01-18',
      lastModified: '2024-01-18',
      signedBy: ['Dr. Smith'],
      requiredSignatures: ['Resident', 'Doctor', 'Nurse'],
      aiAnalysis: {
        documentType: 'Medication Consent',
        confidence: 0.95,
        extractedData: { medications: ['Lisinopril 10mg', 'Metformin 500mg'] },
        compliance: true
      }
    },
    {
      id: '3',
      name: 'Monthly Care Assessment - Dorothy Garcia',
      type: 'assessment',
      residentId: 'R003',
      residentName: 'Dorothy Garcia',
      status: 'draft',
      createdDate: '2024-01-20',
      lastModified: '2024-01-20',
      requiredSignatures: ['Nurse', 'Care Coordinator'],
      aiAnalysis: {
        documentType: 'Care Assessment',
        confidence: 0.92,
        extractedData: { assessmentDate: '2024-01-20', careLevel: 'Moderate Assistance' },
        compliance: false
      }
    }
  ]);

  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showScanner, setShowScanner] = useState(false);
  const [showESignModal, setShowESignModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [signature, setSignature] = useState<string | null>(null);
  const [showFaxModal, setShowFaxModal] = useState(false);
  const [faxRecipients, setFaxRecipients] = useState<FaxRecipient[]>([]);
  const [selectedFaxDocument, setSelectedFaxDocument] = useState<Document | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);

  const documentTypes = [
    { value: 'all', label: 'All Documents' },
    { value: 'admission', label: 'Admission Forms' },
    { value: 'care_plan', label: 'Care Plans' },
    { value: 'medication_consent', label: 'Medication Consent' },
    { value: 'incident_report', label: 'Incident Reports' },
    { value: 'insurance', label: 'Insurance Documents' },
    { value: 'assessment', label: 'Assessments' },
    { value: 'other', label: 'Other' }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'draft', label: 'Draft' },
    { value: 'pending_signature', label: 'Pending Signature' },
    { value: 'signed', label: 'Signed' },
    { value: 'expired', label: 'Expired' }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'signed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'pending_signature':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'expired':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <DocumentTextIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'signed':
        return 'bg-green-100 text-green-800';
      case 'pending_signature':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'admission':
        return 'bg-blue-100 text-blue-800';
      case 'care_plan':
        return 'bg-green-100 text-green-800';
      case 'medication_consent':
        return 'bg-purple-100 text-purple-800';
      case 'incident_report':
        return 'bg-red-100 text-red-800';
      case 'insurance':
        return 'bg-indigo-100 text-indigo-800';
      case 'assessment':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      try {
        const file = files[0];
        const result = await documentService.scanDocument(file);

        // Add the processed document to the list
        const newDocument: Document = {
          id: result.id,
          name: result.originalFileName,
          type: result.documentType.toLowerCase().replace(' ', '_') as any,
          status: 'draft',
          createdDate: new Date().toISOString().split('T')[0],
          lastModified: new Date().toISOString().split('T')[0],
          requiredSignatures: ['Administrator'],
          aiAnalysis: {
            documentType: result.documentType,
            confidence: result.confidence,
            extractedData: result.extractedData,
            compliance: result.complianceCheck.isCompliant
          }
        };

        setDocuments(prev => [newDocument, ...prev]);
        alert(`Document processed successfully! AI detected: ${result.documentType} with ${Math.round(result.confidence * 100)}% confidence`);
      } catch (error) {
        alert('Failed to process document. Please try again.');
      }
    }
  };

  const handleCameraCapture = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      try {
        const file = files[0];
        const result = await documentService.scanDocument(file);

        const newDocument: Document = {
          id: result.id,
          name: `Scanned - ${result.documentType}`,
          type: result.documentType.toLowerCase().replace(' ', '_') as any,
          status: 'draft',
          createdDate: new Date().toISOString().split('T')[0],
          lastModified: new Date().toISOString().split('T')[0],
          requiredSignatures: ['Staff Member'],
          aiAnalysis: {
            documentType: result.documentType,
            confidence: result.confidence,
            extractedData: result.extractedData,
            compliance: result.complianceCheck.isCompliant
          }
        };

        setDocuments(prev => [newDocument, ...prev]);
        alert(`Document scanned successfully! AI detected: ${result.documentType}`);
      } catch (error) {
        alert('Failed to scan document. Please try again.');
      }
    }
  };

  const handleESign = (document: Document) => {
    setSelectedDocument(document);
    setShowESignModal(true);
  };

  const handleFax = async (document: Document) => {
    setSelectedFaxDocument(document);
    try {
      const recipients = await faxService.getCommonRecipients();
      setFaxRecipients(recipients);
      setShowFaxModal(true);
    } catch (error) {
      alert('Failed to load fax recipients');
    }
  };

  const sendFax = async (recipients: FaxRecipient[], coverPageData: any) => {
    if (!selectedFaxDocument) return;

    try {
      const faxRequest = {
        recipients,
        documents: [{
          documentId: selectedFaxDocument.id,
          documentName: selectedFaxDocument.name,
          documentType: selectedFaxDocument.type,
          pages: 1,
          fileUrl: '/mock-document.pdf'
        }],
        coverPage: {
          enabled: true,
          subject: `${selectedFaxDocument.name} - ${selectedFaxDocument.residentName}`,
          message: coverPageData.message || 'Please find attached document for your review.',
          urgent: coverPageData.urgent || false,
          confidential: true
        },
        priority: (coverPageData.urgent ? 'urgent' : 'normal') as 'normal' | 'high' | 'urgent',
        residentId: selectedFaxDocument.residentId,
        residentName: selectedFaxDocument.residentName,
        senderInfo: {
          name: 'CareSyncAI Residential Care',
          organization: 'CareSyncAI Care Facility',
          faxNumber: '******-0100',
          phone: '******-0101',
          email: '<EMAIL>'
        }
      };

      const result = await faxService.sendFax(faxRequest);
      alert(`Fax sent successfully! Fax ID: ${result.faxId}`);
      setShowFaxModal(false);
    } catch (error) {
      alert('Failed to send fax. Please try again.');
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesType = selectedType === 'all' || doc.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || doc.status === selectedStatus;
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (doc.residentName && doc.residentName.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesType && matchesStatus && matchesSearch;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Document Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          AI-Enhanced document scanning, e-signing, and management for residential care
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            📄 Smart Document Processing
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            ✍️ Digital Signatures
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            🤖 AI Document Analysis
          </span>
        </div>
      </div>

      {/* Action Bar */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => fileInputRef.current?.click()}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Upload Document
            </button>
            
            <button
              onClick={() => cameraInputRef.current?.click()}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <CameraIcon className="h-4 w-4 mr-2" />
              Scan Document
            </button>
            
            <button className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500">
              <PlusIcon className="h-4 w-4 mr-2" />
              Create New
            </button>

            <button className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              <InboxIcon className="h-4 w-4 mr-2" />
              Inbound Faxes
            </button>
          </div>
          
          <div className="text-sm text-gray-600">
            <span className="font-medium">{filteredDocuments.length}</span> documents
          </div>
        </div>

        {/* Hidden file inputs */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          onChange={handleFileUpload}
          className="hidden"
        />
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleCameraCapture}
          className="hidden"
        />
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
              Search Documents
            </label>
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name or resident..."
                className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
              Document Type
            </label>
            <select
              id="type"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              {documentTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              id="status"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Documents List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Documents</h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredDocuments.map((document) => (
            <div key={document.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(document.status)}
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">{document.name}</h4>
                      {document.residentName && (
                        <p className="text-sm text-gray-600">Resident: {document.residentName}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-4 flex flex-wrap gap-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(document.type)}`}>
                      {document.type.replace('_', ' ').toUpperCase()}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(document.status)}`}>
                      {document.status.replace('_', ' ').toUpperCase()}
                    </span>
                    {document.aiAnalysis && (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
                        AI Confidence: {Math.round(document.aiAnalysis.confidence * 100)}%
                      </span>
                    )}
                  </div>

                  <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Created:</span> {document.createdDate}
                    </div>
                    <div>
                      <span className="font-medium">Last Modified:</span> {document.lastModified}
                    </div>
                    {document.signedBy && document.signedBy.length > 0 && (
                      <div className="sm:col-span-2">
                        <span className="font-medium">Signed by:</span> {document.signedBy.join(', ')}
                      </div>
                    )}
                  </div>

                  {/* AI Analysis */}
                  {document.aiAnalysis && (
                    <div className="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-md">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <span className="text-purple-600">🤖</span>
                        </div>
                        <div className="ml-3">
                          <h5 className="text-sm font-medium text-purple-800">AI Analysis</h5>
                          <p className="mt-1 text-sm text-purple-700">
                            Detected: {document.aiAnalysis.documentType} 
                            {!document.aiAnalysis.compliance && (
                              <span className="ml-2 text-red-600">⚠️ Compliance issues detected</span>
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="ml-6 flex flex-col space-y-2">
                  <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View
                  </button>
                  
                  {document.status === 'pending_signature' && (
                    <button
                      onClick={() => handleESign(document)}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <PencilIcon className="h-4 w-4 mr-2" />
                      E-Sign
                    </button>
                  )}
                  
                  <button
                    onClick={() => handleFax(document)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <PhoneIcon className="h-4 w-4 mr-2" />
                    Fax
                  </button>

                  <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Download
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* E-Signature Modal */}
      {showESignModal && selectedDocument && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowESignModal(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Electronic Signature Required
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Document: {selectedDocument.name}
                </p>
                
                {/* Signature Canvas */}
                <SignatureCanvas
                  onSignatureChange={setSignature}
                  width={400}
                  height={200}
                />
              </div>
              
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    if (!signature) {
                      alert('Please provide your signature first.');
                      return;
                    }

                    // Update document status
                    setDocuments(prev => prev.map(doc =>
                      doc.id === selectedDocument.id
                        ? { ...doc, status: 'signed' as const, signedBy: ['Current User'] }
                        : doc
                    ));

                    alert('Document signed successfully!');
                    setShowESignModal(false);
                    setSignature(null);
                  }}
                  disabled={!signature}
                  className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium focus:outline-none focus:ring-2 sm:ml-3 sm:w-auto sm:text-sm ${
                    signature
                      ? 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  Sign Document
                </button>
                <button
                  onClick={() => {
                    setShowESignModal(false);
                    setSignature(null);
                  }}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Fax Modal */}
      {showFaxModal && selectedFaxDocument && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowFaxModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center mb-4">
                  <PhoneIcon className="h-6 w-6 text-indigo-600 mr-3" />
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Send Fax
                  </h3>
                </div>

                <div className="mb-4 p-3 bg-gray-50 rounded-md">
                  <p className="text-sm text-gray-600">
                    <strong>Document:</strong> {selectedFaxDocument.name}
                  </p>
                  {selectedFaxDocument.residentName && (
                    <p className="text-sm text-gray-600">
                      <strong>Resident:</strong> {selectedFaxDocument.residentName}
                    </p>
                  )}
                </div>

                {/* Recipients */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Recipients
                  </label>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {faxRecipients.map((recipient, index) => (
                      <label key={index} className="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <div className="ml-3 flex-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-900">{recipient.name}</span>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              recipient.type === 'doctor' ? 'bg-blue-100 text-blue-800' :
                              recipient.type === 'hospital' ? 'bg-red-100 text-red-800' :
                              recipient.type === 'pharmacy' ? 'bg-green-100 text-green-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {recipient.type.toUpperCase()}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500">
                            {recipient.organization} • {recipient.faxNumber}
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Cover Page */}
                <div className="mb-4">
                  <label className="flex items-center mb-2">
                    <input
                      type="checkbox"
                      defaultChecked
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm font-medium text-gray-700">Include cover page</span>
                  </label>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Subject</label>
                      <input
                        type="text"
                        defaultValue={`${selectedFaxDocument.name} - ${selectedFaxDocument.residentName}`}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Message</label>
                      <textarea
                        rows={3}
                        defaultValue="Please find attached document for your review. If you have any questions, please contact us."
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>

                    <div className="flex items-center space-x-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Mark as urgent</span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Confidential</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    // Get selected recipients and form data
                    const selectedRecipients = faxRecipients.slice(0, 1); // Mock: select first recipient
                    const coverPageData = { message: 'Please find attached document for your review.', urgent: false };
                    sendFax(selectedRecipients, coverPageData);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                  Send Fax
                </button>
                <button
                  onClick={() => setShowFaxModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Documents;
