import React from 'react';

const Billing: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Billing</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage invoices and payments with Zoho Books integration
        </p>
      </div>

      <div className="card">
        <div className="card-body">
          <div className="text-center py-12">
            <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-gray-400 text-xl">💰</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Integrated Billing System
            </h3>
            <p className="text-gray-600 mb-4">
              Seamless billing with Zoho Books integration and AI insights.
            </p>
            <p className="text-sm text-gray-500">
              Features: Invoice generation, payment tracking, financial reports,
              insurance claims, and automated billing workflows.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Billing;
