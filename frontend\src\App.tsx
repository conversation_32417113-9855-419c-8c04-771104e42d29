import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';

// Layout Components
import Layout from './components/Layout/Layout';
import RoleBasedLayout from './components/Layout/RoleBasedLayout';
import AuthLayout from './components/Layout/AuthLayout';
import RoleProtectedRoute from './components/Auth/RoleProtectedRoute';

// Page Components
import Dashboard from './pages/Dashboard';
import Patients from './pages/Patients';
import PatientDetail from './pages/PatientDetail';
import MedicalRecords from './pages/MedicalRecords';
import MedicalManagement from './pages/MedicalManagement';
import NutritionalManagement from './pages/NutritionalManagement';
import GroceryManagement from './pages/GroceryManagement';
import Inventory from './pages/Inventory';
import Billing from './pages/Billing';
import EMAR from './pages/EMAR';
import Documents from './pages/Documents';
import FaxManagement from './pages/FaxManagement';
import StaffManagement from './pages/StaffManagement';
import HIPAACompliance from './pages/HIPAACompliance';
import Purchasing from './pages/Purchasing';
import Scheduling from './pages/Scheduling';
import MobileSigningApp from './pages/MobileSigningApp';
import Login from './pages/Login';
import Profile from './pages/Profile';
import Visits from './pages/Visits';
import LandingPage from './pages/LandingPage';
import Signup from './pages/Signup';
import Pricing from './pages/Pricing';

// Context Providers
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SupabaseProvider } from './contexts/SupabaseContext';

// Debug Components (Development only)
import RoleSwitcher from './components/Debug/RoleSwitcher';
import PermissionDebugger from './components/Debug/PermissionDebugger';
import NotFound from './components/ErrorPages/NotFound';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner h-8 w-8"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Public Route Component (redirect if authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner h-8 w-8"></div>
      </div>
    );
  }

  if (user) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};



function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <SupabaseProvider>
        <AuthProvider>
          <Router>
            <div className="App">
              <Routes>
                {/* Landing Page - Public, accessible to everyone */}
                <Route
                  path="/"
                  element={<LandingPage />}
                />

                {/* Public Routes */}
                <Route
                  path="/login"
                  element={
                    <PublicRoute>
                      <AuthLayout>
                        <Login />
                      </AuthLayout>
                    </PublicRoute>
                  }
                />
                <Route
                  path="/signup"
                  element={
                    <PublicRoute>
                      <Signup />
                    </PublicRoute>
                  }
                />
                <Route
                  path="/pricing"
                  element={<Pricing />}
                />

                {/* Protected Routes */}
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="dashboard">
                        <RoleBasedLayout>
                          <Dashboard />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/patients"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="residents">
                        <RoleBasedLayout>
                          <Patients />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/residents"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="residents">
                        <RoleBasedLayout>
                          <Patients />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/patients/:id"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="residents">
                        <RoleBasedLayout>
                          <PatientDetail />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/residents/:id"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="residents">
                        <RoleBasedLayout>
                          <PatientDetail />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/medical-records"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        <MedicalRecords />
                      </Layout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/medical-management"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        <MedicalManagement />
                      </Layout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/nutrition-management"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        <NutritionalManagement />
                      </Layout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/grocery-management"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="purchasing">
                        <RoleBasedLayout>
                          <GroceryManagement />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/inventory"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="inventory">
                        <RoleBasedLayout>
                          <Inventory />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/purchasing"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="purchasing">
                        <RoleBasedLayout>
                          <Purchasing />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/scheduling"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="scheduling">
                        <RoleBasedLayout>
                          <Scheduling />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/billing"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="billing">
                        <RoleBasedLayout>
                          <Billing />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/fax-management"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="faxManagement">
                        <RoleBasedLayout>
                          <FaxManagement />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/fax"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="faxManagement">
                        <RoleBasedLayout>
                          <FaxManagement />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/medications"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="medications">
                        <RoleBasedLayout>
                          <EMAR />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/emar"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="medications">
                        <RoleBasedLayout>
                          <EMAR />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/documents"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        <Documents />
                      </Layout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/staff"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        <StaffManagement />
                      </Layout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/hipaa-compliance"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="hipaaCompliance">
                        <RoleBasedLayout>
                          <HIPAACompliance />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/hipaa"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="hipaaCompliance">
                        <RoleBasedLayout>
                          <HIPAACompliance />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/reports"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="reports">
                        <RoleBasedLayout>
                          <div className="p-6">
                            <h1 className="text-2xl font-bold text-gray-900 mb-4">Reports & Analytics</h1>
                            <p className="text-gray-600">Comprehensive reporting system coming soon...</p>
                          </div>
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/settings"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="systemSettings">
                        <RoleBasedLayout>
                          <div className="p-6">
                            <h1 className="text-2xl font-bold text-gray-900 mb-4">System Settings</h1>
                            <p className="text-gray-600">System configuration panel coming soon...</p>
                          </div>
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/profile"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        <Profile />
                      </Layout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/visits"
                  element={
                    <ProtectedRoute>
                      <RoleProtectedRoute requiredModule="residents">
                        <RoleBasedLayout>
                          <Visits />
                        </RoleBasedLayout>
                      </RoleProtectedRoute>
                    </ProtectedRoute>
                  }
                />

                {/* Mobile Signing App - No authentication required */}
                <Route path="/mobile-signing" element={<MobileSigningApp />} />

                {/* 404 fallback */}
                <Route path="*" element={<NotFound />} />
              </Routes>

              {/* Global Toast Notifications */}
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#363636',
                    color: '#fff',
                  },
                  success: {
                    style: {
                      background: '#22c55e',
                    },
                  },
                  error: {
                    style: {
                      background: '#ef4444',
                    },
                  },
                }}
              />

              {/* Development Tools */}
              <RoleSwitcher />
              <PermissionDebugger />
            </div>
          </Router>
        </AuthProvider>
      </SupabaseProvider>
    </QueryClientProvider>
  );
}

export default App;
