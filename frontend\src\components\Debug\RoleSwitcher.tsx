import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../utils/roleBasedAccess';
import {
  UserIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  CogIcon,
} from '@heroicons/react/24/outline';

const RoleSwitcher: React.FC = () => {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);

  // This is for development/testing only
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (!isDevelopment || !user) {
    return null;
  }

  const roles: { role: UserRole; title: string; icon: any; color: string; description: string }[] = [
    {
      role: 'admin',
      title: 'System Administrator',
      icon: ShieldCheckIcon,
      color: 'from-red-500 to-pink-600',
      description: 'Full system access and management'
    },
    {
      role: 'supervisor',
      title: 'Care Supervisor',
      icon: UserGroupIcon,
      color: 'from-purple-500 to-blue-600',
      description: 'Operational oversight and staff management'
    },
    {
      role: 'caregiver',
      title: 'Care Provider',
      icon: UserIcon,
      color: 'from-green-500 to-emerald-600',
      description: 'Direct patient care and documentation'
    },
    {
      role: 'billing',
      title: 'Billing Specialist',
      icon: CurrencyDollarIcon,
      color: 'from-amber-500 to-yellow-600',
      description: 'Financial management and billing'
    },
  ];

  const currentRole = user.role as UserRole;

  const switchRole = (newRole: UserRole) => {
    // In a real application, this would make an API call
    // For development, we'll just update the user object
    if (user) {
      (user as any).role = newRole;
      window.location.reload(); // Reload to apply changes
    }
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="group relative p-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full shadow-2xl hover:from-purple-500 hover:to-blue-500 transform hover:scale-110 transition-all duration-300 border-4 border-white"
      >
        <CogIcon className="h-6 w-6 group-hover:rotate-180 transition-transform duration-500" />
        <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-gradient-to-r from-red-500 to-pink-500 rounded-full">
          DEV
        </span>
      </button>

      {/* Role Switcher Panel */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 w-80 bg-white rounded-2xl shadow-2xl border-4 border-purple-200 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-4 text-white">
            <h3 className="text-lg font-bold">Development Role Switcher</h3>
            <p className="text-sm opacity-90">Current: {roles.find(r => r.role === currentRole)?.title}</p>
          </div>

          {/* Role Options */}
          <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
            {roles.map((roleOption) => {
              const IconComponent = roleOption.icon;
              const isActive = roleOption.role === currentRole;

              return (
                <button
                  key={roleOption.role}
                  onClick={() => switchRole(roleOption.role)}
                  disabled={isActive}
                  className={`w-full p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                    isActive
                      ? 'bg-gradient-to-r from-amber-100 to-yellow-100 border-amber-300 cursor-not-allowed'
                      : 'bg-gray-50 border-gray-200 hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 hover:border-purple-300 transform hover:scale-105'
                  }`}
                >
                  <div className="flex items-center">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${roleOption.color} shadow-lg ${isActive ? 'scale-110' : 'group-hover:scale-110'} transition-transform duration-300`}>
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="flex items-center">
                        <h4 className="text-lg font-bold text-gray-900">{roleOption.title}</h4>
                        {isActive && (
                          <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-green-500 to-emerald-500 text-white">
                            ACTIVE
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{roleOption.description}</p>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 p-4 border-t border-gray-200">
            <div className="flex items-center text-sm text-gray-600">
              <ShieldCheckIcon className="h-4 w-4 mr-2" />
              <span>Development mode only - Role switching for testing</span>
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 -z-10"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default RoleSwitcher;
