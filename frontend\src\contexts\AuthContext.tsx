import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';
import { useSupabase } from './SupabaseContext';
import { authService } from '../services/authService';
import { demoAuthService, isDemoMode } from '../services/demoAuthService';
import { isDemoAccount } from '../utils/demoAccounts';
import toast from 'react-hot-toast';

// Types
interface AuthUser extends User {
  caregiver_id?: string;
  role?: string;
  first_name?: string;
  last_name?: string;
}

interface SignupData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  facilityName: string;
  phone: string;
  role: string;
}

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (data: SignupData) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (data: any) => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const { supabase } = useSupabase();

  // Check if we're in mock mode (for future use)
  // const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';

  const loadUserProfile = useCallback(async (authUser: User) => {
    try {
      // Get caregiver profile
      const { data: caregiver, error } = await supabase
        .from('caregivers')
        .select('id, first_name, last_name, role, is_active')
        .eq('user_id', authUser.id)
        .single();

      if (error) {
        console.error('❌ Error loading caregiver profile:', error);

        // Fallback: Create a basic user profile with default caregiver role
        if (process.env.NODE_ENV === 'development') {
          console.log('🔄 Using fallback user profile...');
          const fallbackUser = {
            ...authUser,
            caregiver_id: 'fallback-id',
            role: 'caregiver',
            first_name: authUser.email?.split('@')[0] || 'User',
            last_name: 'Caregiver',
          };

          console.log('👤 Fallback User:', {
            id: fallbackUser.id,
            email: fallbackUser.email,
            role: fallbackUser.role,
            name: `${fallbackUser.first_name} ${fallbackUser.last_name}`,
          });

          setUser(fallbackUser);
        }
        return;
      }

      if (!caregiver.is_active) {
        toast.error('Your account is inactive. Please contact an administrator.');
        await logout();
        return;
      }

      const userData = {
        ...authUser,
        caregiver_id: caregiver.id,
        role: caregiver.role,
        first_name: caregiver.first_name,
        last_name: caregiver.last_name,
      };

      // Debug logging in development
      if (process.env.NODE_ENV === 'development') {
        console.log('🔐 User Profile Loaded:', {
          id: userData.id,
          email: userData.email,
          role: userData.role,
          name: `${userData.first_name} ${userData.last_name}`,
          caregiver_id: userData.caregiver_id,
        });
      }

      setUser(userData);
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  }, [supabase]);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔄 Getting initial session...');
        }

        // Check for demo session first
        const demoSession = demoAuthService.getCurrentSession();
        if (demoSession) {
          const demoUser: AuthUser = {
            id: demoSession.user.id,
            email: demoSession.user.email,
            role: demoSession.user.role,
            first_name: demoSession.user.first_name,
            last_name: demoSession.user.last_name,
            caregiver_id: demoSession.user.caregiver_id,
            // Required Supabase User properties
            aud: 'authenticated',
            created_at: new Date().toISOString(),
            app_metadata: { role: demoSession.user.role },
            user_metadata: {
              first_name: demoSession.user.first_name,
              last_name: demoSession.user.last_name,
              title: demoSession.user.title,
              department: demoSession.user.department
            },
            identities: [],
            factors: []
          };

          setUser(demoUser);

          if (process.env.NODE_ENV === 'development') {
            console.log('🎭 Demo session restored:', {
              user: `${demoSession.user.first_name} ${demoSession.user.last_name}`,
              role: demoSession.user.role
            });
          }

          setLoading(false);
          return;
        }

        // Regular session check
        const { data: { session } } = await supabase.auth.getSession();

        if (process.env.NODE_ENV === 'development') {
          console.log('📋 Session data:', session ? 'Session exists' : 'No session');
        }

        if (session?.user) {
          await loadUserProfile(session.user);
        }
      } catch (error) {
        console.error('❌ Error getting initial session:', error);
      } finally {
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ Auth loading complete');
        }
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          await loadUserProfile(session.user);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, [supabase.auth, loadUserProfile]);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);

      // Check if it's a demo account
      if (isDemoAccount(email)) {
        const demoResponse = await demoAuthService.login(email, password);

        if (demoResponse.success && demoResponse.user) {
          const demoUser: AuthUser = {
            id: demoResponse.user.id,
            email: demoResponse.user.email,
            role: demoResponse.user.role,
            first_name: demoResponse.user.first_name,
            last_name: demoResponse.user.last_name,
            caregiver_id: demoResponse.user.caregiver_id,
            // Required Supabase User properties
            aud: 'authenticated',
            created_at: new Date().toISOString(),
            app_metadata: { role: demoResponse.user.role },
            user_metadata: {
              first_name: demoResponse.user.first_name,
              last_name: demoResponse.user.last_name,
              title: demoResponse.user.title,
              department: demoResponse.user.department
            },
            identities: [],
            factors: []
          };

          setUser(demoUser);
          toast.success(`🎭 Demo Login: Welcome ${demoResponse.user.first_name}! (${demoResponse.user.title})`);
          return;
        } else {
          throw new Error(demoResponse.error || 'Demo login failed');
        }
      }

      // Regular authentication
      const response = await authService.login(email, password);

      // For mock authentication, we need to manually set the user
      const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
      if (isMockMode) {
        const mockUser: AuthUser = {
          id: response.user.id,
          email: response.user.email,
          role: response.user.role,
          first_name: response.user.first_name,
          last_name: response.user.last_name,
          // Required Supabase User properties
          aud: 'authenticated',
          created_at: new Date().toISOString(),
          app_metadata: {},
          user_metadata: {},
          identities: [],
          factors: []
        };
        setUser(mockUser);
      }

      toast.success(`Welcome back, ${response.user.first_name}!`);
    } catch (error: any) {
      toast.error(error.message || 'Login failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signup = async (data: SignupData) => {
    try {
      setLoading(true);

      // For demo/mock mode, create a mock user
      const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
      if (isMockMode) {
        // Simulate signup delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Create mock user
        const mockUser: AuthUser = {
          id: `user_${Date.now()}`,
          email: data.email,
          role: data.role,
          first_name: data.firstName,
          last_name: data.lastName,
          // Required Supabase User properties
          aud: 'authenticated',
          created_at: new Date().toISOString(),
          app_metadata: { role: data.role },
          user_metadata: {
            first_name: data.firstName,
            last_name: data.lastName,
            facility_name: data.facilityName,
            phone: data.phone
          },
          identities: [],
          factors: []
        };

        // In a real app, this would create the user in the database
        toast.success(`Account created successfully! Welcome ${data.firstName}!`);
        return;
      }

      // Real signup implementation would go here
      // For now, just simulate success
      toast.success('Account created successfully! Please check your email to verify your account.');

    } catch (error: any) {
      toast.error(error.message || 'Signup failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Check if current user is a demo account
      if (user && isDemoAccount(user.email || '')) {
        demoAuthService.logout();
        setUser(null);
        toast.success('🎭 Demo session ended');
        return;
      }

      // Regular logout
      await authService.logout();
      setUser(null);
      toast.success('Logged out successfully');
    } catch (error: any) {
      toast.error(error.message || 'Logout failed');
      throw error;
    }
  };

  const updateProfile = async (data: any) => {
    try {
      const updatedUser = await authService.updateProfile(data);
      if (user) {
        setUser({
          ...user,
          ...updatedUser,
        });
      }
      toast.success('Profile updated successfully');
    } catch (error: any) {
      toast.error(error.message || 'Profile update failed');
      throw error;
    }
  };

  const value = {
    user,
    loading,
    login,
    signup,
    logout,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
