import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';
import { useSupabase } from './SupabaseContext';
import { authService } from '../services/authService';
import toast from 'react-hot-toast';

// Types
interface AuthUser extends User {
  caregiver_id?: string;
  role?: string;
  first_name?: string;
  last_name?: string;
}

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (data: any) => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const { supabase } = useSupabase();

  // Check if we're in mock mode (for future use)
  // const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';

  const loadUserProfile = useCallback(async (authUser: User) => {
    try {
      // Get caregiver profile
      const { data: caregiver, error } = await supabase
        .from('caregivers')
        .select('id, first_name, last_name, role, is_active')
        .eq('user_id', authUser.id)
        .single();

      if (error) {
        console.error('Error loading caregiver profile:', error);
        return;
      }

      if (!caregiver.is_active) {
        toast.error('Your account is inactive. Please contact an administrator.');
        await logout();
        return;
      }

      setUser({
        ...authUser,
        caregiver_id: caregiver.id,
        role: caregiver.role,
        first_name: caregiver.first_name,
        last_name: caregiver.last_name,
      });
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  }, [supabase]);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          await loadUserProfile(session.user);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          await loadUserProfile(session.user);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, [supabase.auth, loadUserProfile]);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      const response = await authService.login(email, password);

      // For mock authentication, we need to manually set the user
      const isMockMode = process.env.REACT_APP_MOCK_AUTH === 'true';
      if (isMockMode) {
        const mockUser: AuthUser = {
          id: response.user.id,
          email: response.user.email,
          role: response.user.role as 'admin' | 'nurse' | 'caregiver',
          profile: {
            firstName: response.user.first_name,
            lastName: response.user.last_name,
            phone: '******-0123',
            avatar: null
          }
        };
        setUser(mockUser);
      }

      toast.success(`Welcome back, ${response.user.first_name}!`);
    } catch (error: any) {
      toast.error(error.message || 'Login failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
      setUser(null);
      toast.success('Logged out successfully');
    } catch (error: any) {
      toast.error(error.message || 'Logout failed');
      throw error;
    }
  };

  const updateProfile = async (data: any) => {
    try {
      const updatedUser = await authService.updateProfile(data);
      if (user) {
        setUser({
          ...user,
          ...updatedUser,
        });
      }
      toast.success('Profile updated successfully');
    } catch (error: any) {
      toast.error(error.message || 'Profile update failed');
      throw error;
    }
  };

  const value = {
    user,
    loading,
    login,
    logout,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
