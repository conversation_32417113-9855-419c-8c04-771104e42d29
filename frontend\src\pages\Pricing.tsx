import React from 'react';
import { Link } from 'react-router-dom';
import {
  CheckIcon,
  XMarkIcon,
  HeartIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  BellIcon,
  UserGroupIcon,
  PhoneIcon,
  TruckIcon,
  BeakerIcon,
  ChartBarIcon,
  BuildingStorefrontIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const Pricing: React.FC = () => {
  const basicFeatures = [
    { name: 'AI-MAR (Medication Administration Records)', icon: CpuChipIcon, included: true },
    { name: 'HIPAA Compliance', icon: ShieldCheckIcon, included: true },
    { name: 'DocuSign/E-signing', icon: DocumentTextIcon, included: true },
    { name: '<PERSON><PERSON><PERSON> and Reminders', icon: BellIcon, included: true },
    { name: 'Document Generation', icon: DocumentTextIcon, included: true },
    { name: 'Visitor Logs', icon: UserGroupIcon, included: true },
    { name: 'Progress Notes', icon: DocumentTextIcon, included: true },
    { name: 'Staff Management', icon: UserGroupIcon, included: true },
    { name: 'E-Faxing', icon: PhoneIcon, included: true },
    { name: 'Document Forwarding', icon: DocumentTextIcon, included: true },
  ];

  const standardFeatures = [
    ...basicFeatures,
    { name: 'Grocery Delivery', icon: TruckIcon, included: true },
    { name: 'Medication Delivery', icon: BeakerIcon, included: true },
    { name: 'Client Vitals (AI)', icon: ChartBarIcon, included: true },
    { name: 'Nutrition Package', icon: HeartIcon, included: true },
    { name: 'Store Partnership', icon: BuildingStorefrontIcon, included: true },
    { name: 'Advanced Staff Management', icon: UserGroupIcon, included: true },
  ];

  const advancedFeatures = [
    ...standardFeatures,
    { name: 'Predictive Analytics Dashboard', icon: CpuChipIcon, included: true },
    { name: 'Custom AI Model Training', icon: CpuChipIcon, included: true },
    { name: 'Multi-Facility Management', icon: BuildingStorefrontIcon, included: true },
    { name: 'Advanced Reporting Suite', icon: ChartBarIcon, included: true },
    { name: 'API Access & Integrations', icon: CpuChipIcon, included: true },
    { name: 'Dedicated Account Manager', icon: UserGroupIcon, included: true },
    { name: 'Priority Support (24/7)', icon: PhoneIcon, included: true },
    { name: 'Custom Workflow Builder', icon: DocumentTextIcon, included: true },
  ];

  const uniqueFeatures = [
    {
      title: 'AI-Powered AIMAR System',
      description: 'Our proprietary AI Medication Administration Records system reduces errors by 99.8% with intelligent drug interaction detection and dosage optimization.',
      icon: CpuChipIcon
    },
    {
      title: 'Integrated Care Ecosystem',
      description: 'Seamlessly connects medication management, nutrition planning, grocery delivery, and staff coordination in one unified platform.',
      icon: HeartIcon
    },
    {
      title: 'Predictive Health Analytics',
      description: 'Advanced AI algorithms predict health risks and recommend preventive care measures before issues arise.',
      icon: ChartBarIcon
    },
    {
      title: 'Automated Compliance Monitoring',
      description: 'Real-time HIPAA compliance tracking with automated audit trails and regulatory reporting.',
      icon: ShieldCheckIcon
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center">
                <div className="h-10 w-10 bg-gradient-to-r from-purple-600 to-amber-600 rounded-xl flex items-center justify-center p-1 shadow-lg">
                  <img
                    src="/care-solai-logo.jpg"
                    alt="Care-SolAI Logo"
                    className="h-8 w-8 object-cover rounded-lg"
                  />
                </div>
                <span className="ml-2 text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Care-SolAI
                </span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/" className="text-gray-600 hover:text-purple-600 px-3 py-2 text-sm font-medium">
                Home
              </Link>
              <Link to="/login" className="bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors">
                Sign In
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Choose Your
            <span className="block bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Care-SolAI Plan
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Comprehensive healthcare management solutions designed for residential care facilities. 
            Choose the plan that best fits your facility's needs.
          </p>
        </div>
      </section>

      {/* Unique Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Makes Care-SolAI Unique
            </h2>
            <p className="text-xl text-gray-600">
              Industry-leading features that set us apart from the competition
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {uniqueFeatures.map((feature, index) => (
              <div key={index} className="bg-gradient-to-br from-purple-50 to-blue-50 p-6 rounded-xl border border-purple-100">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="ml-4 text-xl font-semibold text-gray-900">{feature.title}</h3>
                </div>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Plans */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Pricing Plans
            </h2>
            <p className="text-xl text-gray-600">
              Transparent pricing with no hidden fees. Choose the plan that fits your facility.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {/* Basic Plan */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-r from-purple-600 to-blue-600 px-6 py-8 text-white text-center">
                <h3 className="text-2xl font-bold mb-2">Basic Plan</h3>
                <div className="text-4xl font-bold mb-2">$249.99</div>
                <p className="text-blue-100">per month</p>
              </div>
              
              <div className="p-6">
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Core Features Included:</h4>
                  <div className="space-y-3">
                    {basicFeatures.map((feature, index) => (
                      <div key={index} className="flex items-center">
                        <CheckIcon className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                        <div className="flex items-center">
                          <feature.icon className="h-4 w-4 text-purple-600 mr-2" />
                          <span className="text-gray-700">{feature.name}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Link
                  to="/signup"
                  className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-300 text-center block"
                >
                  Start Basic Plan
                </Link>
              </div>
            </div>

            {/* Standard Plan */}
            <div className="bg-white rounded-2xl shadow-xl border-2 border-yellow-400 overflow-hidden relative">
              <div className="absolute top-0 right-0 bg-yellow-400 text-purple-900 px-3 py-1 text-sm font-bold rounded-bl-lg">
                POPULAR
              </div>
              <div className="bg-gradient-to-r from-yellow-500 to-amber-500 px-6 py-8 text-white text-center">
                <h3 className="text-2xl font-bold mb-2">Standard Plan</h3>
                <div className="text-4xl font-bold mb-2">$349.99</div>
                <p className="text-yellow-100">per month</p>
                <p className="text-sm text-yellow-100 mt-2">Includes all Basic features plus:</p>
              </div>
              
              <div className="p-6">
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Everything in Basic, Plus:</h4>
                  <div className="space-y-3">
                    {standardFeatures.slice(10).map((feature, index) => (
                      <div key={index} className="flex items-center">
                        <StarIcon className="h-5 w-5 text-yellow-500 mr-3 flex-shrink-0" />
                        <div className="flex items-center">
                          <feature.icon className="h-4 w-4 text-yellow-600 mr-2" />
                          <span className="text-gray-700 font-medium">{feature.name}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Link
                  to="/signup"
                  className="w-full bg-gradient-to-r from-yellow-500 to-amber-500 text-white py-3 px-6 rounded-lg font-medium hover:from-yellow-600 hover:to-amber-600 transition-all duration-300 text-center block"
                >
                  Start Standard Plan
                </Link>
              </div>
            </div>

            {/* Advanced Plan */}
            <div className="bg-white rounded-2xl shadow-xl border-2 border-purple-400 overflow-hidden relative">
              <div className="absolute top-0 right-0 bg-purple-400 text-white px-3 py-1 text-sm font-bold rounded-bl-lg">
                ENTERPRISE
              </div>
              <div className="bg-gradient-to-r from-purple-600 to-indigo-600 px-6 py-8 text-white text-center">
                <h3 className="text-2xl font-bold mb-2">Advanced Plan</h3>
                <div className="text-4xl font-bold mb-2">$549.99</div>
                <p className="text-purple-100">per month</p>
                <p className="text-sm text-purple-100 mt-2">Includes all Standard features plus:</p>
              </div>

              <div className="p-6">
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Everything in Standard, Plus:</h4>
                  <div className="space-y-3">
                    {advancedFeatures.slice(16).map((feature, index) => (
                      <div key={index} className="flex items-center">
                        <CpuChipIcon className="h-5 w-5 text-purple-500 mr-3 flex-shrink-0" />
                        <div className="flex items-center">
                          <feature.icon className="h-4 w-4 text-purple-600 mr-2" />
                          <span className="text-gray-700 font-medium">{feature.name}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Link
                  to="/signup"
                  className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 text-center block"
                >
                  Start Advanced Plan
                </Link>
              </div>
            </div>
          </div>

          {/* Feature Comparison Table */}
          <div className="mt-16">
            <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">Feature Comparison</h3>
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
                    <tr>
                      <th className="px-6 py-4 text-left font-semibold">Features</th>
                      <th className="px-6 py-4 text-center font-semibold">Basic ($249.99)</th>
                      <th className="px-6 py-4 text-center font-semibold">Standard ($349.99)</th>
                      <th className="px-6 py-4 text-center font-semibold">Advanced ($549.99)</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {advancedFeatures.map((feature, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                        <td className="px-6 py-4 flex items-center">
                          <feature.icon className="h-5 w-5 text-purple-600 mr-3" />
                          <span className="font-medium text-gray-900">{feature.name}</span>
                        </td>
                        <td className="px-6 py-4 text-center">
                          {index < 10 ? (
                            <CheckIcon className="h-6 w-6 text-green-500 mx-auto" />
                          ) : (
                            <XMarkIcon className="h-6 w-6 text-gray-400 mx-auto" />
                          )}
                        </td>
                        <td className="px-6 py-4 text-center">
                          {index < 16 ? (
                            <CheckIcon className="h-6 w-6 text-green-500 mx-auto" />
                          ) : (
                            <XMarkIcon className="h-6 w-6 text-gray-400 mx-auto" />
                          )}
                        </td>
                        <td className="px-6 py-4 text-center">
                          <CheckIcon className="h-6 w-6 text-green-500 mx-auto" />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Healthcare Operations?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join hundreds of healthcare facilities already using Care-SolAI to provide better patient care.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/signup"
              className="inline-flex items-center px-8 py-4 bg-white text-purple-600 text-lg font-medium rounded-lg hover:bg-gray-100 transition-colors"
            >
              Get Started
            </Link>
            <Link
              to="/"
              className="inline-flex items-center px-8 py-4 border-2 border-white text-white text-lg font-medium rounded-lg hover:bg-white hover:text-purple-600 transition-colors"
            >
              Learn More
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="h-10 w-10 bg-gradient-to-r from-purple-600 to-amber-600 rounded-xl flex items-center justify-center p-1 shadow-lg">
              <img
                src="/care-solai-logo.jpg"
                alt="Care-SolAI Logo"
                className="h-8 w-8 object-cover rounded-lg"
              />
            </div>
            <span className="ml-2 text-xl font-bold">Care-SolAI</span>
          </div>
          <p className="text-gray-400">
            &copy; 2025 Care-SolAI. All rights reserved. HIPAA Compliant Healthcare Technology.
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Created by <span className="text-amber-400 font-semibold">NYOHAKI</span> and <span className="text-purple-400 font-semibold">SAM INC.</span>
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Pricing;
