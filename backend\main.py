"""
CareSyncAI Backend API
Main FastAPI application for homecare management system
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from contextlib import asynccontextmanager
import uvicorn
import os
from typing import Optional

# Import routers
from routers import patients, medical_records, records, inventory, billing, ai_predictions, auth, caregivers
from core.config import settings
from core.database import init_db
from core.auth import verify_token
from middleware.logging import LoggingMiddleware
from middleware.rate_limiting import RateLimitMiddleware

# Security
security = HTTPBearer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting CareSyncAI Backend...")
    await init_db()
    print("✅ Database initialized")
    yield
    # Shutdown
    print("🛑 Shutting down CareSyncAI Backend...")

# Create FastAPI app
app = FastAPI(
    title="CareSyncAI API",
    description="AI-Powered Homecare Management System API",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.caresyncai.com"]
)

app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimitMiddleware)

# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint for monitoring"""
    return {
        "status": "healthy",
        "service": "CareSyncAI Backend",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }

# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to CareSyncAI API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

# Authentication dependency
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current authenticated user"""
    try:
        payload = verify_token(credentials.credentials)
        return payload
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

# Include routers
app.include_router(
    auth.router,
    prefix="/api/auth",
    tags=["Authentication"]
)

app.include_router(
    caregivers.router,
    prefix="/api/caregivers",
    tags=["Caregivers"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    patients.router,
    prefix="/api/patients",
    tags=["Patients"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    medical_records.router,
    prefix="/api/medical-records",
    tags=["Medical Records"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    records.router,
    prefix="/api/records",
    tags=["Records"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    inventory.router,
    prefix="/api/inventory",
    tags=["Inventory"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    billing.router,
    prefix="/api/billing",
    tags=["Billing"],
    dependencies=[Depends(get_current_user)]
)

app.include_router(
    ai_predictions.router,
    prefix="/api/ai",
    tags=["AI Predictions"],
    dependencies=[Depends(get_current_user)]
)

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return {
        "error": True,
        "message": exc.detail,
        "status_code": exc.status_code
    }

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler"""
    if settings.DEBUG:
        import traceback
        return {
            "error": True,
            "message": str(exc),
            "traceback": traceback.format_exc()
        }
    return {
        "error": True,
        "message": "Internal server error"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
