"""
Patient management API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, Query, status
from typing import List, Optional
import uuid
from datetime import date

from schemas.patient import (
    PatientCreate, PatientUpdate, PatientResponse, 
    PatientSummary, PatientSearchResponse
)
from core.database import DatabaseService
from core.auth import get_current_user, check_patient_access, check_supervisor_permission
from services.patient_service import PatientService
from utils.pagination import PaginationParams

router = APIRouter()

# Initialize patient service
patient_service = PatientService()

@router.post("/", response_model=PatientResponse, status_code=status.HTTP_201_CREATED)
async def create_patient(
    patient_data: PatientCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new patient
    Requires supervisor or admin role
    """
    if not check_supervisor_permission(current_user.get("role")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create patients"
        )
    
    try:
        patient = await patient_service.create_patient(patient_data.dict(), current_user["caregiver_id"])
        return patient
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create patient: {str(e)}"
        )

@router.get("/", response_model=PatientSearchResponse)
async def get_patients(
    pagination: PaginationParams = Depends(),
    status_filter: Optional[str] = Query(None, description="Filter by patient status"),
    caregiver_id: Optional[uuid.UUID] = Query(None, description="Filter by caregiver"),
    search: Optional[str] = Query(None, description="Search by name or patient number"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get list of patients with filtering and pagination
    Returns only patients assigned to current caregiver unless user is supervisor/admin
    """
    try:
        filters = {}
        
        # Apply role-based filtering
        if not check_supervisor_permission(current_user.get("role")):
            # Regular caregivers can only see their assigned patients
            filters["caregiver_filter"] = current_user["caregiver_id"]
        elif caregiver_id:
            # Supervisors can filter by specific caregiver
            filters["caregiver_filter"] = str(caregiver_id)
        
        if status_filter:
            filters["status"] = status_filter
        
        patients = await patient_service.get_patients(
            filters=filters,
            search_term=search,
            limit=pagination.limit,
            offset=pagination.offset
        )
        
        total_count = await patient_service.count_patients(filters)
        
        return PatientSearchResponse(
            patients=patients,
            total_count=total_count,
            page=pagination.page,
            page_size=pagination.limit,
            total_pages=(total_count + pagination.limit - 1) // pagination.limit
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve patients: {str(e)}"
        )

@router.get("/{patient_id}", response_model=PatientResponse)
async def get_patient(
    patient_id: uuid.UUID,
    current_user: dict = Depends(get_current_user)
):
    """
    Get patient by ID
    Requires access to the specific patient
    """
    # Check patient access
    if not check_patient_access(
        current_user["caregiver_id"], 
        str(patient_id), 
        current_user["role"]
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient"
        )
    
    try:
        patient = await patient_service.get_patient_by_id(str(patient_id))
        if not patient:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient not found"
            )
        return patient
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve patient: {str(e)}"
        )

@router.put("/{patient_id}", response_model=PatientResponse)
async def update_patient(
    patient_id: uuid.UUID,
    patient_data: PatientUpdate,
    current_user: dict = Depends(get_current_user)
):
    """
    Update patient information
    Requires access to the specific patient
    """
    # Check patient access
    if not check_patient_access(
        current_user["caregiver_id"], 
        str(patient_id), 
        current_user["role"]
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient"
        )
    
    try:
        # Remove None values from update data
        update_data = {k: v for k, v in patient_data.dict().items() if v is not None}
        
        patient = await patient_service.update_patient(str(patient_id), update_data)
        if not patient:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient not found"
            )
        return patient
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update patient: {str(e)}"
        )

@router.delete("/{patient_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_patient(
    patient_id: uuid.UUID,
    current_user: dict = Depends(get_current_user)
):
    """
    Delete patient (soft delete - set status to inactive)
    Requires admin role
    """
    if current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can delete patients"
        )
    
    try:
        success = await patient_service.delete_patient(str(patient_id))
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete patient: {str(e)}"
        )

@router.get("/{patient_id}/summary")
async def get_patient_summary(
    patient_id: uuid.UUID,
    current_user: dict = Depends(get_current_user)
):
    """
    Get patient summary for AI analysis
    Includes recent vitals, medications, and care notes
    """
    # Check patient access
    if not check_patient_access(
        current_user["caregiver_id"], 
        str(patient_id), 
        current_user["role"]
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this patient"
        )
    
    try:
        summary = await patient_service.get_patient_ai_summary(str(patient_id))
        if not summary:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient not found"
            )
        return summary
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve patient summary: {str(e)}"
        )

@router.post("/{patient_id}/assign-caregiver")
async def assign_caregiver(
    patient_id: uuid.UUID,
    caregiver_id: uuid.UUID,
    is_primary: bool = True,
    current_user: dict = Depends(get_current_user)
):
    """
    Assign caregiver to patient
    Requires supervisor or admin role
    """
    if not check_supervisor_permission(current_user.get("role")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to assign caregivers"
        )
    
    try:
        success = await patient_service.assign_caregiver(
            str(patient_id), 
            str(caregiver_id), 
            is_primary
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Patient or caregiver not found"
            )
        return {"message": "Caregiver assigned successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to assign caregiver: {str(e)}"
        )
