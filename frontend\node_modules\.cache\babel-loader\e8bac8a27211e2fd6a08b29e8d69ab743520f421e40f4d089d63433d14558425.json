{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@supabase\\storage-js\\src\\lib\\types.ts"], "sourcesContent": ["export interface Bucket {\n  id: string\n  name: string\n  owner: string\n  file_size_limit?: number\n  allowed_mime_types?: string[]\n  created_at: string\n  updated_at: string\n  public: boolean\n}\n\nexport interface FileObject {\n  name: string\n  bucket_id: string\n  owner: string\n  id: string\n  updated_at: string\n  created_at: string\n  last_accessed_at: string\n  metadata: Record<string, any>\n  buckets: Bucket\n}\n\nexport interface FileObjectV2 {\n  id: string\n  version: string\n  name: string\n  bucket_id: string\n  updated_at: string\n  created_at: string\n  last_accessed_at: string\n  size?: number\n  cache_control?: string\n  content_type?: string\n  etag?: string\n  last_modified?: string\n  metadata?: Record<string, any>\n}\n\nexport interface SortBy {\n  column?: string\n  order?: string\n}\n\nexport interface FileOptions {\n  /**\n   * The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control: max-age=<seconds>` header. Defaults to 3600 seconds.\n   */\n  cacheControl?: string\n  /**\n   * the `Content-Type` header value. Should be specified if using a `fileBody` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`.\n   */\n  contentType?: string\n  /**\n   * When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false.\n   */\n  upsert?: boolean\n  /**\n   * The duplex option is a string parameter that enables or disables duplex streaming, allowing for both reading and writing data in the same stream. It can be passed as an option to the fetch() method.\n   */\n  duplex?: string\n\n  /**\n   * The metadata option is an object that allows you to store additional information about the file. This information can be used to filter and search for files. The metadata object can contain any key-value pairs you want to store.\n   */\n  metadata?: Record<string, any>\n\n  /**\n   * Optionally add extra headers\n   */\n  headers?: Record<string, string>\n}\n\nexport interface DestinationOptions {\n  destinationBucket?: string\n}\n\nexport interface SearchOptions {\n  /**\n   *  The number of files you want to be returned.\n   */\n  limit?: number\n\n  /**\n   * The starting position.\n   */\n  offset?: number\n\n  /**\n   * The column to sort by. Can be any column inside a FileObject.\n   */\n  sortBy?: SortBy\n\n  /**\n   * The search string to filter files by.\n   */\n  search?: string\n}\n\nexport interface FetchParameters {\n  /**\n   * Pass in an AbortController's signal to cancel the request.\n   */\n  signal?: AbortSignal\n}\n\n// TODO: need to check for metadata props. The api swagger doesnt have.\nexport interface Metadata {\n  name: string\n}\n\nexport interface TransformOptions {\n  /**\n   * The width of the image in pixels.\n   */\n  width?: number\n  /**\n   * The height of the image in pixels.\n   */\n  height?: number\n  /**\n   * The resize mode can be cover, contain or fill. Defaults to cover.\n   * Cover resizes the image to maintain it's aspect ratio while filling the entire width and height.\n   * Contain resizes the image to maintain it's aspect ratio while fitting the entire image within the width and height.\n   * Fill resizes the image to fill the entire width and height. If the object's aspect ratio does not match the width and height, the image will be stretched to fit.\n   */\n  resize?: 'cover' | 'contain' | 'fill'\n  /**\n   * Set the quality of the returned image.\n   * A number from 20 to 100, with 100 being the highest quality.\n   * Defaults to 80\n   */\n  quality?: number\n  /**\n   * Specify the format of the image requested.\n   *\n   * When using 'origin' we force the format to be the same as the original image.\n   * When this option is not passed in, images are optimized to modern image formats like Webp.\n   */\n  format?: 'origin'\n}\n\ntype CamelCase<S extends string> = S extends `${infer P1}_${infer P2}${infer P3}`\n  ? `${Lowercase<P1>}${Uppercase<P2>}${CamelCase<P3>}`\n  : S\n\nexport type Camelize<T> = {\n  [K in keyof T as CamelCase<Extract<K, string>>]: T[K]\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}