{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\medisyn\\\\frontend\\\\src\\\\pages\\\\Patients.tsx\";\nimport React from 'react';\nimport { PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Patients = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Patients\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-600\",\n          children: \"Manage your patient roster and care plans\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 sm:mt-0\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-primary\",\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), \"Add Patient\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search patients...\",\n                className: \"form-input pl-10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-input\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                children: \"All Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-input\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                children: \"All Caregivers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                children: \"Emily Rodriguez\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                children: \"David Wilson\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400 text-xl\",\n              children: \"\\uD83D\\uDC65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-2\",\n            children: \"Patient Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-4\",\n            children: \"This section will display your patient roster with detailed information, care plans, and AI-powered insights.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Features coming soon: Patient profiles, medical history, care scheduling, fall risk assessments, and medication management.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Patients;\nexport default Patients;\nvar _c;\n$RefreshReg$(_c, \"Patients\");", "map": {"version": 3, "names": ["React", "PlusIcon", "MagnifyingGlassIcon", "jsxDEV", "_jsxDEV", "Patients", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/src/pages/Patients.tsx"], "sourcesContent": ["import React from 'react';\nimport { PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';\n\nconst Patients: React.FC = () => {\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Patients</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Manage your patient roster and care plans\n          </p>\n        </div>\n        <div className=\"mt-4 sm:mt-0\">\n          <button className=\"btn-primary\">\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Add Patient\n          </button>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"card\">\n        <div className=\"card-body\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search patients...\"\n                  className=\"form-input pl-10\"\n                />\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <select className=\"form-input\">\n                <option>All Status</option>\n                <option>Active</option>\n                <option>Inactive</option>\n              </select>\n              <select className=\"form-input\">\n                <option>All Caregivers</option>\n                <option>Emily Rodriguez</option>\n                <option>David Wilson</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Patients List */}\n      <div className=\"card\">\n        <div className=\"card-body\">\n          <div className=\"text-center py-12\">\n            <div className=\"h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-gray-400 text-xl\">👥</span>\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Patient Management\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              This section will display your patient roster with detailed information,\n              care plans, and AI-powered insights.\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              Features coming soon: Patient profiles, medical history, care scheduling,\n              fall risk assessments, and medication management.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Patients;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,mBAAmB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAC/B,oBACED,OAAA;IAAKE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBH,OAAA;MAAKE,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAC3EH,OAAA;QAAAG,QAAA,gBACEH,OAAA;UAAIE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DP,OAAA;UAAGE,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BH,OAAA;UAAQE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC7BH,OAAA,CAACH,QAAQ;YAACK,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNP,OAAA;MAAKE,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBH,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBH,OAAA;UAAKE,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CH,OAAA;YAAKE,SAAS,EAAC,QAAQ;YAAAC,QAAA,eACrBH,OAAA;cAAKE,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBH,OAAA,CAACF,mBAAmB;gBAACI,SAAS,EAAC;cAA0E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5GP,OAAA;gBACEQ,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oBAAoB;gBAChCP,SAAS,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAQE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC5BH,OAAA;gBAAAG,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3BP,OAAA;gBAAAG,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvBP,OAAA;gBAAAG,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACTP,OAAA;cAAQE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC5BH,OAAA;gBAAAG,QAAA,EAAQ;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/BP,OAAA;gBAAAG,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCP,OAAA;gBAAAG,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNP,OAAA;MAAKE,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBH,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBH,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCH,OAAA;YAAKE,SAAS,EAAC,kFAAkF;YAAAC,QAAA,eAC/FH,OAAA;cAAME,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNP,OAAA;YAAIE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLP,OAAA;YAAGE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAGlC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJP,OAAA;YAAGE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAGrC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GAxEIT,QAAkB;AA0ExB,eAAeA,QAAQ;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}