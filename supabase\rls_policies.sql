-- Row Level Security Policies for CareSyncAI
-- Ensures HIPAA compliance and proper access control

-- Helper function to get current user's caregiver record
CREATE OR REPLACE FUNCTION get_current_caregiver_id()
RETURNS UUID AS $$
BEGIN
    RETURN (
        SELECT id FROM caregivers 
        WHERE user_id = auth.uid()
        LIMIT 1
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT role = 'admin' FROM caregivers 
        WHERE user_id = auth.uid()
        LIMIT 1
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is supervisor or admin
CREATE OR REPLACE FUNCTION is_supervisor_or_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        SELECT role IN ('admin', 'supervisor') FROM caregivers 
        WHERE user_id = auth.uid()
        LIMIT 1
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Caregivers table policies
CREATE POLICY "Caregivers can view their own profile" ON caregivers
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can view all caregivers" ON caregivers
    FOR SELECT USING (is_admin());

CREATE POLICY "Supervisors can view active caregivers" ON caregivers
    FOR SELECT USING (is_supervisor_or_admin() AND is_active = true);

CREATE POLICY "Admins can insert caregivers" ON caregivers
    FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Admins can update caregivers" ON caregivers
    FOR UPDATE USING (is_admin());

CREATE POLICY "Caregivers can update their own profile" ON caregivers
    FOR UPDATE USING (user_id = auth.uid());

-- Patients table policies
CREATE POLICY "Caregivers can view assigned patients" ON patients
    FOR SELECT USING (
        primary_caregiver_id = get_current_caregiver_id() OR
        backup_caregiver_id = get_current_caregiver_id() OR
        is_supervisor_or_admin()
    );

CREATE POLICY "Supervisors and admins can insert patients" ON patients
    FOR INSERT WITH CHECK (is_supervisor_or_admin());

CREATE POLICY "Assigned caregivers can update patients" ON patients
    FOR UPDATE USING (
        primary_caregiver_id = get_current_caregiver_id() OR
        backup_caregiver_id = get_current_caregiver_id() OR
        is_supervisor_or_admin()
    );

-- Medical records table policies
CREATE POLICY "Caregivers can view records for assigned patients" ON medical_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM patients p 
            WHERE p.id = patient_id 
            AND (p.primary_caregiver_id = get_current_caregiver_id() OR
                 p.backup_caregiver_id = get_current_caregiver_id())
        ) OR is_supervisor_or_admin()
    );

CREATE POLICY "Caregivers can insert records for assigned patients" ON medical_records
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM patients p 
            WHERE p.id = patient_id 
            AND (p.primary_caregiver_id = get_current_caregiver_id() OR
                 p.backup_caregiver_id = get_current_caregiver_id())
        ) OR is_supervisor_or_admin()
    );

CREATE POLICY "Caregivers can update their own records" ON medical_records
    FOR UPDATE USING (
        caregiver_id = get_current_caregiver_id() OR
        is_supervisor_or_admin()
    );

-- Records/Documents table policies
CREATE POLICY "Caregivers can view documents for assigned patients" ON records
    FOR SELECT USING (
        (access_level = 1 AND EXISTS (
            SELECT 1 FROM patients p 
            WHERE p.id = patient_id 
            AND (p.primary_caregiver_id = get_current_caregiver_id() OR
                 p.backup_caregiver_id = get_current_caregiver_id())
        )) OR
        (access_level = 2 AND is_supervisor_or_admin()) OR
        (access_level = 3 AND is_admin())
    );

CREATE POLICY "Caregivers can upload documents for assigned patients" ON records
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM patients p 
            WHERE p.id = patient_id 
            AND (p.primary_caregiver_id = get_current_caregiver_id() OR
                 p.backup_caregiver_id = get_current_caregiver_id())
        ) OR is_supervisor_or_admin()
    );

CREATE POLICY "Users can update their own uploaded documents" ON records
    FOR UPDATE USING (
        uploaded_by = get_current_caregiver_id() OR
        is_supervisor_or_admin()
    );

-- Shop inventory table policies
CREATE POLICY "All authenticated users can view inventory" ON shop_inventory
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Caregivers can update inventory quantities" ON shop_inventory
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Supervisors can insert inventory items" ON shop_inventory
    FOR INSERT WITH CHECK (is_supervisor_or_admin());

CREATE POLICY "Admins can delete inventory items" ON shop_inventory
    FOR DELETE USING (is_admin());

-- Billing table policies
CREATE POLICY "Billing staff can view all billing records" ON billing
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM caregivers c 
            WHERE c.user_id = auth.uid() 
            AND c.role IN ('admin', 'billing')
        )
    );

CREATE POLICY "Caregivers can view their own billing records" ON billing
    FOR SELECT USING (caregiver_id = get_current_caregiver_id());

CREATE POLICY "Billing staff can insert billing records" ON billing
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM caregivers c 
            WHERE c.user_id = auth.uid() 
            AND c.role IN ('admin', 'billing')
        )
    );

CREATE POLICY "Billing staff can update billing records" ON billing
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM caregivers c 
            WHERE c.user_id = auth.uid() 
            AND c.role IN ('admin', 'billing')
        )
    );

-- AI predictions table policies
CREATE POLICY "Caregivers can view predictions for assigned patients" ON ai_predictions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM patients p 
            WHERE p.id = patient_id 
            AND (p.primary_caregiver_id = get_current_caregiver_id() OR
                 p.backup_caregiver_id = get_current_caregiver_id())
        ) OR is_supervisor_or_admin()
    );

CREATE POLICY "System can insert AI predictions" ON ai_predictions
    FOR INSERT WITH CHECK (true); -- Allow system inserts

-- Care schedules table policies
CREATE POLICY "Caregivers can view their own schedules" ON care_schedules
    FOR SELECT USING (
        caregiver_id = get_current_caregiver_id() OR
        is_supervisor_or_admin()
    );

CREATE POLICY "Supervisors can insert schedules" ON care_schedules
    FOR INSERT WITH CHECK (is_supervisor_or_admin());

CREATE POLICY "Caregivers can update their own schedules" ON care_schedules
    FOR UPDATE USING (
        caregiver_id = get_current_caregiver_id() OR
        is_supervisor_or_admin()
    );

-- Audit log policies (read-only for most users)
CREATE POLICY "Admins can view audit logs" ON audit_log
    FOR SELECT USING (is_admin());

CREATE POLICY "System can insert audit logs" ON audit_log
    FOR INSERT WITH CHECK (true); -- Allow system inserts
