{"ast": null, "code": "import GoTrueClient from './GoTrueClient';\nconst AuthClient = GoTrueClient;\nexport default AuthClient;", "map": {"version": 3, "names": ["GoTrueClient", "AuthClient"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@supabase\\auth-js\\src\\AuthClient.ts"], "sourcesContent": ["import GoTrueClient from './GoTrueClient'\n\nconst AuthClient = GoTrueClient\n\nexport default AuthClient\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,gBAAgB;AAEzC,MAAMC,UAAU,GAAGD,YAAY;AAE/B,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}