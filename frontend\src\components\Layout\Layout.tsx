import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  UserGroupIcon,
  DocumentTextIcon,
  CubeIcon,
  CurrencyDollarIcon,
  UserCircleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  FolderIcon,
  HeartIcon,
  CakeIcon,
  ShieldCheckIcon,
  ShoppingCartIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';

interface LayoutProps {
  children: React.ReactNode;
}

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Residents', href: '/patients', icon: UserGroupIcon },
  { name: 'Medical Management', href: '/medical-management', icon: HeartIcon },
  { name: 'Nutrition Management', href: '/nutrition-management', icon: CakeIcon },
  { name: 'Purchasing & Supplies', href: '/grocery-management', icon: ShoppingCartIcon },
  { name: 'Care Records', href: '/medical-records', icon: DocumentTextIcon },
  { name: 'Documents', href: '/documents', icon: FolderIcon },
  { name: 'Fax Center', href: '/fax-management', icon: BellIcon },
  { name: 'AIMAR', href: '/emar', icon: CubeIcon },
  { name: 'Staff Management', href: '/staff', icon: UserCircleIcon },
  { name: 'HIPAA Compliance', href: '/hipaa-compliance', icon: ShieldCheckIcon },
  { name: 'Billing', href: '/billing', icon: CurrencyDollarIcon },
];

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();

  const isCurrentPath = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Ultra-Beautiful Mobile Sidebar */}
      <div className={`fixed inset-0 z-40 lg:hidden ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gradient-to-br from-purple-900/75 via-blue-900/75 to-indigo-900/75 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-72 flex-col bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 shadow-2xl border-r-4 border-amber-400">
          {/* Mobile Header */}
          <div className="flex h-20 items-center justify-between px-6 border-b border-purple-700/50">
            <div className="flex items-center group cursor-pointer">
              <div className="relative h-14 w-14 bg-gradient-to-br from-amber-400 via-yellow-500 to-amber-600 rounded-2xl flex items-center justify-center shadow-xl group-hover:scale-110 transition-all duration-300 border-2 border-yellow-300 p-1">
                <img
                  src="/care-solai-logo.jpg"
                  alt="Care-SolAI Logo"
                  className="h-12 w-12 object-cover rounded-xl"
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-pulse"></div>
              </div>
              <div className="ml-3">
                <span className="text-xl font-black bg-gradient-to-r from-amber-300 to-yellow-400 bg-clip-text text-transparent">Care-SolAI</span>
                <span className="block text-sm font-bold text-blue-200">Residential Care</span>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-2 text-amber-300 hover:text-white hover:bg-purple-700/50 rounded-xl transition-all duration-300"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Mobile Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigation.map((item, index) => (
              <Link
                key={item.name}
                to={item.href}
                className={`group flex items-center px-4 py-3 text-sm font-bold rounded-2xl transition-all duration-300 transform hover:scale-105 ${
                  isCurrentPath(item.href)
                    ? 'bg-gradient-to-r from-amber-400 to-yellow-500 text-purple-900 shadow-xl border-2 border-yellow-300'
                    : 'text-blue-100 hover:bg-gradient-to-r hover:from-purple-700 hover:to-blue-700 hover:text-white border-2 border-transparent hover:border-amber-300'
                }`}
                onClick={() => setSidebarOpen(false)}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className={`p-2 rounded-xl mr-4 transition-all duration-300 ${
                  isCurrentPath(item.href)
                    ? 'bg-purple-700 shadow-lg group-hover:scale-110 group-hover:rotate-12'
                    : 'bg-purple-800/50 group-hover:bg-amber-500 group-hover:scale-110 group-hover:rotate-6'
                }`}>
                  <item.icon className={`h-5 w-5 transition-colors duration-300 ${
                    isCurrentPath(item.href) ? 'text-amber-300' : 'text-blue-200 group-hover:text-purple-900'
                  }`} />
                </div>
                <span className="font-black">{item.name}</span>
                {isCurrentPath(item.href) && (
                  <div className="ml-auto w-2 h-2 bg-purple-700 rounded-full animate-pulse"></div>
                )}
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* Ultra-Beautiful Desktop Sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-72 lg:flex-col">
        <div className="flex flex-col flex-grow bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 shadow-2xl border-r-4 border-amber-400">
          {/* Animated Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-800/30 via-blue-800/30 to-indigo-800/30"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-amber-400/20 to-yellow-500/20 rounded-full animate-pulse"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-400/20 to-blue-400/20 rounded-full animate-bounce"></div>

          {/* Header */}
          <div className="relative z-10 flex h-20 items-center px-6 border-b border-purple-700/50">
            <div className="flex items-center group cursor-pointer" onClick={() => alert('Care-SolAI - Next Generation Healthcare Management')}>
              <div className="relative h-14 w-14 bg-gradient-to-br from-amber-400 via-yellow-500 to-amber-600 rounded-2xl flex items-center justify-center shadow-xl group-hover:scale-110 transition-all duration-300 border-2 border-yellow-300 p-1">
                <img
                  src="/care-solai-logo.jpg"
                  alt="Care-SolAI Logo"
                  className="h-12 w-12 object-cover rounded-xl"
                />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-pulse"></div>
              </div>
              <div className="ml-4 group-hover:scale-105 transition-all duration-300">
                <span className="text-xl font-black bg-gradient-to-r from-amber-300 to-yellow-400 bg-clip-text text-transparent">Care-SolAI</span>
                <span className="block text-sm font-bold text-blue-200">Residential Care Platform</span>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="relative z-10 flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigation.map((item, index) => (
              <Link
                key={item.name}
                to={item.href}
                className={`group flex items-center px-4 py-3 text-sm font-bold rounded-2xl transition-all duration-300 transform hover:scale-105 ${
                  isCurrentPath(item.href)
                    ? 'bg-gradient-to-r from-amber-400 to-yellow-500 text-purple-900 shadow-xl border-2 border-yellow-300 scale-105'
                    : 'text-blue-100 hover:bg-gradient-to-r hover:from-purple-700 hover:to-blue-700 hover:text-white border-2 border-transparent hover:border-amber-300'
                }`}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className={`relative p-2 rounded-xl mr-4 transition-all duration-300 ${
                  isCurrentPath(item.href)
                    ? 'bg-purple-700 shadow-lg group-hover:scale-110 group-hover:rotate-12'
                    : 'bg-purple-800/50 group-hover:bg-amber-500 group-hover:scale-110 group-hover:rotate-6'
                }`}>
                  <item.icon className={`h-5 w-5 transition-colors duration-300 ${
                    isCurrentPath(item.href) ? 'text-amber-300' : 'text-blue-200 group-hover:text-purple-900'
                  }`} />
                  {isCurrentPath(item.href) && (
                    <div className="absolute -top-1 -right-1 w-2 h-2 bg-amber-300 rounded-full animate-ping"></div>
                  )}
                </div>
                <span className="font-black flex-1">{item.name}</span>
                {isCurrentPath(item.href) && (
                  <div className="w-2 h-2 bg-purple-700 rounded-full animate-pulse"></div>
                )}
              </Link>
            ))}
          </nav>

          {/* Enhanced User Info */}
          <div className="relative z-10 flex-shrink-0 border-t border-purple-700/50 p-6">
            <div className="group flex items-center w-full cursor-pointer hover:bg-purple-800/30 rounded-2xl p-3 transition-all duration-300">
              <div className="relative h-12 w-12 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-all duration-300 border-2 border-yellow-300">
                <UserCircleIcon className="h-6 w-6 text-purple-900" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-bold text-white group-hover:text-amber-300 transition-colors duration-300">
                  {user?.first_name} {user?.last_name}
                </p>
                <p className="text-xs text-blue-200 capitalize group-hover:text-yellow-300 transition-colors duration-300">
                  {user?.role} • Online
                </p>
              </div>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-72">
        {/* Ultra-Beautiful Top Bar */}
        <div className="sticky top-0 z-10 bg-gradient-to-r from-white via-blue-50/80 to-purple-50/80 border-b-4 border-gradient-to-r from-purple-200 via-blue-200 to-amber-200 shadow-xl backdrop-blur-sm">
          <div className="flex h-20 items-center justify-between px-6 sm:px-8 lg:px-10">
            {/* Mobile Menu Button */}
            <button
              onClick={() => setSidebarOpen(true)}
              className="group p-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-2xl hover:from-purple-500 hover:to-blue-500 lg:hidden shadow-xl transform hover:scale-110 transition-all duration-300 border-2 border-purple-400"
            >
              <Bars3Icon className="h-6 w-6 group-hover:rotate-180 transition-transform duration-300" />
            </button>

            <div className="flex items-center space-x-6">
              {/* Enhanced Notifications */}
              <button className="group relative p-3 bg-gradient-to-r from-amber-400 to-yellow-500 text-purple-900 rounded-2xl hover:from-yellow-400 hover:to-amber-400 shadow-xl transform hover:scale-110 transition-all duration-300 border-2 border-yellow-300">
                <BellIcon className="h-6 w-6 group-hover:animate-bounce" />
                <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-gradient-to-r from-red-500 to-pink-500 rounded-full shadow-lg animate-pulse">
                  3
                </span>
              </button>

              {/* Enhanced User Menu */}
              <div className="flex items-center space-x-4">
                <Link
                  to="/profile"
                  className="group relative overflow-hidden px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold rounded-2xl hover:from-purple-500 hover:to-blue-500 shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-purple-400"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center">
                    <UserCircleIcon className="h-5 w-5 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                    <span>Profile</span>
                  </div>
                </Link>

                <button
                  onClick={logout}
                  className="group relative overflow-hidden px-6 py-3 bg-gradient-to-r from-amber-500 to-yellow-600 text-purple-900 font-bold rounded-2xl hover:from-yellow-400 hover:to-amber-400 shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-yellow-400"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center">
                    <span>Sign out</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="py-6">
            <div className="mobile-container">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
