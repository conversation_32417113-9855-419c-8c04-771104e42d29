"""
Billing service for CareSyncAI with Zoho Books integration
"""

from typing import Dict, Any, List, Optional
from datetime import date, datetime, timedelta
import uuid
import logging

from core.database import DatabaseService, get_db
from services.zoho_service import ZohoBooksService
from core.config import settings

logger = logging.getLogger(__name__)

class BillingService:
    """Service class for billing operations"""
    
    def __init__(self):
        self.db_service = DatabaseService("billing")
        self.zoho_service = ZohoBooksService()
        self.client = get_db()
    
    async def create_billing_record(
        self, 
        billing_data: Dict[str, Any], 
        created_by: str
    ) -> Dict[str, Any]:
        """Create a new billing record"""
        try:
            # Generate invoice number if not provided
            if not billing_data.get("invoice_number"):
                billing_data["invoice_number"] = await self._generate_invoice_number()
            
            # Set default values
            billing_data["status"] = "draft"
            billing_data["created_by"] = created_by
            
            # Calculate totals
            billing_data = self._calculate_totals(billing_data)
            
            # Create billing record in database
            billing_record = await self.db_service.create(billing_data)
            
            logger.info(f"Billing record created: {billing_record['id']}")
            return billing_record
        except Exception as e:
            logger.error(f"Error creating billing record: {e}")
            raise
    
    async def create_and_send_invoice(
        self, 
        billing_id: str, 
        send_email: bool = True
    ) -> Dict[str, Any]:
        """Create invoice in Zoho Books and optionally send it"""
        try:
            # Get billing record
            billing_record = await self.db_service.get_by_id(billing_id)
            if not billing_record:
                raise ValueError("Billing record not found")
            
            # Get patient and caregiver details
            patient_data = await self._get_patient_data(billing_record["patient_id"])
            caregiver_data = await self._get_caregiver_data(billing_record["caregiver_id"])
            
            # Prepare invoice data for Zoho
            invoice_data = {
                "customer_data": {
                    "patient_id": billing_record["patient_id"],
                    "first_name": patient_data["first_name"],
                    "last_name": patient_data["last_name"],
                    "email": patient_data.get("email"),
                    "phone": patient_data.get("phone"),
                    "address": patient_data.get("address", {})
                },
                "patient_id": billing_record["patient_id"],
                "caregiver_id": billing_record["caregiver_id"],
                "invoice_number": billing_record["invoice_number"],
                "date": billing_record["billing_period_start"],
                "due_date": billing_record.get("due_date"),
                "services": billing_record.get("services_provided", []),
                "notes": self._generate_invoice_notes(billing_record, patient_data, caregiver_data),
                "reference_number": f"CS-{billing_record['invoice_number']}"
            }
            
            # Create invoice in Zoho Books
            zoho_invoice = await self.zoho_service.create_invoice(invoice_data)
            
            # Update billing record with Zoho invoice ID
            await self.db_service.update(billing_id, {
                "zoho_invoice_id": zoho_invoice["invoice_id"],
                "status": "sent" if send_email else "created"
            })
            
            # Send invoice if requested
            if send_email and patient_data.get("email"):
                email_data = {
                    "to_emails": [patient_data["email"]],
                    "subject": f"Invoice {billing_record['invoice_number']} - CareSyncAI Homecare Services",
                    "body": self._generate_email_body(billing_record, patient_data)
                }
                
                await self.zoho_service.send_invoice(zoho_invoice["invoice_id"], email_data)
                
                # Update status to sent
                await self.db_service.update(billing_id, {"status": "sent"})
            
            logger.info(f"Invoice created and sent for billing {billing_id}")
            return {
                "billing_id": billing_id,
                "zoho_invoice_id": zoho_invoice["invoice_id"],
                "invoice_number": billing_record["invoice_number"],
                "status": "sent" if send_email else "created"
            }
        except Exception as e:
            logger.error(f"Error creating/sending invoice: {e}")
            raise
    
    async def mark_invoice_paid(
        self, 
        billing_id: str, 
        payment_date: Optional[date] = None,
        payment_method: Optional[str] = None
    ) -> bool:
        """Mark invoice as paid"""
        try:
            billing_record = await self.db_service.get_by_id(billing_id)
            if not billing_record:
                raise ValueError("Billing record not found")
            
            # Update in Zoho Books if invoice exists
            if billing_record.get("zoho_invoice_id"):
                await self.zoho_service.update_invoice_status(
                    billing_record["zoho_invoice_id"], 
                    "paid"
                )
            
            # Update local billing record
            update_data = {
                "status": "paid",
                "paid_date": payment_date or date.today(),
                "payment_method": payment_method
            }
            
            await self.db_service.update(billing_id, update_data)
            
            logger.info(f"Invoice {billing_id} marked as paid")
            return True
        except Exception as e:
            logger.error(f"Error marking invoice as paid: {e}")
            return False
    
    async def get_billing_records(
        self, 
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get billing records with filters"""
        try:
            return await self.db_service.get_all(
                filters=filters,
                limit=limit,
                offset=offset,
                order_by="created_at",
                ascending=False
            )
        except Exception as e:
            logger.error(f"Error getting billing records: {e}")
            raise
    
    async def get_patient_billing_summary(self, patient_id: str) -> Dict[str, Any]:
        """Get billing summary for a patient"""
        try:
            # Get all billing records for patient
            billing_records = await self.db_service.get_all(
                filters={"patient_id": patient_id}
            )
            
            # Calculate summary statistics
            total_billed = sum(record.get("total_amount", 0) for record in billing_records)
            total_paid = sum(
                record.get("total_amount", 0) 
                for record in billing_records 
                if record.get("status") == "paid"
            )
            outstanding = total_billed - total_paid
            
            overdue_records = [
                record for record in billing_records
                if (record.get("status") in ["sent", "overdue"] and 
                    record.get("due_date") and 
                    datetime.strptime(record["due_date"], "%Y-%m-%d").date() < date.today())
            ]
            
            return {
                "patient_id": patient_id,
                "total_billed": total_billed,
                "total_paid": total_paid,
                "outstanding_amount": outstanding,
                "overdue_amount": sum(record.get("total_amount", 0) for record in overdue_records),
                "total_invoices": len(billing_records),
                "paid_invoices": len([r for r in billing_records if r.get("status") == "paid"]),
                "overdue_invoices": len(overdue_records)
            }
        except Exception as e:
            logger.error(f"Error getting billing summary: {e}")
            raise
    
    async def generate_monthly_invoices(self, month: int, year: int) -> List[Dict[str, Any]]:
        """Generate monthly invoices for all active patients"""
        try:
            # Get all active patients
            patients_result = self.client.table("patients").select(
                "id, first_name, last_name, primary_caregiver_id"
            ).eq("status", "active").execute()
            
            generated_invoices = []
            
            for patient in patients_result.data:
                # Get care schedules for the month
                start_date = date(year, month, 1)
                if month == 12:
                    end_date = date(year + 1, 1, 1) - timedelta(days=1)
                else:
                    end_date = date(year, month + 1, 1) - timedelta(days=1)
                
                schedules_result = self.client.table("care_schedules").select(
                    "*"
                ).eq("patient_id", patient["id"]).gte(
                    "scheduled_date", start_date.isoformat()
                ).lte(
                    "scheduled_date", end_date.isoformat()
                ).eq("status", "completed").execute()
                
                if schedules_result.data:
                    # Calculate billing data
                    total_hours = 0
                    services = []
                    
                    for schedule in schedules_result.data:
                        if schedule.get("actual_start_time") and schedule.get("actual_end_time"):
                            start_time = datetime.fromisoformat(schedule["actual_start_time"])
                            end_time = datetime.fromisoformat(schedule["actual_end_time"])
                            hours = (end_time - start_time).total_seconds() / 3600
                            total_hours += hours
                            
                            services.append({
                                "service": schedule.get("service_type", "Homecare Service"),
                                "date": schedule["scheduled_date"],
                                "hours": hours,
                                "rate": 25.00,  # Default rate, should be configurable
                                "description": f"Care services on {schedule['scheduled_date']}"
                            })
                    
                    if total_hours > 0:
                        billing_data = {
                            "patient_id": patient["id"],
                            "caregiver_id": patient["primary_caregiver_id"],
                            "billing_period_start": start_date,
                            "billing_period_end": end_date,
                            "services_provided": services,
                            "hours_worked": total_hours,
                            "hourly_rate": 25.00,
                            "due_date": (end_date + timedelta(days=30)).isoformat()
                        }
                        
                        billing_record = await self.create_billing_record(
                            billing_data, 
                            "system"
                        )
                        generated_invoices.append(billing_record)
            
            logger.info(f"Generated {len(generated_invoices)} monthly invoices for {month}/{year}")
            return generated_invoices
        except Exception as e:
            logger.error(f"Error generating monthly invoices: {e}")
            raise
    
    async def _generate_invoice_number(self) -> str:
        """Generate unique invoice number"""
        try:
            year = date.today().year
            
            # Get the last invoice number for this year
            result = self.client.table("billing").select(
                "invoice_number"
            ).like("invoice_number", f"INV-{year}-%").order(
                "created_at", desc=True
            ).limit(1).execute()
            
            if result.data:
                last_number = result.data[0]["invoice_number"]
                sequence = int(last_number.split("-")[-1]) + 1
            else:
                sequence = 1
            
            return f"INV-{year}-{sequence:04d}"
        except Exception as e:
            logger.error(f"Error generating invoice number: {e}")
            return f"INV-{date.today().year}-{uuid.uuid4().hex[:4].upper()}"
    
    def _calculate_totals(self, billing_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate billing totals"""
        services = billing_data.get("services_provided", [])
        
        subtotal = 0
        for service in services:
            subtotal += service.get("hours", 0) * service.get("rate", 0)
        
        # Calculate tax (8% default, should be configurable)
        tax_rate = 0.08
        tax_amount = subtotal * tax_rate
        total_amount = subtotal + tax_amount
        
        billing_data.update({
            "subtotal": round(subtotal, 2),
            "tax_amount": round(tax_amount, 2),
            "total_amount": round(total_amount, 2)
        })
        
        return billing_data
    
    async def _get_patient_data(self, patient_id: str) -> Dict[str, Any]:
        """Get patient data for billing"""
        result = self.client.table("patients").select(
            "first_name, last_name, email, phone, address"
        ).eq("id", patient_id).execute()
        
        if result.data:
            return result.data[0]
        else:
            raise ValueError(f"Patient {patient_id} not found")
    
    async def _get_caregiver_data(self, caregiver_id: str) -> Dict[str, Any]:
        """Get caregiver data for billing"""
        result = self.client.table("caregivers").select(
            "first_name, last_name, email, phone"
        ).eq("id", caregiver_id).execute()
        
        if result.data:
            return result.data[0]
        else:
            raise ValueError(f"Caregiver {caregiver_id} not found")
    
    def _generate_invoice_notes(
        self, 
        billing_record: Dict[str, Any], 
        patient_data: Dict[str, Any],
        caregiver_data: Dict[str, Any]
    ) -> str:
        """Generate invoice notes"""
        return f"""
Homecare services provided to {patient_data['first_name']} {patient_data['last_name']}
Service Period: {billing_record['billing_period_start']} to {billing_record['billing_period_end']}
Primary Caregiver: {caregiver_data['first_name']} {caregiver_data['last_name']}
Total Hours: {billing_record.get('hours_worked', 0)}

Thank you for choosing CareSyncAI for your homecare needs.
        """.strip()
    
    def _generate_email_body(
        self, 
        billing_record: Dict[str, Any], 
        patient_data: Dict[str, Any]
    ) -> str:
        """Generate email body for invoice"""
        return f"""
Dear {patient_data['first_name']} {patient_data['last_name']},

Please find attached your invoice #{billing_record['invoice_number']} for homecare services provided during the period from {billing_record['billing_period_start']} to {billing_record['billing_period_end']}.

Invoice Details:
- Total Amount: ${billing_record.get('total_amount', 0):.2f}
- Due Date: {billing_record.get('due_date', 'N/A')}

If you have any questions about this invoice, please don't hesitate to contact us.

Thank you for choosing CareSyncAI.

Best regards,
CareSyncAI Billing Team
        """.strip()
