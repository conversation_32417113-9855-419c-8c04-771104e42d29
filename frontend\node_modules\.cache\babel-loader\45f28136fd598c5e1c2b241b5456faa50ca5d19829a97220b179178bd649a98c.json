{"ast": null, "code": "/**\n * A specialized version of `_.map` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction arrayMap(array, iteratee) {\n  var index = -1,\n    length = array == null ? 0 : array.length,\n    result = Array(length);\n  while (++index < length) {\n    result[index] = iteratee(array[index], index, array);\n  }\n  return result;\n}\nexport default arrayMap;", "map": {"version": 3, "names": ["arrayMap", "array", "iteratee", "index", "length", "result", "Array"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/node_modules/lodash-es/_arrayMap.js"], "sourcesContent": ["/**\n * A specialized version of `_.map` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction arrayMap(array, iteratee) {\n  var index = -1,\n      length = array == null ? 0 : array.length,\n      result = Array(length);\n\n  while (++index < length) {\n    result[index] = iteratee(array[index], index, array);\n  }\n  return result;\n}\n\nexport default arrayMap;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACjC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACG,MAAM;IACzCC,MAAM,GAAGC,KAAK,CAACF,MAAM,CAAC;EAE1B,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvBC,MAAM,CAACF,KAAK,CAAC,GAAGD,QAAQ,CAACD,KAAK,CAACE,KAAK,CAAC,EAAEA,KAAK,EAAEF,KAAK,CAAC;EACtD;EACA,OAAOI,MAAM;AACf;AAEA,eAAeL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}