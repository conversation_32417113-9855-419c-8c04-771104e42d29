{"ast": null, "code": "// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {}\n  onUnsubscribe() {}\n};\nexport { Subscribable };", "map": {"version": 3, "names": ["Subscribable", "constructor", "listeners", "Set", "subscribe", "bind", "listener", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size"], "sources": ["C:\\Users\\<USER>\\Desktop\\medisyn\\frontend\\node_modules\\@tanstack\\query-core\\src\\subscribable.ts"], "sourcesContent": ["export class Subscribable<TListener extends Function> {\n  protected listeners = new Set<TListener>()\n\n  constructor() {\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    this.listeners.add(listener)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(listener)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n"], "mappings": ";AAAO,IAAMA,YAAA,GAAN,MAA+C;EAGpDC,YAAA,EAAc;IAFd,KAAUC,SAAA,GAAY,mBAAIC,GAAA,CAAe;IAGvC,KAAKC,SAAA,GAAY,KAAKA,SAAA,CAAUC,IAAA,CAAK,IAAI;EAC3C;EAEAD,UAAUE,QAAA,EAAiC;IACzC,KAAKJ,SAAA,CAAUK,GAAA,CAAID,QAAQ;IAE3B,KAAKE,WAAA,CAAY;IAEjB,OAAO,MAAM;MACX,KAAKN,SAAA,CAAUO,MAAA,CAAOH,QAAQ;MAC9B,KAAKI,aAAA,CAAc;IACrB;EACF;EAEAC,aAAA,EAAwB;IACtB,OAAO,KAAKT,SAAA,CAAUU,IAAA,GAAO;EAC/B;EAEUJ,YAAA,EAAoB,CAE9B;EAEUE,cAAA,EAAsB,CAEhC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}