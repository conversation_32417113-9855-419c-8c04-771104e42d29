# Core ML/AI Dependencies
tensorflow==2.15.0
torch==2.1.1
transformers==4.36.0
scikit-learn==1.3.2
numpy==1.24.3
pandas==2.1.4

# NLP and Text Processing
nltk==3.8.1
spacy==3.7.2
textblob==0.17.1

# Data Processing
scipy==1.11.4
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Model Serving and API
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# Database and Storage
psycopg2-binary==2.9.9
sqlalchemy==2.0.23

# Utilities
python-dotenv==1.0.0
requests==2.31.0
joblib==1.3.2
pickle5==0.0.12

# Monitoring and Logging
mlflow==2.8.1
wandb==0.16.0

# Development and Testing
pytest==7.4.3
jupyter==1.0.0
ipykernel==6.26.0

# Health and Medical Specific
lifelines==0.27.8  # Survival analysis
pyhealth==1.1.5   # Healthcare ML library
