"""
NLP Analyzer for CareSyncAI
Analyzes caregiver notes and medical records for insights
"""

import re
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import numpy as np

# NLP libraries
try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    from textblob import TextBlob
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.chunk import ne_chunk
    from nltk.tag import pos_tag
except ImportError:
    print("Some NLP libraries not installed. Install with: pip install transformers textblob nltk")

logger = logging.getLogger(__name__)

class CaregiverNotesAnalyzer:
    """Analyzes caregiver notes for medical insights and concerns"""
    
    def __init__(self):
        self.sentiment_analyzer = None
        self.medical_ner = None
        self.concern_classifier = None
        self.medical_keywords = self._load_medical_keywords()
        self.urgency_keywords = self._load_urgency_keywords()
        
        # Initialize NLP models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize NLP models"""
        try:
            # Sentiment analysis
            self.sentiment_analyzer = pipeline(
                "sentiment-analysis",
                model="distilbert-base-uncased-finetuned-sst-2-english",
                return_all_scores=True
            )
            
            # Medical NER (using a general NER model - in production, use medical-specific)
            self.medical_ner = pipeline(
                "ner",
                model="dbmdz/bert-large-cased-finetuned-conll03-english",
                aggregation_strategy="simple"
            )
            
            logger.info("NLP models initialized successfully")
        except Exception as e:
            logger.warning(f"Could not initialize all NLP models: {e}")
            logger.info("Falling back to rule-based analysis")
    
    def _load_medical_keywords(self) -> Dict[str, List[str]]:
        """Load medical keywords for different categories"""
        return {
            'symptoms': [
                'pain', 'ache', 'hurt', 'sore', 'discomfort', 'nausea', 'dizzy', 'tired',
                'fatigue', 'weak', 'confusion', 'agitated', 'restless', 'anxious',
                'shortness of breath', 'difficulty breathing', 'chest pain', 'headache',
                'fever', 'chills', 'sweating', 'tremor', 'shaking', 'fall', 'fell'
            ],
            'mood': [
                'happy', 'sad', 'depressed', 'angry', 'frustrated', 'calm', 'peaceful',
                'agitated', 'confused', 'alert', 'responsive', 'withdrawn', 'social',
                'cooperative', 'uncooperative', 'cheerful', 'moody'
            ],
            'activities': [
                'walking', 'eating', 'drinking', 'sleeping', 'bathing', 'dressing',
                'toileting', 'medication', 'exercise', 'therapy', 'visit', 'appointment'
            ],
            'concerns': [
                'refused', 'declined', 'unable', 'difficulty', 'struggle', 'problem',
                'issue', 'concern', 'worried', 'emergency', 'urgent', 'immediate',
                'call doctor', 'hospital', 'ambulance'
            ]
        }
    
    def _load_urgency_keywords(self) -> List[str]:
        """Load keywords that indicate urgent situations"""
        return [
            'emergency', 'urgent', 'immediate', 'critical', 'severe', 'acute',
            'call doctor', 'call 911', 'hospital', 'ambulance', 'unresponsive',
            'unconscious', 'bleeding', 'chest pain', 'difficulty breathing',
            'severe pain', 'high fever', 'seizure', 'stroke', 'heart attack'
        ]
    
    def analyze_note(self, note_text: str, patient_context: Optional[Dict] = None) -> Dict:
        """Analyze a single caregiver note"""
        try:
            analysis = {
                'note_text': note_text,
                'timestamp': datetime.now().isoformat(),
                'sentiment': self._analyze_sentiment(note_text),
                'medical_entities': self._extract_medical_entities(note_text),
                'keywords': self._extract_keywords(note_text),
                'urgency_level': self._assess_urgency(note_text),
                'concerns': self._identify_concerns(note_text),
                'recommendations': self._generate_recommendations(note_text, patient_context),
                'summary': self._generate_summary(note_text)
            }
            
            return analysis
        except Exception as e:
            logger.error(f"Error analyzing note: {e}")
            raise
    
    def _analyze_sentiment(self, text: str) -> Dict:
        """Analyze sentiment of the note"""
        try:
            if self.sentiment_analyzer:
                # Use transformer model
                results = self.sentiment_analyzer(text)
                sentiment_scores = {result['label'].lower(): result['score'] for result in results[0]}
                
                # Determine overall sentiment
                if sentiment_scores.get('positive', 0) > 0.6:
                    overall = 'positive'
                elif sentiment_scores.get('negative', 0) > 0.6:
                    overall = 'negative'
                else:
                    overall = 'neutral'
                
                return {
                    'overall': overall,
                    'scores': sentiment_scores,
                    'confidence': max(sentiment_scores.values())
                }
            else:
                # Fallback to TextBlob
                blob = TextBlob(text)
                polarity = blob.sentiment.polarity
                
                if polarity > 0.1:
                    overall = 'positive'
                elif polarity < -0.1:
                    overall = 'negative'
                else:
                    overall = 'neutral'
                
                return {
                    'overall': overall,
                    'polarity': polarity,
                    'subjectivity': blob.sentiment.subjectivity
                }
        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            return {'overall': 'neutral', 'error': str(e)}
    
    def _extract_medical_entities(self, text: str) -> List[Dict]:
        """Extract medical entities from text"""
        try:
            entities = []
            
            if self.medical_ner:
                # Use transformer NER
                ner_results = self.medical_ner(text)
                for entity in ner_results:
                    entities.append({
                        'text': entity['word'],
                        'label': entity['entity_group'],
                        'confidence': entity['score'],
                        'start': entity['start'],
                        'end': entity['end']
                    })
            
            # Rule-based entity extraction for medical terms
            medical_entities = self._extract_medical_terms(text)
            entities.extend(medical_entities)
            
            return entities
        except Exception as e:
            logger.error(f"Error extracting entities: {e}")
            return []
    
    def _extract_medical_terms(self, text: str) -> List[Dict]:
        """Extract medical terms using rule-based approach"""
        entities = []
        text_lower = text.lower()
        
        for category, keywords in self.medical_keywords.items():
            for keyword in keywords:
                if keyword in text_lower:
                    start_idx = text_lower.find(keyword)
                    entities.append({
                        'text': keyword,
                        'label': category,
                        'confidence': 0.8,
                        'start': start_idx,
                        'end': start_idx + len(keyword)
                    })
        
        return entities
    
    def _extract_keywords(self, text: str) -> Dict[str, List[str]]:
        """Extract keywords by category"""
        keywords = {}
        text_lower = text.lower()
        
        for category, keyword_list in self.medical_keywords.items():
            found_keywords = [kw for kw in keyword_list if kw in text_lower]
            if found_keywords:
                keywords[category] = found_keywords
        
        return keywords
    
    def _assess_urgency(self, text: str) -> Dict:
        """Assess urgency level of the note"""
        text_lower = text.lower()
        urgency_score = 0
        found_urgent_terms = []
        
        for keyword in self.urgency_keywords:
            if keyword in text_lower:
                urgency_score += 1
                found_urgent_terms.append(keyword)
        
        # Determine urgency level
        if urgency_score >= 3:
            level = 'critical'
        elif urgency_score >= 2:
            level = 'high'
        elif urgency_score >= 1:
            level = 'medium'
        else:
            level = 'low'
        
        return {
            'level': level,
            'score': urgency_score,
            'urgent_terms': found_urgent_terms
        }
    
    def _identify_concerns(self, text: str) -> List[Dict]:
        """Identify specific concerns in the note"""
        concerns = []
        text_lower = text.lower()
        
        # Medication concerns
        if any(term in text_lower for term in ['refused medication', 'missed dose', 'medication error']):
            concerns.append({
                'type': 'medication',
                'description': 'Medication compliance issue detected',
                'severity': 'medium'
            })
        
        # Fall concerns
        if any(term in text_lower for term in ['fall', 'fell', 'tripped', 'stumbled']):
            concerns.append({
                'type': 'fall_incident',
                'description': 'Fall or fall risk incident reported',
                'severity': 'high'
            })
        
        # Pain concerns
        if any(term in text_lower for term in ['severe pain', 'extreme pain', 'unbearable']):
            concerns.append({
                'type': 'pain',
                'description': 'Severe pain reported',
                'severity': 'high'
            })
        
        # Behavioral concerns
        if any(term in text_lower for term in ['agitated', 'aggressive', 'confused', 'disoriented']):
            concerns.append({
                'type': 'behavioral',
                'description': 'Behavioral changes observed',
                'severity': 'medium'
            })
        
        # Vital signs concerns
        if any(term in text_lower for term in ['high blood pressure', 'low blood pressure', 'irregular heartbeat']):
            concerns.append({
                'type': 'vitals',
                'description': 'Abnormal vital signs noted',
                'severity': 'medium'
            })
        
        return concerns
    
    def _generate_recommendations(self, text: str, patient_context: Optional[Dict] = None) -> List[str]:
        """Generate recommendations based on note analysis"""
        recommendations = []
        text_lower = text.lower()
        
        # Medication recommendations
        if 'refused medication' in text_lower:
            recommendations.append("Consider alternative medication administration methods")
            recommendations.append("Discuss medication concerns with patient and family")
        
        # Fall prevention recommendations
        if any(term in text_lower for term in ['fall', 'unsteady', 'balance']):
            recommendations.append("Implement fall prevention measures")
            recommendations.append("Consider physical therapy evaluation")
        
        # Pain management recommendations
        if any(term in text_lower for term in ['pain', 'discomfort', 'ache']):
            recommendations.append("Assess pain levels and consider pain management strategies")
            recommendations.append("Monitor pain medication effectiveness")
        
        # Mood and behavioral recommendations
        if any(term in text_lower for term in ['sad', 'depressed', 'withdrawn']):
            recommendations.append("Monitor mood and consider mental health support")
            recommendations.append("Encourage social activities and engagement")
        
        # General care recommendations
        if 'difficulty' in text_lower:
            recommendations.append("Provide additional assistance with identified activities")
            recommendations.append("Consider care plan modifications")
        
        return recommendations
    
    def _generate_summary(self, text: str) -> str:
        """Generate a brief summary of the note"""
        try:
            # Simple extractive summary - take first sentence and key points
            sentences = sent_tokenize(text)
            
            if len(sentences) <= 2:
                return text
            
            # Take first sentence and any sentence with key medical terms
            summary_sentences = [sentences[0]]
            
            for sentence in sentences[1:]:
                sentence_lower = sentence.lower()
                if any(keyword in sentence_lower for keywords in self.medical_keywords.values() for keyword in keywords):
                    summary_sentences.append(sentence)
                    if len(summary_sentences) >= 3:
                        break
            
            return ' '.join(summary_sentences)
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return text[:200] + "..." if len(text) > 200 else text
    
    def analyze_multiple_notes(self, notes: List[Dict]) -> Dict:
        """Analyze multiple notes for trends and patterns"""
        try:
            analyses = []
            for note in notes:
                analysis = self.analyze_note(note['text'], note.get('patient_context'))
                analysis['note_id'] = note.get('id')
                analysis['date'] = note.get('date')
                analyses.append(analysis)
            
            # Aggregate analysis
            trends = self._identify_trends(analyses)
            
            return {
                'individual_analyses': analyses,
                'trends': trends,
                'total_notes': len(notes),
                'analysis_date': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error analyzing multiple notes: {e}")
            raise
    
    def _identify_trends(self, analyses: List[Dict]) -> Dict:
        """Identify trends across multiple note analyses"""
        trends = {
            'sentiment_trend': self._analyze_sentiment_trend(analyses),
            'recurring_concerns': self._find_recurring_concerns(analyses),
            'urgency_pattern': self._analyze_urgency_pattern(analyses),
            'keyword_frequency': self._analyze_keyword_frequency(analyses)
        }
        
        return trends
    
    def _analyze_sentiment_trend(self, analyses: List[Dict]) -> Dict:
        """Analyze sentiment trends over time"""
        sentiments = [analysis['sentiment']['overall'] for analysis in analyses]
        
        positive_count = sentiments.count('positive')
        negative_count = sentiments.count('negative')
        neutral_count = sentiments.count('neutral')
        
        total = len(sentiments)
        
        return {
            'positive_percentage': (positive_count / total) * 100 if total > 0 else 0,
            'negative_percentage': (negative_count / total) * 100 if total > 0 else 0,
            'neutral_percentage': (neutral_count / total) * 100 if total > 0 else 0,
            'trend': 'improving' if positive_count > negative_count else 'declining' if negative_count > positive_count else 'stable'
        }
    
    def _find_recurring_concerns(self, analyses: List[Dict]) -> List[Dict]:
        """Find concerns that appear in multiple notes"""
        concern_counts = {}
        
        for analysis in analyses:
            for concern in analysis.get('concerns', []):
                concern_type = concern['type']
                if concern_type in concern_counts:
                    concern_counts[concern_type] += 1
                else:
                    concern_counts[concern_type] = 1
        
        # Return concerns that appear in multiple notes
        recurring = [
            {'type': concern_type, 'frequency': count}
            for concern_type, count in concern_counts.items()
            if count > 1
        ]
        
        return sorted(recurring, key=lambda x: x['frequency'], reverse=True)
    
    def _analyze_urgency_pattern(self, analyses: List[Dict]) -> Dict:
        """Analyze urgency patterns"""
        urgency_levels = [analysis['urgency_level']['level'] for analysis in analyses]
        
        critical_count = urgency_levels.count('critical')
        high_count = urgency_levels.count('high')
        medium_count = urgency_levels.count('medium')
        low_count = urgency_levels.count('low')
        
        total = len(urgency_levels)
        
        return {
            'critical_percentage': (critical_count / total) * 100 if total > 0 else 0,
            'high_percentage': (high_count / total) * 100 if total > 0 else 0,
            'medium_percentage': (medium_count / total) * 100 if total > 0 else 0,
            'low_percentage': (low_count / total) * 100 if total > 0 else 0,
            'average_urgency': 'high' if (critical_count + high_count) > (medium_count + low_count) else 'low'
        }
    
    def _analyze_keyword_frequency(self, analyses: List[Dict]) -> Dict:
        """Analyze keyword frequency across notes"""
        keyword_counts = {}
        
        for analysis in analyses:
            for category, keywords in analysis.get('keywords', {}).items():
                for keyword in keywords:
                    if keyword in keyword_counts:
                        keyword_counts[keyword] += 1
                    else:
                        keyword_counts[keyword] = 1
        
        # Return top 10 most frequent keywords
        top_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            'top_keywords': [{'keyword': kw, 'frequency': freq} for kw, freq in top_keywords],
            'total_unique_keywords': len(keyword_counts)
        }

# Example usage
if __name__ == "__main__":
    analyzer = CaregiverNotesAnalyzer()
    
    # Example note
    note_text = """
    Patient seemed more confused today than usual. Had difficulty remembering 
    to take morning medications. Complained of mild pain in left knee. 
    Mood appeared somewhat agitated during personal care. Refused to eat lunch. 
    Will monitor closely and contact family if condition worsens.
    """
    
    analysis = analyzer.analyze_note(note_text)
    print(f"Note analysis: {analysis}")
