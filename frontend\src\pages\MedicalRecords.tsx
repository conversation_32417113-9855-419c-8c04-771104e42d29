import React from 'react';

const MedicalRecords: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Medical Records</h1>
        <p className="mt-1 text-sm text-gray-600">
          Track vitals, medications, and care notes
        </p>
      </div>

      <div className="card">
        <div className="card-body">
          <div className="text-center py-12">
            <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-gray-400 text-xl">📋</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Medical Records Management
            </h3>
            <p className="text-gray-600 mb-4">
              Comprehensive medical record tracking with AI-powered insights.
            </p>
            <p className="text-sm text-gray-500">
              Features: Vital signs tracking, medication logs, care notes,
              incident reports, and health trend analysis.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MedicalRecords;
