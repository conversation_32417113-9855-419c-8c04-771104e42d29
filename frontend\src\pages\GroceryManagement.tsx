import React, { useState } from 'react';
import {
  ShoppingCartIcon,
  CubeIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlusIcon,
  EyeIcon,
  CogIcon,
  TruckIcon,
  CurrencyDollarIcon,
  CalendarDaysIcon,
  BuildingStorefrontIcon,
  BeakerIcon,
} from '@heroicons/react/24/outline';

interface SupplyItem {
  id: string;
  name: string;
  category: 'food_groceries' | 'cleaning_supplies' | 'personal_care' | 'medical_supplies' | 'linens_bedding' | 'furniture' | 'maintenance' | 'office_supplies' | 'safety_equipment' | 'recreational' | 'kitchen_equipment' | 'laundry_supplies';
  subcategory?: string;
  brand?: string;
  unit: 'lbs' | 'oz' | 'gallons' | 'quarts' | 'pieces' | 'boxes' | 'cans' | 'bags' | 'rolls' | 'bottles' | 'packs';
  unitSize: string;
  currentStock: number;
  minimumStock: number;
  maximumStock: number;
  averageConsumption: number; // per week
  lastOrderDate: string;
  lastOrderQuantity: number;
  expirationDate?: string;
  cost: number;
  vendor: string;
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    sodium: number;
    allergens: string[];
  };
  dietaryRestrictions: string[];
  storageRequirements: 'refrigerated' | 'frozen' | 'dry' | 'room_temperature' | 'climate_controlled' | 'secure_storage';
  isEssential: boolean;
  autoReorder: boolean;
  complianceRequired: boolean;
  certifications: string[];
  safetyDataSheet?: string;
}

interface PurchaseOrder {
  id: string;
  orderNumber: string;
  vendor: string;
  orderDate: string;
  expectedDelivery: string;
  status: 'draft' | 'submitted' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  orderType: 'routine' | 'emergency' | 'bulk' | 'special_order';
  items: Array<{
    itemId: string;
    itemName: string;
    category: string;
    quantity: number;
    unitCost: number;
    totalCost: number;
    urgency: 'low' | 'medium' | 'high' | 'critical';
    complianceRequired?: boolean;
    certificationNeeded?: string[];
  }>;
  totalAmount: number;
  deliveryInstructions?: string;
  specialRequests?: string;
  approvedBy?: string;
  approvalDate?: string;
  receivedBy?: string;
  receivedDate?: string;
  discrepancies?: Array<{
    itemId: string;
    expected: number;
    received: number;
    reason: string;
  }>;
  budgetCategory: string;
  departmentCode: string;
}

interface Vendor {
  id: string;
  name: string;
  type: 'grocery_chain' | 'wholesale' | 'specialty' | 'local_farm' | 'medical_supply' | 'cleaning_supply' | 'furniture' | 'maintenance' | 'office_supply' | 'safety_equipment';
  contactInfo: {
    phone: string;
    email: string;
    address: string;
    website?: string;
    accountManager?: string;
  };
  deliverySchedule: {
    days: string[];
    timeSlots: string[];
    minimumOrder: number;
    deliveryFee: number;
    freeDeliveryThreshold: number;
    emergencyDelivery: boolean;
  };
  paymentTerms: {
    method: 'credit_card' | 'net_30' | 'net_15' | 'cod';
    discounts: Array<{
      condition: string;
      percentage: number;
    }>;
  };
  reliability: {
    onTimeDelivery: number; // percentage
    orderAccuracy: number; // percentage
    qualityRating: number; // 1-5
  };
  specialties: string[];
  certifications: string[];
  complianceLevel: 'basic' | 'healthcare' | 'medical_grade';
  isPreferred: boolean;
}

interface InventoryAlert {
  id: string;
  type: 'low_stock' | 'expiring_soon' | 'expired' | 'overstock' | 'missing_item';
  severity: 'low' | 'medium' | 'high' | 'critical';
  itemId: string;
  itemName: string;
  message: string;
  currentStock?: number;
  minimumStock?: number;
  expirationDate?: string;
  daysUntilExpiry?: number;
  suggestedAction: string;
  createdDate: string;
  acknowledged: boolean;
  acknowledgedBy?: string;
  resolvedDate?: string;
}

const GroceryManagement: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'ordering' | 'inventory' | 'vendors' | 'budget'>('overview');

  // Modal and cart states
  const [showCreateOrderModal, setShowCreateOrderModal] = useState(false);
  const [showAISuggestionsModal, setShowAISuggestionsModal] = useState(false);
  const [showCustomizeModal, setShowCustomizeModal] = useState(false);
  const [showCartModal, setShowCartModal] = useState(false);
  const [cart, setCart] = useState<Array<SupplyItem & { quantity: number; customizations?: string }>>([]);
  const [selectedItem, setSelectedItem] = useState<SupplyItem | null>(null);

  // Mock data for supply items
  const supplyItems: SupplyItem[] = [
    {
      id: 'SI001',
      name: 'Whole Milk',
      category: 'food_groceries',
      subcategory: 'dairy',
      brand: 'Local Dairy Co.',
      unit: 'gallons',
      unitSize: '1 gallon',
      currentStock: 8,
      minimumStock: 12,
      maximumStock: 24,
      averageConsumption: 15,
      lastOrderDate: '2024-01-18',
      lastOrderQuantity: 20,
      expirationDate: '2024-01-25',
      cost: 3.99,
      vendor: 'FreshMart Grocery',
      nutritionalInfo: {
        calories: 150,
        protein: 8,
        carbs: 12,
        fat: 8,
        sodium: 105,
        allergens: ['dairy']
      },
      dietaryRestrictions: ['lactose_free'],
      storageRequirements: 'refrigerated',
      isEssential: true,
      autoReorder: true,
      complianceRequired: false,
      certifications: ['USDA Organic']
    },
    {
      id: 'SI002',
      name: 'Disinfectant Wipes',
      category: 'cleaning_supplies',
      subcategory: 'disinfectants',
      brand: 'MediClean Pro',
      unit: 'packs',
      unitSize: '80-count pack',
      currentStock: 15,
      minimumStock: 25,
      maximumStock: 50,
      averageConsumption: 20,
      lastOrderDate: '2024-01-19',
      lastOrderQuantity: 30,
      cost: 8.99,
      vendor: 'Healthcare Supply Co.',
      dietaryRestrictions: [],
      storageRequirements: 'room_temperature',
      isEssential: true,
      autoReorder: true,
      complianceRequired: true,
      certifications: ['EPA Approved', 'Healthcare Grade'],
      safetyDataSheet: 'SDS-MC-001'
    },
    {
      id: 'SI003',
      name: 'Bed Sheets - Twin XL',
      category: 'linens_bedding',
      subcategory: 'bed_linens',
      brand: 'ComfortCare',
      unit: 'pieces',
      unitSize: 'Twin XL fitted sheet',
      currentStock: 12,
      minimumStock: 20,
      maximumStock: 40,
      averageConsumption: 8,
      lastOrderDate: '2024-01-15',
      lastOrderQuantity: 25,
      cost: 15.99,
      vendor: 'Healthcare Linens Supply',
      dietaryRestrictions: [],
      storageRequirements: 'dry',
      isEssential: true,
      autoReorder: true,
      complianceRequired: false,
      certifications: ['Fire Retardant', 'Antimicrobial']
    },
    {
      id: 'SI004',
      name: 'Blood Pressure Cuffs',
      category: 'medical_supplies',
      subcategory: 'monitoring_equipment',
      brand: 'MedTech Pro',
      unit: 'pieces',
      unitSize: 'Adult standard cuff',
      currentStock: 3,
      minimumStock: 5,
      maximumStock: 10,
      averageConsumption: 1,
      lastOrderDate: '2024-01-10',
      lastOrderQuantity: 5,
      cost: 45.99,
      vendor: 'Medical Equipment Supply',
      dietaryRestrictions: [],
      storageRequirements: 'room_temperature',
      isEssential: true,
      autoReorder: false,
      complianceRequired: true,
      certifications: ['FDA Approved', 'Medical Grade']
    },
    {
      id: 'SI005',
      name: 'Toilet Paper - 2-Ply',
      category: 'personal_care',
      subcategory: 'hygiene',
      brand: 'SoftCare',
      unit: 'rolls',
      unitSize: '12-pack',
      currentStock: 8,
      minimumStock: 15,
      maximumStock: 30,
      averageConsumption: 12,
      lastOrderDate: '2024-01-18',
      lastOrderQuantity: 20,
      cost: 12.99,
      vendor: 'Bulk Supply Warehouse',
      dietaryRestrictions: [],
      storageRequirements: 'dry',
      isEssential: true,
      autoReorder: true,
      complianceRequired: false,
      certifications: ['Septic Safe']
    }
  ];

  // Mock data for recent orders
  const recentOrders: PurchaseOrder[] = [
    {
      id: 'GO001',
      orderNumber: 'ORD-2024-001',
      vendor: 'FreshMart Grocery',
      orderDate: '2024-01-20',
      expectedDelivery: '2024-01-22',
      status: 'confirmed',
      orderType: 'routine',
      items: [
        {
          itemId: 'SI001',
          itemName: 'Whole Milk',
          category: 'food_groceries',
          quantity: 20,
          unitCost: 3.99,
          totalCost: 79.80,
          urgency: 'high',
          complianceRequired: false
        },
        {
          itemId: 'SI006',
          itemName: 'Bread - Whole Wheat',
          category: 'food_groceries',
          quantity: 15,
          unitCost: 2.99,
          totalCost: 44.85,
          urgency: 'medium',
          complianceRequired: false
        }
      ],
      totalAmount: 124.65,
      deliveryInstructions: 'Deliver to kitchen entrance',
      approvedBy: 'Kitchen Manager',
      approvalDate: '2024-01-20',
      budgetCategory: 'Food & Nutrition',
      departmentCode: 'KITCHEN'
    },
    {
      id: 'GO002',
      orderNumber: 'ORD-2024-002',
      vendor: 'Wholesale Meats Inc.',
      orderDate: '2024-01-19',
      expectedDelivery: '2024-01-21',
      status: 'delivered',
      orderType: 'routine',
      items: [
        {
          itemId: 'SI007',
          itemName: 'Ground Turkey',
          category: 'food_groceries',
          quantity: 15,
          unitCost: 5.99,
          totalCost: 89.85,
          urgency: 'high',
          complianceRequired: false
        }
      ],
      totalAmount: 89.85,
      receivedBy: 'Kitchen Staff',
      receivedDate: '2024-01-21',
      budgetCategory: 'Food & Nutrition',
      departmentCode: 'KITCHEN'
    }
  ];

  // Mock inventory alerts
  const inventoryAlerts: InventoryAlert[] = [
    {
      id: 'IA001',
      type: 'low_stock',
      severity: 'high',
      itemId: 'GI001',
      itemName: 'Whole Milk',
      message: 'Stock level below minimum threshold',
      currentStock: 8,
      minimumStock: 12,
      suggestedAction: 'Reorder 20 gallons immediately',
      createdDate: '2024-01-20T08:00:00Z',
      acknowledged: false
    },
    {
      id: 'IA002',
      type: 'expiring_soon',
      severity: 'medium',
      itemId: 'GI003',
      itemName: 'Fresh Spinach',
      message: 'Items expiring within 2 days',
      expirationDate: '2024-01-22',
      daysUntilExpiry: 2,
      suggestedAction: 'Use in upcoming meals or donate',
      createdDate: '2024-01-20T09:30:00Z',
      acknowledged: true,
      acknowledgedBy: 'Kitchen Manager'
    },
    {
      id: 'IA003',
      type: 'low_stock',
      severity: 'critical',
      itemId: 'GI002',
      itemName: 'Ground Turkey',
      message: 'Critical stock level - immediate action required',
      currentStock: 5,
      minimumStock: 8,
      suggestedAction: 'Emergency order needed today',
      createdDate: '2024-01-20T10:15:00Z',
      acknowledged: false
    }
  ];

  const getStockLevelColor = (current: number, minimum: number, maximum: number) => {
    const percentage = (current / maximum) * 100;
    if (current <= minimum) return 'bg-red-500';
    if (percentage <= 30) return 'bg-yellow-500';
    if (percentage <= 70) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered':
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'shipped':
        return 'bg-blue-100 text-blue-800';
      case 'submitted':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  // Handler functions for buttons
  const handleCreateOrder = () => {
    setShowCreateOrderModal(true);
  };

  const handleAISuggestions = () => {
    setShowAISuggestionsModal(true);
  };

  const handleCustomize = (item: SupplyItem) => {
    setSelectedItem(item);
    setShowCustomizeModal(true);
  };

  const handleAddToCart = (item: SupplyItem, quantity: number = 1, customizations?: string) => {
    const existingItem = cart.find(cartItem => cartItem.id === item.id);

    if (existingItem) {
      setCart(cart.map(cartItem =>
        cartItem.id === item.id
          ? { ...cartItem, quantity: cartItem.quantity + quantity, customizations }
          : cartItem
      ));
    } else {
      setCart([...cart, { ...item, quantity, customizations }]);
    }

    alert(`${item.name} added to cart!`);
  };

  const handleViewCart = () => {
    setShowCartModal(true);
  };

  const removeFromCart = (itemId: string) => {
    setCart(cart.filter(item => item.id !== itemId));
  };

  const updateCartQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setCart(cart.map(item =>
      item.id === itemId ? { ...item, quantity } : item
    ));
  };

  const getTotalCartValue = () => {
    return cart.reduce((total, item) => total + (item.cost * item.quantity), 0);
  };

  const getTotalCartItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'ordering', name: 'Smart Ordering', icon: ShoppingCartIcon },
    { id: 'inventory', name: 'Inventory', icon: CubeIcon },
    { id: 'vendors', name: 'Vendors', icon: BuildingStorefrontIcon },
    { id: 'budget', name: 'Budget & Analytics', icon: CurrencyDollarIcon },
  ];

  return (
    <div className="space-y-6">
      {/* Ultra-Interactive Emblem Header */}
      <div className="relative overflow-hidden bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-3xl p-10 text-white shadow-2xl border-4 border-gradient-to-r from-amber-400 via-yellow-500 to-amber-600">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-800/30 via-blue-800/30 to-indigo-800/30"></div>
        <div className="absolute top-0 right-0 w-80 h-80 bg-gradient-to-bl from-amber-400/20 to-yellow-500/20 rounded-full animate-pulse transform translate-x-40 -translate-y-40"></div>
        <div className="absolute bottom-0 left-0 w-60 h-60 bg-gradient-to-tr from-purple-400/20 to-blue-400/20 rounded-full animate-bounce transform -translate-x-30 translate-y-30"></div>
        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-gradient-to-r from-yellow-400/10 to-amber-500/10 rounded-full animate-spin transform -translate-x-16 -translate-y-16"></div>

        {/* Interactive Floating Orbs */}
        <div className="absolute top-20 right-20 w-4 h-4 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full animate-ping"></div>
        <div className="absolute bottom-20 left-20 w-3 h-3 bg-gradient-to-r from-purple-400 to-blue-500 rounded-full animate-pulse"></div>
        <div className="absolute top-40 left-40 w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-bounce"></div>

        <div className="relative z-10 flex justify-between items-start">
          <div className="flex-1">
            {/* Interactive Logo Section */}
            <div className="flex items-center mb-6 group cursor-pointer" onClick={() => alert('Care-SolAI - Next Generation Healthcare Management')}>
              <div className="relative p-4 bg-gradient-to-br from-amber-400 via-yellow-500 to-amber-600 rounded-2xl mr-6 shadow-2xl group-hover:scale-110 transition-all duration-300 border-2 border-yellow-300">
                <ShoppingCartIcon className="h-10 w-10 text-purple-900 group-hover:rotate-12 transition-transform duration-300" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-pulse"></div>
              </div>
              <div className="group-hover:scale-105 transition-all duration-300">
                <h1 className="text-4xl font-black bg-gradient-to-r from-amber-300 via-yellow-400 to-amber-300 bg-clip-text text-transparent animate-pulse">
                  Care-SolAI
                </h1>
                <h2 className="text-xl font-bold bg-gradient-to-r from-blue-200 via-purple-200 to-blue-200 bg-clip-text text-transparent">
                  Supply Chain Intelligence
                </h2>
                <div className="flex items-center mt-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                  <span className="text-green-300 text-sm font-medium">AI Systems Online</span>
                </div>
              </div>
            </div>

            <p className="text-blue-100 max-w-3xl leading-relaxed text-lg mb-6">
              Revolutionary AI-powered supply management ecosystem designed for next-generation healthcare facilities.
              Experience intelligent procurement, predictive analytics, and seamless inventory optimization.
            </p>

            {/* Interactive Feature Badges */}
            <div className="flex flex-wrap gap-4">
              <button className="group relative overflow-hidden px-6 py-3 rounded-2xl bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-bold shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-purple-400">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <BeakerIcon className="h-5 w-5 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  <span>AI Optimization</span>
                </div>
              </button>

              <button className="group relative overflow-hidden px-6 py-3 rounded-2xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white font-bold shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-blue-400">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <CubeIcon className="h-5 w-5 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  <span>Smart Inventory</span>
                </div>
              </button>

              <button className="group relative overflow-hidden px-6 py-3 rounded-2xl bg-gradient-to-r from-amber-500 to-yellow-600 hover:from-amber-400 hover:to-yellow-500 text-purple-900 font-bold shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-yellow-400">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <CurrencyDollarIcon className="h-5 w-5 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  <span>Budget Intelligence</span>
                </div>
              </button>

              <button className="group relative overflow-hidden px-6 py-3 rounded-2xl bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-500 hover:to-purple-600 text-white font-bold shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-indigo-400">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <CheckCircleIcon className="h-5 w-5 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  <span>Healthcare Compliance</span>
                </div>
              </button>
            </div>
          </div>

          {/* Ultra-Interactive Cart Section */}
          <div className="flex flex-col items-end space-y-6 ml-8">
            <button
              onClick={handleViewCart}
              className="group relative overflow-hidden px-8 py-4 bg-gradient-to-br from-amber-400 via-yellow-500 to-amber-600 text-purple-900 font-black rounded-2xl hover:from-yellow-400 hover:to-amber-400 transform hover:scale-110 hover:rotate-1 transition-all duration-300 shadow-2xl border-4 border-yellow-300"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center">
                <ShoppingCartIcon className="h-8 w-8 mr-4 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300" />
                <div className="text-left">
                  <div className="text-lg font-black">Shopping Cart</div>
                  <div className="text-sm font-bold opacity-90">
                    {getTotalCartItems() > 0 ? `${getTotalCartItems()} items • $${getTotalCartValue().toFixed(2)}` : 'Ready to Shop'}
                  </div>
                </div>
              </div>
              {getTotalCartItems() > 0 && (
                <span className="absolute -top-3 -right-3 inline-flex items-center justify-center px-3 py-2 text-sm font-black leading-none text-white bg-gradient-to-r from-purple-600 to-blue-600 rounded-full shadow-xl animate-bounce border-2 border-white">
                  {getTotalCartItems()}
                </span>
              )}
            </button>

            {/* Interactive System Status */}
            <div className="group text-right text-blue-100 cursor-pointer hover:scale-105 transition-all duration-300" onClick={() => alert('All Care-SolAI systems operational and optimized!')}>
              <div className="flex items-center justify-end mb-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse mr-2"></div>
                <div className="text-lg font-bold group-hover:text-amber-300 transition-colors duration-300">System Status</div>
              </div>
              <div className="text-sm opacity-90 group-hover:text-yellow-300 transition-colors duration-300">All systems operational</div>
              <div className="text-xs opacity-75 group-hover:text-amber-200 transition-colors duration-300">AI optimization active</div>
            </div>

            {/* Real-time Activity Indicator */}
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-amber-400 rounded-full animate-ping"></div>
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
              <span className="text-xs text-blue-200 ml-2">Live Activity</span>
            </div>
          </div>
        </div>
      </div>
      {/* Ultra-Interactive Emblem Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
        {/* Cart Items Card - Blue Emblem */}
        <div
          className="group relative overflow-hidden bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-900 rounded-3xl p-8 text-white shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-3 hover:rotate-1 transition-all duration-500 cursor-pointer border-4 border-blue-400/30 hover:border-amber-400"
          onClick={() => handleViewCart()}
        >
          {/* Animated Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-indigo-600/20 to-blue-800/20"></div>
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-amber-400/30 to-yellow-500/30 rounded-full opacity-0 group-hover:opacity-100 transform translate-x-8 -translate-y-8 group-hover:animate-pulse transition-all duration-500"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-blue-300/20 to-purple-400/20 rounded-full animate-bounce"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <div className="relative p-4 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-2xl shadow-xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-2 border-yellow-300">
                <ShoppingCartIcon className="h-8 w-8 text-blue-900 group-hover:text-purple-900 transition-colors duration-300" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-ping"></div>
              </div>
              <div className="text-right group-hover:scale-110 transition-all duration-300">
                <div className="text-4xl font-black bg-gradient-to-r from-amber-300 to-yellow-400 bg-clip-text text-transparent group-hover:animate-pulse">
                  {getTotalCartItems()}
                </div>
                <div className="text-blue-200 text-sm font-bold group-hover:text-amber-200 transition-colors duration-300">Items in Cart</div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="text-blue-100 text-sm font-medium group-hover:text-amber-100 transition-colors duration-300">
                {cart.length > 0 ? `Total Value: $${getTotalCartValue().toFixed(2)}` : 'Ready to add items'}
              </div>
              <div className="w-full bg-blue-800/50 rounded-full h-3 border border-blue-600">
                <div
                  className="bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 h-3 rounded-full transition-all duration-1000 shadow-lg group-hover:animate-pulse"
                  style={{ width: `${Math.min((getTotalCartItems() / 10) * 100, 100)}%` }}
                ></div>
              </div>
              <div className="text-xs text-blue-300 group-hover:text-yellow-300 transition-colors duration-300">
                Click to view cart details
              </div>
            </div>
          </div>
        </div>

        {/* Available Items Card - Purple Emblem */}
        <div
          className="group relative overflow-hidden bg-gradient-to-br from-purple-600 via-purple-700 to-violet-900 rounded-3xl p-8 text-white shadow-2xl hover:shadow-purple-500/25 transform hover:-translate-y-3 hover:rotate-1 transition-all duration-500 cursor-pointer border-4 border-purple-400/30 hover:border-amber-400"
          onClick={() => setSelectedTab('inventory')}
        >
          {/* Animated Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 via-violet-600/20 to-purple-800/20"></div>
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-amber-400/30 to-yellow-500/30 rounded-full opacity-0 group-hover:opacity-100 transform translate-x-8 -translate-y-8 group-hover:animate-pulse transition-all duration-500"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-purple-300/20 to-blue-400/20 rounded-full animate-bounce"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <div className="relative p-4 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-2xl shadow-xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-2 border-yellow-300">
                <CubeIcon className="h-8 w-8 text-purple-900 group-hover:text-blue-900 transition-colors duration-300" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-ping"></div>
              </div>
              <div className="text-right group-hover:scale-110 transition-all duration-300">
                <div className="text-4xl font-black bg-gradient-to-r from-amber-300 to-yellow-400 bg-clip-text text-transparent group-hover:animate-pulse">
                  {supplyItems.length}
                </div>
                <div className="text-purple-200 text-sm font-bold group-hover:text-amber-200 transition-colors duration-300">Available Items</div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="text-purple-100 text-sm font-medium group-hover:text-amber-100 transition-colors duration-300">
                {supplyItems.filter(item => item.currentStock > item.minimumStock).length} well stocked items
              </div>
              <div className="w-full bg-purple-800/50 rounded-full h-3 border border-purple-600">
                <div
                  className="bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 h-3 rounded-full transition-all duration-1000 shadow-lg group-hover:animate-pulse"
                  style={{ width: `${(supplyItems.filter(item => item.currentStock > item.minimumStock).length / supplyItems.length) * 100}%` }}
                ></div>
              </div>
              <div className="text-xs text-purple-300 group-hover:text-yellow-300 transition-colors duration-300">
                Click to manage inventory
              </div>
            </div>
          </div>
        </div>

        {/* Low Stock Alerts Card - Gold Emblem */}
        <div
          className="group relative overflow-hidden bg-gradient-to-br from-amber-500 via-yellow-600 to-orange-700 rounded-3xl p-8 text-white shadow-2xl hover:shadow-amber-500/25 transform hover:-translate-y-3 hover:rotate-1 transition-all duration-500 cursor-pointer border-4 border-amber-400/50 hover:border-purple-400"
          onClick={() => handleAISuggestions()}
        >
          {/* Animated Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-amber-400/20 via-yellow-500/20 to-orange-600/20"></div>
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-purple-400/30 to-blue-500/30 rounded-full opacity-0 group-hover:opacity-100 transform translate-x-8 -translate-y-8 group-hover:animate-pulse transition-all duration-500"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-yellow-300/20 to-amber-400/20 rounded-full animate-bounce"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <div className="relative p-4 bg-gradient-to-br from-purple-600 to-blue-700 rounded-2xl shadow-xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-2 border-purple-400">
                <ExclamationTriangleIcon className="h-8 w-8 text-amber-300 group-hover:text-yellow-300 transition-colors duration-300 animate-pulse" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-red-500 to-orange-500 rounded-full animate-ping"></div>
              </div>
              <div className="text-right group-hover:scale-110 transition-all duration-300">
                <div className="text-4xl font-black bg-gradient-to-r from-purple-300 to-blue-400 bg-clip-text text-transparent group-hover:animate-pulse">
                  {supplyItems.filter(item => item.currentStock <= item.minimumStock).length}
                </div>
                <div className="text-yellow-100 text-sm font-bold group-hover:text-purple-200 transition-colors duration-300">Stock Alerts</div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="text-yellow-100 text-sm font-medium group-hover:text-purple-100 transition-colors duration-300">
                {supplyItems.filter(item => item.currentStock === 0).length} critical shortages
              </div>
              <div className="w-full bg-yellow-700/50 rounded-full h-3 border border-yellow-600">
                <div
                  className="bg-gradient-to-r from-purple-500 via-blue-600 to-purple-500 h-3 rounded-full transition-all duration-1000 shadow-lg group-hover:animate-pulse"
                  style={{ width: `${Math.min((supplyItems.filter(item => item.currentStock <= item.minimumStock).length / supplyItems.length) * 100, 100)}%` }}
                ></div>
              </div>
              <div className="text-xs text-yellow-200 group-hover:text-purple-200 transition-colors duration-300">
                Click for AI recommendations
              </div>
            </div>
          </div>
        </div>

        {/* Total Inventory Value Card - Indigo-Purple Emblem */}
        <div
          className="group relative overflow-hidden bg-gradient-to-br from-indigo-600 via-blue-700 to-purple-900 rounded-3xl p-8 text-white shadow-2xl hover:shadow-indigo-500/25 transform hover:-translate-y-3 hover:rotate-1 transition-all duration-500 cursor-pointer border-4 border-indigo-400/30 hover:border-amber-400"
          onClick={() => setSelectedTab('budget')}
        >
          {/* Animated Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-blue-600/20 to-purple-800/20"></div>
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-amber-400/30 to-yellow-500/30 rounded-full opacity-0 group-hover:opacity-100 transform translate-x-8 -translate-y-8 group-hover:animate-pulse transition-all duration-500"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-indigo-300/20 to-purple-400/20 rounded-full animate-bounce"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <div className="relative p-4 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-2xl shadow-xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-2 border-yellow-300">
                <CurrencyDollarIcon className="h-8 w-8 text-purple-900 group-hover:text-indigo-900 transition-colors duration-300" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full animate-ping"></div>
              </div>
              <div className="text-right group-hover:scale-110 transition-all duration-300">
                <div className="text-3xl font-black bg-gradient-to-r from-amber-300 to-yellow-400 bg-clip-text text-transparent group-hover:animate-pulse">
                  ${supplyItems.reduce((total, item) => total + (item.currentStock * item.cost), 0).toLocaleString()}
                </div>
                <div className="text-indigo-200 text-sm font-bold group-hover:text-amber-200 transition-colors duration-300">Inventory Value</div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="text-indigo-100 text-sm font-medium group-hover:text-amber-100 transition-colors duration-300">
                Avg: ${(supplyItems.reduce((total, item) => total + (item.currentStock * item.cost), 0) / supplyItems.length).toFixed(0)} per item
              </div>
              <div className="w-full bg-indigo-800/50 rounded-full h-3 border border-indigo-600">
                <div className="bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 h-3 rounded-full transition-all duration-1000 shadow-lg group-hover:animate-pulse w-3/4"></div>
              </div>
              <div className="text-xs text-indigo-300 group-hover:text-yellow-300 transition-colors duration-300">
                Click to view budget details
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Ultra-Interactive Emblem Tabs */}
      <div className="relative bg-gradient-to-br from-white via-blue-50/80 to-purple-50/80 shadow-2xl rounded-3xl border-4 border-gradient-to-r from-purple-200 via-blue-200 to-amber-200 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-100/30 via-blue-100/30 to-amber-100/30"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 via-blue-500 to-amber-500 animate-pulse"></div>

        <div className="relative border-b-4 border-gradient-to-r from-purple-200 via-blue-200 to-amber-200">
          <nav className="flex space-x-3 px-8 py-4">
            {tabs.map((tab, index) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`group relative overflow-hidden py-5 px-8 font-bold text-sm rounded-2xl transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 ${
                  selectedTab === tab.id
                    ? 'bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 text-white shadow-2xl border-4 border-amber-400 scale-105'
                    : 'text-gray-700 hover:text-white hover:bg-gradient-to-br hover:from-purple-500 hover:to-blue-600 border-4 border-transparent hover:border-amber-300 shadow-lg'
                }`}
                style={{
                  animationDelay: `${index * 100}ms`
                }}
              >
                {/* Animated Background Elements */}
                <div className={`absolute inset-0 transition-all duration-500 ${
                  selectedTab === tab.id
                    ? 'bg-gradient-to-r from-amber-400/20 via-yellow-500/20 to-amber-400/20 opacity-100'
                    : 'bg-gradient-to-r from-purple-400/10 to-blue-400/10 opacity-0 group-hover:opacity-100'
                }`}></div>

                {/* Floating Orbs */}
                {selectedTab === tab.id && (
                  <>
                    <div className="absolute top-1 right-1 w-2 h-2 bg-amber-400 rounded-full animate-ping"></div>
                    <div className="absolute bottom-1 left-1 w-1.5 h-1.5 bg-yellow-300 rounded-full animate-pulse"></div>
                  </>
                )}

                <div className="flex items-center relative z-10">
                  <tab.icon className={`h-6 w-6 mr-4 transition-all duration-500 ${
                    selectedTab === tab.id
                      ? 'text-amber-300 scale-125 rotate-12'
                      : 'text-purple-600 group-hover:text-amber-300 group-hover:scale-110 group-hover:rotate-6'
                  }`} />
                  <span className={`font-black transition-all duration-300 ${
                    selectedTab === tab.id
                      ? 'text-white'
                      : 'text-gray-700 group-hover:text-white'
                  }`}>
                    {tab.name}
                  </span>
                </div>

                {/* Active Tab Glow Effect */}
                {selectedTab === tab.id && (
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 opacity-30 rounded-2xl animate-pulse"></div>
                )}

                {/* Hover Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 via-blue-500 to-purple-400 opacity-0 group-hover:opacity-20 rounded-2xl transition-opacity duration-300"></div>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {selectedTab === 'overview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Supply Management Dashboard</h3>

              {/* Supply Categories Overview */}
              <div className="border border-gray-200 rounded-lg p-6">
                <h4 className="font-medium text-gray-900 mb-4">Supply Categories</h4>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 bg-blue-50 rounded-lg text-center">
                    <div className="text-2xl mb-2">🍎</div>
                    <h5 className="font-medium text-gray-900">Food & Groceries</h5>
                    <p className="text-sm text-gray-600">Meals, snacks, beverages</p>
                  </div>

                  <div className="p-4 bg-green-50 rounded-lg text-center">
                    <div className="text-2xl mb-2">🧽</div>
                    <h5 className="font-medium text-gray-900">Cleaning Supplies</h5>
                    <p className="text-sm text-gray-600">Disinfectants, sanitizers</p>
                  </div>

                  <div className="p-4 bg-purple-50 rounded-lg text-center">
                    <div className="text-2xl mb-2">🩺</div>
                    <h5 className="font-medium text-gray-900">Medical Supplies</h5>
                    <p className="text-sm text-gray-600">Equipment, monitoring tools</p>
                  </div>

                  <div className="p-4 bg-orange-50 rounded-lg text-center">
                    <div className="text-2xl mb-2">🛏️</div>
                    <h5 className="font-medium text-gray-900">Linens & Bedding</h5>
                    <p className="text-sm text-gray-600">Sheets, towels, blankets</p>
                  </div>

                  <div className="p-4 bg-yellow-50 rounded-lg text-center">
                    <div className="text-2xl mb-2">🧴</div>
                    <h5 className="font-medium text-gray-900">Personal Care</h5>
                    <p className="text-sm text-gray-600">Hygiene, toiletries</p>
                  </div>

                  <div className="p-4 bg-red-50 rounded-lg text-center">
                    <div className="text-2xl mb-2">🪑</div>
                    <h5 className="font-medium text-gray-900">Furniture</h5>
                    <p className="text-sm text-gray-600">Chairs, tables, storage</p>
                  </div>

                  <div className="p-4 bg-indigo-50 rounded-lg text-center">
                    <div className="text-2xl mb-2">🔧</div>
                    <h5 className="font-medium text-gray-900">Maintenance</h5>
                    <p className="text-sm text-gray-600">Tools, repair supplies</p>
                  </div>

                  <div className="p-4 bg-pink-50 rounded-lg text-center">
                    <div className="text-2xl mb-2">🎯</div>
                    <h5 className="font-medium text-gray-900">Recreational</h5>
                    <p className="text-sm text-gray-600">Activities, games, crafts</p>
                  </div>
                </div>
              </div>

              {/* Inventory Alerts */}
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium text-gray-900">Inventory Alerts</h4>
                  <button className="text-sm text-blue-600 hover:text-blue-800">View All</button>
                </div>
                <div className="space-y-3">
                  {inventoryAlerts.slice(0, 3).map((alert) => (
                    <div key={alert.id} className={`border rounded-lg p-4 ${getSeverityColor(alert.severity)}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <ExclamationTriangleIcon className="h-5 w-5" />
                            <h5 className="font-medium">{alert.itemName}</h5>
                            <span className="text-xs px-2 py-1 rounded-full bg-white bg-opacity-50 capitalize">
                              {alert.type.replace('_', ' ')}
                            </span>
                          </div>
                          <p className="text-sm mt-1">{alert.message}</p>
                          <p className="text-xs mt-2 font-medium">Suggested: {alert.suggestedAction}</p>
                        </div>
                        <div className="ml-4">
                          {!alert.acknowledged && (
                            <button className="px-3 py-1 bg-white text-gray-800 text-sm rounded-md hover:bg-gray-50">
                              Acknowledge
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* AI Recommendations */}
              <div className="border border-gray-200 rounded-lg p-6">
                <h4 className="font-medium text-gray-900 mb-4">🤖 AI Recommendations</h4>
                <div className="space-y-3">
                  <div className="p-3 bg-purple-50 rounded">
                    <div className="flex items-start">
                      <BeakerIcon className="h-5 w-5 text-purple-600 mt-0.5" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-purple-800">Bulk Purchase Opportunity</p>
                        <p className="text-sm text-purple-700">Save 15% on dairy products with 20+ gallon order from FreshMart</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-3 bg-orange-50 rounded">
                    <div className="flex items-start">
                      <ClockIcon className="h-5 w-5 text-orange-600 mt-0.5" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-orange-800">Seasonal Adjustment</p>
                        <p className="text-sm text-orange-700">Increase soup and warm beverage inventory for winter months</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-3 bg-green-50 rounded">
                    <div className="flex items-start">
                      <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
                      <div className="ml-3">
                        <p className="text-sm font-medium text-green-800">Cost Optimization</p>
                        <p className="text-sm text-green-700">Switch to generic brand cereals to save $45/month without quality loss</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'ordering' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Smart Ordering System</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={handleAISuggestions}
                    className="px-4 py-2 bg-purple-600 text-white text-sm rounded-md hover:bg-purple-700"
                  >
                    <BeakerIcon className="h-4 w-4 inline mr-1" />
                    AI Suggestions
                  </button>
                  <button
                    onClick={handleCreateOrder}
                    className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                  >
                    <PlusIcon className="h-4 w-4 inline mr-1" />
                    Create Order
                  </button>
                </div>
              </div>

              {/* Auto-Reorder Suggestions */}
              <div className="border border-gray-200 rounded-lg p-6">
                <h4 className="font-medium text-gray-900 mb-4">🤖 AI-Powered Reorder Suggestions</h4>
                <div className="space-y-4">
                  {supplyItems.filter(item => item.currentStock <= item.minimumStock).map((item) => (
                    <div key={item.id} className="border border-gray-100 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                              <CubeIcon className="h-6 w-6 text-gray-500" />
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900">{item.name}</h5>
                              <p className="text-sm text-gray-600">{item.brand} • {item.unitSize}</p>
                              <div className="flex items-center space-x-2 mt-1">
                                <span className="text-xs px-2 py-1 bg-red-100 text-red-800 rounded-full">
                                  Low Stock: {item.currentStock} {item.unit}
                                </span>
                                <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                                  Min: {item.minimumStock} {item.unit}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="mt-3 grid grid-cols-1 sm:grid-cols-4 gap-4">
                            <div>
                              <p className="text-sm font-medium text-gray-700">Suggested Quantity</p>
                              <p className="text-sm text-gray-900">{Math.ceil(item.averageConsumption * 1.5)} {item.unit}</p>
                            </div>

                            <div>
                              <p className="text-sm font-medium text-gray-700">Weekly Usage</p>
                              <p className="text-sm text-gray-600">{item.averageConsumption} {item.unit}</p>
                            </div>

                            <div>
                              <p className="text-sm font-medium text-gray-700">Unit Cost</p>
                              <p className="text-sm text-gray-900">${item.cost}</p>
                            </div>

                            <div>
                              <p className="text-sm font-medium text-gray-700">Total Cost</p>
                              <p className="text-sm text-gray-900 font-medium">
                                ${(Math.ceil(item.averageConsumption * 1.5) * item.cost).toFixed(2)}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="ml-6 flex flex-col space-y-2">
                          <button
                            onClick={() => handleAddToCart(item, Math.ceil(item.averageConsumption * 1.5))}
                            className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
                          >
                            Add to Cart
                          </button>
                          <button
                            onClick={() => handleCustomize(item)}
                            className="px-4 py-2 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700"
                          >
                            Customize
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'inventory' && (
            <div className="text-center py-8">
              <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Inventory Management</h3>
              <p className="mt-1 text-sm text-gray-500">
                Real-time inventory tracking with expiration monitoring
              </p>
            </div>
          )}

          {selectedTab === 'vendors' && (
            <div className="text-center py-8">
              <BuildingStorefrontIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Vendor Management</h3>
              <p className="mt-1 text-sm text-gray-500">
                Manage suppliers, compare prices, and track vendor performance
              </p>
            </div>
          )}

          {selectedTab === 'budget' && (
            <div className="text-center py-8">
              <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Budget & Analytics</h3>
              <p className="mt-1 text-sm text-gray-500">
                Track spending, analyze trends, and optimize purchasing decisions
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Create Order Modal */}
      {showCreateOrderModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowCreateOrderModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <PlusIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Create New Order
                      </h3>
                      <p className="text-sm text-gray-500">
                        Build a comprehensive supply order for your facility
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowCreateOrderModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Order Type Selection */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-900 mb-3">Order Type</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <button className="p-3 border-2 border-blue-200 rounded-lg hover:border-blue-400 bg-white">
                        <div className="text-center">
                          <ShoppingCartIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                          <p className="font-medium text-blue-900">Regular Order</p>
                          <p className="text-sm text-blue-700">Standard supply replenishment</p>
                        </div>
                      </button>
                      <button className="p-3 border-2 border-orange-200 rounded-lg hover:border-orange-400 bg-white">
                        <div className="text-center">
                          <ExclamationTriangleIcon className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                          <p className="font-medium text-orange-900">Emergency Order</p>
                          <p className="text-sm text-orange-700">Urgent supply needs</p>
                        </div>
                      </button>
                      <button className="p-3 border-2 border-green-200 rounded-lg hover:border-green-400 bg-white">
                        <div className="text-center">
                          <CalendarDaysIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
                          <p className="font-medium text-green-900">Scheduled Order</p>
                          <p className="text-sm text-green-700">Recurring deliveries</p>
                        </div>
                      </button>
                    </div>
                  </div>

                  {/* Category Selection */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Select Categories</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {[
                        { name: 'Food & Groceries', icon: '🍎', count: 45 },
                        { name: 'Medical Supplies', icon: '🏥', count: 23 },
                        { name: 'Cleaning Supplies', icon: '🧽', count: 18 },
                        { name: 'Personal Care', icon: '🧴', count: 15 },
                        { name: 'Linens & Bedding', icon: '🛏️', count: 12 },
                        { name: 'Kitchen Equipment', icon: '🍳', count: 8 },
                        { name: 'Office Supplies', icon: '📝', count: 10 },
                        { name: 'Safety Equipment', icon: '🦺', count: 7 }
                      ].map((category) => (
                        <label key={category.name} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-100 cursor-pointer">
                          <input type="checkbox" className="mr-3" />
                          <div className="flex-1">
                            <div className="flex items-center">
                              <span className="text-lg mr-2">{category.icon}</span>
                              <div>
                                <p className="text-sm font-medium text-gray-900">{category.name}</p>
                                <p className="text-xs text-gray-500">{category.count} items</p>
                              </div>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Order Details */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-green-900 mb-3">Order Details</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-green-700">Delivery Date</label>
                        <input
                          type="date"
                          defaultValue={new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-green-700">Priority Level</label>
                        <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm">
                          <option value="normal">Normal</option>
                          <option value="high">High</option>
                          <option value="urgent">Urgent</option>
                        </select>
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-green-700">Special Instructions</label>
                        <textarea
                          rows={3}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                          placeholder="Any special delivery instructions, dietary requirements, or notes..."
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Order created successfully! You can now add items to this order.');
                    setShowCreateOrderModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Create Order
                </button>
                <button
                  onClick={() => setShowCreateOrderModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* AI Suggestions Modal */}
      {showAISuggestionsModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAISuggestionsModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 sm:mx-0 sm:h-10 sm:w-10">
                      <BeakerIcon className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        AI-Powered Supply Suggestions
                      </h3>
                      <p className="text-sm text-gray-500">
                        Smart recommendations based on consumption patterns and predictive analytics
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAISuggestionsModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Priority Recommendations */}
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-red-900 mb-3 flex items-center">
                      <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
                      Urgent Recommendations
                    </h4>
                    <div className="space-y-3">
                      <div className="bg-white p-3 rounded border border-red-200">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-red-900">Low Stock Alert: Hand Sanitizer</p>
                            <p className="text-sm text-red-700">Current: 2 bottles | Minimum: 10 bottles</p>
                            <p className="text-sm text-red-600">⚠️ Critical shortage - Order immediately</p>
                          </div>
                          <button className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                            Add to Cart
                          </button>
                        </div>
                      </div>
                      <div className="bg-white p-3 rounded border border-red-200">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-red-900">Expiring Soon: Milk Products</p>
                            <p className="text-sm text-red-700">3 gallons expire in 2 days</p>
                            <p className="text-sm text-red-600">🥛 Consider smaller quantities for next order</p>
                          </div>
                          <button className="px-3 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700">
                            Adjust Order
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Cost Optimization */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-green-900 mb-3 flex items-center">
                      <CurrencyDollarIcon className="h-5 w-5 mr-2" />
                      Cost Optimization Opportunities
                    </h4>
                    <div className="space-y-3">
                      <div className="bg-white p-3 rounded border border-green-200">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-green-900">Bulk Purchase Savings</p>
                            <p className="text-sm text-green-700">Save $127.50 on cleaning supplies with 20+ item order</p>
                            <p className="text-sm text-green-600">💰 15% discount available from CleanCorp</p>
                          </div>
                          <button className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                            Apply Deal
                          </button>
                        </div>
                      </div>
                      <div className="bg-white p-3 rounded border border-green-200">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-green-900">Alternative Supplier</p>
                            <p className="text-sm text-green-700">Switch to MedSupply Co. for 8% savings on medical supplies</p>
                            <p className="text-sm text-green-600">📊 Same quality, better pricing</p>
                          </div>
                          <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                            Compare
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Seasonal Recommendations */}
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-orange-900 mb-3 flex items-center">
                      <CalendarDaysIcon className="h-5 w-5 mr-2" />
                      Seasonal & Trend Analysis
                    </h4>
                    <div className="space-y-3">
                      <div className="bg-white p-3 rounded border border-orange-200">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-orange-900">Winter Preparation</p>
                            <p className="text-sm text-orange-700">Increase soup, hot beverages, and warm clothing inventory</p>
                            <p className="text-sm text-orange-600">🌨️ Based on historical consumption patterns</p>
                          </div>
                          <button className="px-3 py-1 bg-orange-600 text-white text-sm rounded hover:bg-orange-700">
                            Auto-Adjust
                          </button>
                        </div>
                      </div>
                      <div className="bg-white p-3 rounded border border-orange-200">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-orange-900">Flu Season Prep</p>
                            <p className="text-sm text-orange-700">Stock up on tissues, sanitizers, and immune-boosting foods</p>
                            <p className="text-sm text-orange-600">🤧 Preventive care recommendations</p>
                          </div>
                          <button className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700">
                            Add Bundle
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* AI Insights */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-900 mb-3 flex items-center">
                      <ChartBarIcon className="h-5 w-5 mr-2" />
                      AI Analytics Insights
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white p-3 rounded border border-blue-200">
                        <p className="font-medium text-blue-900">Consumption Trends</p>
                        <p className="text-sm text-blue-700">Dairy consumption up 12% this month</p>
                        <p className="text-xs text-blue-600">Adjust reorder quantities accordingly</p>
                      </div>
                      <div className="bg-white p-3 rounded border border-blue-200">
                        <p className="font-medium text-blue-900">Waste Reduction</p>
                        <p className="text-sm text-blue-700">Reduce bread orders by 15% to minimize waste</p>
                        <p className="text-xs text-blue-600">Based on disposal patterns</p>
                      </div>
                      <div className="bg-white p-3 rounded border border-blue-200">
                        <p className="font-medium text-blue-900">Quality Optimization</p>
                        <p className="text-sm text-blue-700">Switch to organic produce for better resident satisfaction</p>
                        <p className="text-xs text-blue-600">Satisfaction scores increased 18%</p>
                      </div>
                      <div className="bg-white p-3 rounded border border-blue-200">
                        <p className="font-medium text-blue-900">Delivery Optimization</p>
                        <p className="text-sm text-blue-700">Consolidate Tuesday deliveries to save $45/week</p>
                        <p className="text-xs text-blue-600">Logistics efficiency improvement</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('AI suggestions applied! Your ordering preferences have been updated.');
                    setShowAISuggestionsModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Apply Suggestions
                </button>
                <button
                  onClick={() => setShowAISuggestionsModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Customize Modal */}
      {showCustomizeModal && selectedItem && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowCustomizeModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 sm:mx-0 sm:h-10 sm:w-10">
                      <CogIcon className="h-6 w-6 text-gray-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Customize Order
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedItem?.name} - {selectedItem?.brand}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowCustomizeModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Quantity Selection */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-900 mb-3">Quantity & Sizing</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-700">Quantity</label>
                        <input
                          type="number"
                          defaultValue={selectedItem ? Math.ceil(selectedItem.averageConsumption * 1.5) : 1}
                          min="1"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-blue-700">Unit Size</label>
                        <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                          <option value={selectedItem?.unitSize || 'standard'}>{selectedItem?.unitSize || 'Standard'} (Current)</option>
                          <option value="bulk">Bulk Size (+15% savings)</option>
                          <option value="individual">Individual Portions</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Special Requirements */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-green-900 mb-3">Special Requirements</h4>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-3" />
                        <span className="text-sm text-green-700">Organic/Natural option (+$2.50)</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-3" />
                        <span className="text-sm text-green-700">Low sodium variant</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-3" />
                        <span className="text-sm text-green-700">Allergen-free alternative</span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="mr-3" />
                        <span className="text-sm text-green-700">Extended shelf life</span>
                      </label>
                    </div>
                  </div>

                  {/* Delivery Preferences */}
                  <div className="bg-orange-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-orange-900 mb-3">Delivery Preferences</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-orange-700">Delivery Date</label>
                        <input
                          type="date"
                          defaultValue={new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-orange-700">Priority</label>
                        <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm">
                          <option value="normal">Normal</option>
                          <option value="high">High Priority</option>
                          <option value="urgent">Urgent</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Special Instructions */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Special Instructions</h4>
                    <textarea
                      rows={3}
                      className="w-full rounded-md border-gray-300 shadow-sm focus:border-gray-500 focus:ring-gray-500 sm:text-sm"
                      placeholder="Any special handling, storage, or delivery instructions..."
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    if (selectedItem) {
                      handleAddToCart(selectedItem, Math.ceil(selectedItem.averageConsumption * 1.5), 'Customized order with special requirements');
                      setShowCustomizeModal(false);
                    }
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Add to Cart
                </button>
                <button
                  onClick={() => setShowCustomizeModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cart Modal */}
      {showCartModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowCartModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <ShoppingCartIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Shopping Cart
                      </h3>
                      <p className="text-sm text-gray-500">
                        {getTotalCartItems()} items • Total: ${getTotalCartValue().toFixed(2)}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowCartModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  {cart.length === 0 ? (
                    <div className="text-center py-8">
                      <ShoppingCartIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Your cart is empty</p>
                      <p className="text-sm text-gray-400">Add items from the ordering section</p>
                    </div>
                  ) : (
                    <>
                      {/* Cart Items */}
                      <div className="max-h-96 overflow-y-auto space-y-3">
                        {cart.map((item) => (
                          <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-medium text-gray-900">{item.name}</h4>
                                <p className="text-sm text-gray-600">{item.brand} • {item.category}</p>
                                {item.customizations && (
                                  <p className="text-sm text-blue-600">✨ {item.customizations}</p>
                                )}
                                <div className="mt-2 flex items-center space-x-4">
                                  <div className="flex items-center">
                                    <button
                                      onClick={() => updateCartQuantity(item.id, item.quantity - 1)}
                                      className="px-2 py-1 border border-gray-300 rounded-l text-sm hover:bg-gray-50"
                                    >
                                      -
                                    </button>
                                    <span className="px-3 py-1 border-t border-b border-gray-300 text-sm">
                                      {item.quantity}
                                    </span>
                                    <button
                                      onClick={() => updateCartQuantity(item.id, item.quantity + 1)}
                                      className="px-2 py-1 border border-gray-300 rounded-r text-sm hover:bg-gray-50"
                                    >
                                      +
                                    </button>
                                  </div>
                                  <span className="text-sm text-gray-500">
                                    ${item.cost.toFixed(2)} each
                                  </span>
                                </div>
                              </div>
                              <div className="ml-4 text-right">
                                <p className="font-medium text-gray-900">
                                  ${(item.cost * item.quantity).toFixed(2)}
                                </p>
                                <button
                                  onClick={() => removeFromCart(item.id)}
                                  className="text-sm text-red-600 hover:text-red-800"
                                >
                                  Remove
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Cart Summary */}
                      <div className="border-t border-gray-200 pt-4">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm text-gray-600">Subtotal:</span>
                            <span className="text-sm font-medium">${getTotalCartValue().toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm text-gray-600">Delivery Fee:</span>
                            <span className="text-sm font-medium">$25.00</span>
                          </div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm text-gray-600">Tax (8.5%):</span>
                            <span className="text-sm font-medium">${(getTotalCartValue() * 0.085).toFixed(2)}</span>
                          </div>
                          <div className="border-t border-gray-300 pt-2">
                            <div className="flex justify-between items-center">
                              <span className="text-lg font-medium text-gray-900">Total:</span>
                              <span className="text-lg font-bold text-gray-900">
                                ${(getTotalCartValue() + 25 + (getTotalCartValue() * 0.085)).toFixed(2)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                {cart.length > 0 && (
                  <button
                    onClick={() => {
                      alert(`Order placed successfully! Total: $${(getTotalCartValue() + 25 + (getTotalCartValue() * 0.085)).toFixed(2)}`);
                      setCart([]);
                      setShowCartModal(false);
                    }}
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Place Order
                  </button>
                )}
                <button
                  onClick={() => setShowCartModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Continue Shopping
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GroceryManagement;
