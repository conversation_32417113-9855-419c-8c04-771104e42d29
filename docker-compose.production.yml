version: '3.8'

services:
  # Frontend - React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: care-solai-frontend
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=https://yourdomain.com/api
      - REACT_APP_WS_URL=wss://yourdomain.com/ws
    volumes:
      - frontend_build:/app/build
    networks:
      - care-solai-network

  # Backend - FastAPI Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: care-solai-backend
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - SECRET_KEY=${SECRET_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
    ports:
      - "8000:8000"
    depends_on:
      - database
    networks:
      - care-solai-network
    restart: unless-stopped

  # WebSocket Server - Wireless Signing
  websocket-server:
    build:
      context: ./backend
      dockerfile: Dockerfile.websocket
    container_name: care-solai-websocket
    environment:
      - ENVIRONMENT=production
      - WS_HOST=0.0.0.0
      - WS_PORT=8001
    ports:
      - "8001:8001"
    networks:
      - care-solai-network
    restart: unless-stopped
    command: python wireless_signing_server.py

  # Database - PostgreSQL
  database:
    image: postgres:15-alpine
    container_name: care-solai-db
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./supabase/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./supabase/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql
    networks:
      - care-solai-network
    restart: unless-stopped

  # Nginx - Reverse Proxy & Load Balancer
  nginx:
    image: nginx:alpine
    container_name: care-solai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - frontend_build:/usr/share/nginx/html
    depends_on:
      - frontend
      - backend
      - websocket-server
    networks:
      - care-solai-network
    restart: unless-stopped

  # Redis - Session & Caching
  redis:
    image: redis:7-alpine
    container_name: care-solai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - care-solai-network
    restart: unless-stopped

  # AI Services - Python ML/AI
  ai-service:
    build:
      context: ./ai
      dockerfile: Dockerfile
    container_name: care-solai-ai
    environment:
      - ENVIRONMENT=production
      - MODEL_PATH=/app/models
    ports:
      - "8002:8002"
    volumes:
      - ai_models:/app/models
    networks:
      - care-solai-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  frontend_build:
  ai_models:

networks:
  care-solai-network:
    driver: bridge
