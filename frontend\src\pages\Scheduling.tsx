import React, { useState } from 'react';
import {
  CalendarDaysIcon,
  ClockIcon,
  UserGroupIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  BellIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

const Scheduling: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState('calendar');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedView, setSelectedView] = useState('week'); // week, month, day
  const [showAddScheduleModal, setShowAddScheduleModal] = useState(false);
  const [showAddShiftModal, setShowAddShiftModal] = useState(false);
  const [showAddAppointmentModal, setShowAddAppointmentModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedShift, setSelectedShift] = useState<any>(null);
  const [selectedAppointment, setSelectedAppointment] = useState<any>(null);

  // Mock data for shifts and appointments (using current date: July 25, 2025)
  const shifts = [
    {
      id: 'S001',
      staffName: 'Sarah Johnson',
      role: 'Registered Nurse',
      date: '2025-07-25',
      startTime: '07:00',
      endTime: '19:30',
      shiftType: 'day_extended',
      status: 'scheduled',
      residents: ['R001', 'R002', 'R003'],
      color: 'bg-blue-500'
    },
    {
      id: 'S002',
      staffName: 'Michael Chen',
      role: 'Care Assistant',
      date: '2025-07-25',
      startTime: '06:00',
      endTime: '14:00',
      shiftType: 'day',
      status: 'confirmed',
      residents: ['R004', 'R005'],
      color: 'bg-green-500'
    },
    {
      id: 'S003',
      staffName: 'Emily Rodriguez',
      role: 'Registered Nurse',
      date: '2025-07-25',
      startTime: '19:00',
      endTime: '07:30',
      shiftType: 'night_extended',
      status: 'pending',
      residents: ['R001', 'R002', 'R003', 'R004', 'R005', 'R006'],
      color: 'bg-purple-500'
    }
  ];

  const appointments = [
    {
      id: 'A001',
      residentName: 'John Smith',
      type: 'Doctor Visit',
      provider: 'Dr. Williams',
      date: '2025-07-25',
      time: '10:00',
      duration: 60,
      status: 'confirmed',
      notes: 'Routine checkup'
    },
    {
      id: 'A002',
      residentName: 'Mary Johnson',
      type: 'Physical Therapy',
      provider: 'PT Sarah',
      date: '2025-07-25',
      time: '14:30',
      duration: 45,
      status: 'pending',
      notes: 'Mobility assessment'
    },
    {
      id: 'A003',
      residentName: 'Robert Davis',
      type: 'Dental Cleaning',
      provider: 'Dr. Brown',
      date: '2025-07-26',
      time: '09:00',
      duration: 30,
      status: 'confirmed',
      notes: 'Regular cleaning'
    }
  ];

  const getShiftTypeDisplay = (shiftType: string) => {
    switch (shiftType) {
      case 'day':
        return 'Day (6AM-2PM)';
      case 'evening':
        return 'Evening (2PM-10PM)';
      case 'night':
        return 'Night (10PM-6AM)';
      case 'day_extended':
        return 'Extended Day (7AM-7:30PM)';
      case 'night_extended':
        return 'Extended Night (7PM-7:30AM)';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Generate calendar dates for the current month
  const generateCalendarDates = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);

    // Start from the first Sunday of the calendar (might be from previous month)
    const startDate = new Date(firstDay);
    startDate.setDate(firstDay.getDate() - firstDay.getDay());

    // Generate 42 days (6 weeks) for the calendar
    const dates = [];
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      dates.push(currentDate);
    }

    return dates;
  };

  const isToday = (date: Date) => {
    // Current date is July 25, 2025
    const today = new Date(2025, 6, 25); // Month is 0-indexed, so 6 = July
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };

  const isCurrentMonth = (date: Date, currentDate: Date) => {
    return date.getMonth() === currentDate.getMonth() &&
           date.getFullYear() === currentDate.getFullYear();
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (selectedView === 'day') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    } else if (selectedView === 'week') {
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
    } else {
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
    }
    setCurrentDate(newDate);
  };

  // Button handlers
  const handleAddSchedule = () => {
    setShowAddScheduleModal(true);
  };

  const handleAddShift = () => {
    setShowAddShiftModal(true);
  };

  const handleAddAppointment = () => {
    setShowAddAppointmentModal(true);
  };

  const handleGoToToday = () => {
    setCurrentDate(new Date());
  };

  const handleEditShift = (shift: any) => {
    setSelectedShift(shift);
    setShowAddShiftModal(true);
  };

  const handleEditAppointment = (appointment: any) => {
    setSelectedAppointment(appointment);
    setShowAddAppointmentModal(true);
  };

  const handleCancelAppointment = (appointmentId: string) => {
    if (window.confirm('Are you sure you want to cancel this appointment?')) {
      alert(`Appointment ${appointmentId} has been cancelled.`);
      // In real app, this would update the state/database
    }
  };

  const handleViewShiftDetails = (shift: any) => {
    alert(`Shift Details:\n\nStaff: ${shift.staffName}\nRole: ${shift.role}\nTime: ${shift.startTime} - ${shift.endTime}\nResidents: ${shift.residents.length}\nStatus: ${shift.status}`);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const filteredShifts = shifts.filter(shift =>
    shift.staffName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    shift.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Staff Scheduling & Appointments</h1>
            <p className="text-purple-100">Manage staff shifts, resident appointments, and facility calendar</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-2xl font-bold">24</div>
              <div className="text-sm text-purple-200">Staff Scheduled</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">8</div>
              <div className="text-sm text-purple-200">Appointments Today</div>
            </div>
            <button
              onClick={handleAddSchedule}
              className="bg-white text-purple-600 px-4 py-2 rounded-lg font-medium hover:bg-purple-50 transition-colors duration-200"
            >
              <PlusIcon className="h-5 w-5 inline mr-2" />
              Add Schedule
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'calendar', name: 'Calendar View', icon: CalendarDaysIcon },
            { id: 'shifts', name: 'Staff Shifts', icon: UserGroupIcon },
            { id: 'appointments', name: 'Appointments', icon: ClockIcon },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id)}
              className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                selectedTab === tab.id
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className={`mr-2 h-5 w-5 ${
                selectedTab === tab.id ? 'text-purple-500' : 'text-gray-400 group-hover:text-gray-500'
              }`} />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Calendar View Tab */}
      {selectedTab === 'calendar' && (
        <div className="space-y-6">
          {/* Calendar Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigateDate('prev')}
                className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>
              <h2 className="text-xl font-semibold text-gray-900">
                {formatDate(currentDate)}
              </h2>
              <button
                onClick={() => navigateDate('next')}
                className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50"
              >
                <ArrowRightIcon className="h-5 w-5" />
              </button>
            </div>
            
            <div className="flex items-center space-x-2">
              <select
                value={selectedView}
                onChange={(e) => setSelectedView(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="day">Day View</option>
                <option value="week">Week View</option>
                <option value="month">Month View</option>
              </select>
              <button
                onClick={handleGoToToday}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-purple-700 transition-colors duration-200"
              >
                Today
              </button>
            </div>
          </div>

          {/* Calendar Grid */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="grid grid-cols-7 gap-px bg-gray-200">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <div key={day} className="bg-gray-50 p-4 text-center text-sm font-medium text-gray-700">
                  {day}
                </div>
              ))}
            </div>
            
            <div className="grid grid-cols-7 gap-px bg-gray-200">
              {generateCalendarDates(currentDate).slice(0, 35).map((date, i) => {
                const dayNumber = date.getDate();
                const isCurrentMonthDate = isCurrentMonth(date, currentDate);
                const isTodayDate = isToday(date);

                return (
                  <div
                    key={i}
                    className={`p-2 h-32 relative ${
                      isTodayDate
                        ? 'bg-purple-50 border-2 border-purple-500'
                        : 'bg-white'
                    }`}
                  >
                    <div className={`text-sm font-medium mb-1 ${
                      isTodayDate
                        ? 'text-purple-600 font-bold'
                        : isCurrentMonthDate
                          ? 'text-gray-900'
                          : 'text-gray-400'
                    }`}>
                      {dayNumber}
                    </div>

                    {/* Show events for current month dates */}
                    {isCurrentMonthDate && (
                      <div className="space-y-1">
                        {/* Sample events for demonstration */}
                        {dayNumber === 25 && isTodayDate && (
                          <>
                            <div className="bg-blue-100 text-blue-800 text-xs p-1 rounded truncate">
                              Sarah - Day Shift
                            </div>
                            <div className="bg-green-100 text-green-800 text-xs p-1 rounded truncate">
                              Dr. Visit - John
                            </div>
                          </>
                        )}
                        {dayNumber % 3 === 0 && dayNumber !== 25 && (
                          <div className="bg-blue-100 text-blue-800 text-xs p-1 rounded truncate">
                            Day Shift: 3 staff
                          </div>
                        )}
                        {dayNumber % 5 === 0 && dayNumber !== 25 && (
                          <div className="bg-green-100 text-green-800 text-xs p-1 rounded truncate">
                            Appointment
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Staff Shifts Tab */}
      {selectedTab === 'shifts' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">Staff Shifts</h2>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search staff..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <button
                onClick={handleAddShift}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors duration-200"
              >
                <PlusIcon className="h-5 w-5 inline mr-2" />
                Add Shift
              </button>
            </div>
          </div>

          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Staff Member
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Shift Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assigned Residents
                  </th>
                  <th className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredShifts.map((shift) => (
                  <tr key={shift.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{shift.staffName}</div>
                        <div className="text-sm text-gray-500">{shift.role}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm text-gray-900">{getShiftTypeDisplay(shift.shiftType)}</div>
                        <div className="text-sm text-gray-500">{shift.date}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {shift.startTime} - {shift.endTime}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(shift.status)}`}>
                        {shift.status === 'confirmed' && <CheckCircleIcon className="h-3 w-3 mr-1" />}
                        {shift.status === 'pending' && <ExclamationTriangleIcon className="h-3 w-3 mr-1" />}
                        <span className="capitalize">{shift.status}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {shift.residents.length} residents
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2 justify-end">
                        <button
                          onClick={() => handleEditShift(shift)}
                          className="text-purple-600 hover:text-purple-900 transition-colors duration-200"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleViewShiftDetails(shift)}
                          className="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                        >
                          View
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Appointments Tab */}
      {selectedTab === 'appointments' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">Resident Appointments</h2>
            <button
              onClick={handleAddAppointment}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors duration-200"
            >
              <PlusIcon className="h-5 w-5 inline mr-2" />
              Schedule Appointment
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {appointments.map((appointment) => (
              <div key={appointment.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">{appointment.residentName}</h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                    {appointment.status === 'confirmed' && <CheckCircleIcon className="h-3 w-3 mr-1" />}
                    {appointment.status === 'pending' && <ClockIcon className="h-3 w-3 mr-1" />}
                    <span className="capitalize">{appointment.status}</span>
                  </span>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center">
                    <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">{appointment.type}</div>
                      <div className="text-sm text-gray-500">{appointment.provider}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <CalendarDaysIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">{appointment.date}</div>
                      <div className="text-sm text-gray-500">{appointment.time} ({appointment.duration} min)</div>
                    </div>
                  </div>
                  
                  {appointment.notes && (
                    <div className="pt-3 border-t border-gray-200">
                      <p className="text-sm text-gray-600">{appointment.notes}</p>
                    </div>
                  )}
                </div>

                <div className="mt-4 flex space-x-2">
                  <button
                    onClick={() => handleEditAppointment(appointment)}
                    className="flex-1 bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700 transition-colors duration-200"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleCancelAppointment(appointment.id)}
                    className="flex-1 border border-gray-300 text-gray-700 px-3 py-2 rounded text-sm hover:bg-gray-50 transition-colors duration-200"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add Schedule Modal */}
      {showAddScheduleModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddScheduleModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 sm:mx-0 sm:h-10 sm:w-10">
                      <CalendarDaysIcon className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Add Schedule</h3>
                      <p className="text-sm text-gray-500">Create a new schedule entry</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAddScheduleModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Schedule Type</label>
                    <select className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm">
                      <option>Staff Shift</option>
                      <option>Resident Appointment</option>
                      <option>Facility Event</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Date</label>
                    <input
                      type="date"
                      defaultValue="2025-07-25"
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Start Time</label>
                      <input
                        type="time"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">End Time</label>
                      <input
                        type="time"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Schedule created successfully!');
                    setShowAddScheduleModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-purple-600 text-base font-medium text-white hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Create Schedule
                </button>
                <button
                  onClick={() => setShowAddScheduleModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Shift Modal */}
      {showAddShiftModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddShiftModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <UserGroupIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        {selectedShift ? 'Edit Shift' : 'Add Staff Shift'}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedShift ? 'Modify shift details' : 'Schedule a new staff shift'}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setShowAddShiftModal(false);
                      setSelectedShift(null);
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Staff Member</label>
                    <select
                      defaultValue={selectedShift?.staffName || ''}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="">Select staff member...</option>
                      <option value="Sarah Johnson">Sarah Johnson - RN</option>
                      <option value="Michael Chen">Michael Chen - Care Assistant</option>
                      <option value="Emily Rodriguez">Emily Rodriguez - RN</option>
                      <option value="David Wilson">David Wilson - LPN</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Shift Type</label>
                    <select
                      defaultValue={selectedShift?.shiftType || ''}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      <option value="day">Day Shift (6 AM - 2 PM)</option>
                      <option value="evening">Evening Shift (2 PM - 10 PM)</option>
                      <option value="night">Night Shift (10 PM - 6 AM)</option>
                      <option value="day_extended">Extended Day (7 AM - 7:30 PM)</option>
                      <option value="night_extended">Extended Night (7 PM - 7:30 AM)</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Date</label>
                    <input
                      type="date"
                      defaultValue={selectedShift?.date || "2025-07-25"}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Start Time</label>
                      <input
                        type="time"
                        defaultValue={selectedShift?.startTime || '07:00'}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">End Time</label>
                      <input
                        type="time"
                        defaultValue={selectedShift?.endTime || '19:30'}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert(selectedShift ? 'Shift updated successfully!' : 'Shift created successfully!');
                    setShowAddShiftModal(false);
                    setSelectedShift(null);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  {selectedShift ? 'Update Shift' : 'Create Shift'}
                </button>
                <button
                  onClick={() => {
                    setShowAddShiftModal(false);
                    setSelectedShift(null);
                  }}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Appointment Modal */}
      {showAddAppointmentModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddAppointmentModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                      <ClockIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        {selectedAppointment ? 'Edit Appointment' : 'Schedule Appointment'}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedAppointment ? 'Modify appointment details' : 'Schedule a new resident appointment'}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setShowAddAppointmentModal(false);
                      setSelectedAppointment(null);
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Resident</label>
                    <select
                      defaultValue={selectedAppointment?.residentName || ''}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                    >
                      <option value="">Select resident...</option>
                      <option value="John Smith">John Smith</option>
                      <option value="Mary Johnson">Mary Johnson</option>
                      <option value="Robert Davis">Robert Davis</option>
                      <option value="Linda Wilson">Linda Wilson</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Appointment Type</label>
                    <select
                      defaultValue={selectedAppointment?.type || ''}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                    >
                      <option value="Doctor Visit">Doctor Visit</option>
                      <option value="Physical Therapy">Physical Therapy</option>
                      <option value="Dental Cleaning">Dental Cleaning</option>
                      <option value="Eye Exam">Eye Exam</option>
                      <option value="Lab Work">Lab Work</option>
                      <option value="Specialist Consultation">Specialist Consultation</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Provider</label>
                    <input
                      type="text"
                      defaultValue={selectedAppointment?.provider || ''}
                      placeholder="Dr. Smith, PT Sarah, etc."
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Date</label>
                    <input
                      type="date"
                      defaultValue={selectedAppointment?.date || "2025-07-25"}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Time</label>
                      <input
                        type="time"
                        defaultValue={selectedAppointment?.time || '10:00'}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Duration (minutes)</label>
                      <input
                        type="number"
                        defaultValue={selectedAppointment?.duration || 60}
                        min="15"
                        step="15"
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notes</label>
                    <textarea
                      rows={3}
                      defaultValue={selectedAppointment?.notes || ''}
                      placeholder="Any special instructions or notes..."
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert(selectedAppointment ? 'Appointment updated successfully!' : 'Appointment scheduled successfully!');
                    setShowAddAppointmentModal(false);
                    setSelectedAppointment(null);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  {selectedAppointment ? 'Update Appointment' : 'Schedule Appointment'}
                </button>
                <button
                  onClick={() => {
                    setShowAddAppointmentModal(false);
                    setSelectedAppointment(null);
                  }}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Scheduling;
