import React, { useState } from 'react';
import {
  UserIcon,
  EyeIcon,
  EyeSlashIcon,
  ClipboardDocumentIcon,
  CheckIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import { DEMO_CREDENTIALS } from '../../utils/demoAccounts';

interface DemoCredentialsProps {
  onCredentialSelect?: (email: string, password: string) => void;
}

const DemoCredentials: React.FC<DemoCredentialsProps> = ({ onCredentialSelect }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showPasswords, setShowPasswords] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const handleCredentialClick = (email: string, password: string) => {
    if (onCredentialSelect) {
      onCredentialSelect(email, password);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'from-red-500 to-pink-600';
      case 'supervisor':
        return 'from-purple-500 to-blue-600';
      case 'caregiver':
        return 'from-green-500 to-emerald-600';
      case 'billing':
        return 'from-amber-500 to-yellow-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return '👩‍💼';
      case 'supervisor':
        return '👨‍⚕️';
      case 'caregiver':
        return '👩‍⚕️';
      case 'billing':
        return '👨‍💼';
      default:
        return '👤';
    }
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Full system access - All features and settings';
      case 'supervisor':
        return 'Operational management - Patient care, staff, purchasing';
      case 'caregiver':
        return 'Patient care focus - Assigned patients and AIMAR';
      case 'billing':
        return 'Financial management - Billing and cost analysis';
      default:
        return 'Standard user access';
    }
  };

  return (
    <div className="relative">
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="group flex items-center px-4 py-2 text-sm font-medium text-purple-600 hover:text-purple-700 bg-purple-50 hover:bg-purple-100 rounded-lg border border-purple-200 transition-all duration-200"
      >
        <UserIcon className="h-4 w-4 mr-2" />
        <span>Demo Accounts</span>
        <InformationCircleIcon className="h-4 w-4 ml-2 opacity-60" />
      </button>

      {/* Demo Credentials Panel */}
      {isOpen && (
        <div className="absolute top-12 left-0 right-0 z-50 bg-white rounded-2xl shadow-2xl border-4 border-purple-200 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-4 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-bold">Demo Account Credentials</h3>
                <p className="text-sm opacity-90">Click any account to auto-fill login form</p>
              </div>
              <button
                onClick={() => setShowPasswords(!showPasswords)}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors duration-200"
              >
                {showPasswords ? (
                  <EyeSlashIcon className="h-5 w-5" />
                ) : (
                  <EyeIcon className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>

          {/* Credentials List */}
          <div className="p-4 space-y-3 max-h-80 overflow-y-auto">
            {Object.entries(DEMO_CREDENTIALS).map(([role, creds]) => (
              <div
                key={role}
                className="group relative overflow-hidden bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 border-2 border-gray-200 hover:border-purple-300 transition-all duration-300 cursor-pointer transform hover:scale-105"
                onClick={() => handleCredentialClick(creds.email, creds.password)}
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-r ${getRoleColor(role)} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
                
                <div className="relative z-10">
                  {/* Role Header */}
                  <div className="flex items-center mb-3">
                    <div className={`p-2 bg-gradient-to-r ${getRoleColor(role)} rounded-xl text-white mr-3 shadow-lg`}>
                      <span className="text-lg">{getRoleIcon(role)}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-bold text-gray-900 capitalize">{creds.role}</h4>
                      <p className="text-sm text-gray-600">{getRoleDescription(role)}</p>
                    </div>
                  </div>

                  {/* Credentials */}
                  <div className="space-y-2">
                    {/* Email */}
                    <div className="flex items-center justify-between bg-white rounded-lg p-2 border border-gray-200">
                      <div className="flex-1">
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Email</label>
                        <div className="text-sm font-mono text-gray-900">{creds.email}</div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          copyToClipboard(creds.email, `${role}-email`);
                        }}
                        className="p-1 hover:bg-gray-100 rounded transition-colors duration-200"
                      >
                        {copiedField === `${role}-email` ? (
                          <CheckIcon className="h-4 w-4 text-green-500" />
                        ) : (
                          <ClipboardDocumentIcon className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>

                    {/* Password */}
                    <div className="flex items-center justify-between bg-white rounded-lg p-2 border border-gray-200">
                      <div className="flex-1">
                        <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Password</label>
                        <div className="text-sm font-mono text-gray-900">
                          {showPasswords ? creds.password : '••••••••••••••'}
                        </div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          copyToClipboard(creds.password, `${role}-password`);
                        }}
                        className="p-1 hover:bg-gray-100 rounded transition-colors duration-200"
                      >
                        {copiedField === `${role}-password` ? (
                          <CheckIcon className="h-4 w-4 text-green-500" />
                        ) : (
                          <ClipboardDocumentIcon className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>

                    {/* Access Level */}
                    <div className="bg-blue-50 rounded-lg p-2 border border-blue-200">
                      <label className="text-xs font-medium text-blue-600 uppercase tracking-wide">Access Level</label>
                      <div className="text-sm text-blue-800">{creds.access}</div>
                    </div>
                  </div>

                  {/* Click Hint */}
                  <div className="mt-3 text-center">
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      Click to auto-fill login form
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 p-4 border-t border-gray-200">
            <div className="flex items-center justify-center text-sm text-gray-600">
              <InformationCircleIcon className="h-4 w-4 mr-2" />
              <span>Demo accounts for testing different user roles and permissions</span>
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default DemoCredentials;
