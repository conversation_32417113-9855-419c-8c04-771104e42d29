import React from 'react';
import {
  UserGroupIcon,
  DocumentTextIcon,
  CubeIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  // Mock data - in real app, this would come from API
  const stats = [
    {
      name: 'Active Residents',
      value: '18',
      change: '+2 new admissions',
      changeType: 'positive',
      icon: UserGroupIcon,
    },
    {
      name: 'Care Assessments Due',
      value: '6',
      change: '2 overdue',
      changeType: 'warning',
      icon: DocumentTextIcon,
    },
    {
      name: 'Medication Alerts',
      value: '3',
      change: 'AI flagged interactions',
      changeType: 'warning',
      icon: ExclamationTriangleIcon,
    },
    {
      name: 'Compliance Score',
      value: '98%',
      change: '+2% this month',
      changeType: 'positive',
      icon: CheckCircleIcon,
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'care_assessment',
      message: 'Weekly care assessment completed for <PERSON>',
      time: '2 hours ago',
      icon: CheckCircleIcon,
      iconColor: 'text-secondary-500',
    },
    {
      id: 2,
      type: 'ai_alert',
      message: 'AI detected fall risk increase for William Anderson',
      time: '4 hours ago',
      icon: ExclamationTriangleIcon,
      iconColor: 'text-warning-500',
    },
    {
      id: 3,
      type: 'medication',
      message: 'Morning medications administered via eMAR',
      time: '6 hours ago',
      icon: CheckCircleIcon,
      iconColor: 'text-secondary-500',
    },
    {
      id: 4,
      type: 'compliance',
      message: 'Monthly compliance report generated',
      time: '1 day ago',
      icon: DocumentTextIcon,
      iconColor: 'text-primary-500',
    },
  ];

  const upcomingTasks = [
    {
      id: 1,
      task: 'Morning medication round - 6 residents',
      time: '9:00 AM',
      priority: 'high',
    },
    {
      id: 2,
      task: 'Care plan review with family - Margaret Thompson',
      time: '11:00 AM',
      priority: 'medium',
    },
    {
      id: 3,
      task: 'Monthly health assessment - Dorothy Garcia',
      time: '2:00 PM',
      priority: 'medium',
    },
    {
      id: 4,
      task: 'Staff training - New eMAR features',
      time: '4:00 PM',
      priority: 'low',
    },
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-danger-600 bg-danger-100';
      case 'medium':
        return 'text-warning-600 bg-warning-100';
      case 'low':
        return 'text-secondary-600 bg-secondary-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.first_name}!
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          AI-Enhanced Residential Care Management Dashboard
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            🏠 Home-Based Care Facility
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            🤖 AI-Powered EHR & eMAR
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            📊 Predictive Care Analytics
          </span>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="mobile-grid">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                </div>
              </div>
              <div className="mt-4">
                <p
                  className={`text-sm ${
                    stat.changeType === 'positive'
                      ? 'text-secondary-600'
                      : stat.changeType === 'warning'
                      ? 'text-warning-600'
                      : 'text-danger-600'
                  }`}
                >
                  {stat.change}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Recent Activities</h3>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <activity.icon className={`h-5 w-5 ${activity.iconColor}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Upcoming Tasks */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Today's Schedule</h3>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {upcomingTasks.map((task) => (
                <div key={task.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{task.task}</p>
                    <p className="text-xs text-gray-500">{task.time}</p>
                  </div>
                  <span
                    className={`badge ${getPriorityColor(task.priority)} capitalize`}
                  >
                    {task.priority}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
            <button className="btn-outline flex flex-col items-center p-4">
              <UserGroupIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Add Patient</span>
            </button>
            <button className="btn-outline flex flex-col items-center p-4">
              <DocumentTextIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Log Visit</span>
            </button>
            <button className="btn-outline flex flex-col items-center p-4">
              <CubeIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Update Inventory</span>
            </button>
            <button className="btn-outline flex flex-col items-center p-4">
              <CurrencyDollarIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Create Invoice</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
