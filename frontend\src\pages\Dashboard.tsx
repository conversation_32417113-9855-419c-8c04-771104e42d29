import React, { useState, useEffect } from 'react';
import {
  UserGroupIcon,
  DocumentTextIcon,
  CubeIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  BeakerIcon,
  ChartBarIcon,
  ClockIcon,
  BellIcon,
  SparklesIcon,
  HeartIcon,
  ShieldCheckIcon,
  CalendarDaysIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [notifications, setNotifications] = useState(3);

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // Mock data - in real app, this would come from API
  const stats = [
    {
      name: 'Active Residents',
      value: '18',
      change: '+2 new admissions',
      changeType: 'positive',
      icon: UserGroupIcon,
    },
    {
      name: 'Care Assessments Due',
      value: '6',
      change: '2 overdue',
      changeType: 'warning',
      icon: DocumentTextIcon,
    },
    {
      name: 'Medication Alerts',
      value: '3',
      change: 'AI flagged interactions',
      changeType: 'warning',
      icon: ExclamationTriangleIcon,
    },
    {
      name: 'Compliance Score',
      value: '98%',
      change: '+2% this month',
      changeType: 'positive',
      icon: CheckCircleIcon,
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'care_assessment',
      message: 'Weekly care assessment completed for Margaret Thompson',
      time: '2 hours ago',
      icon: CheckCircleIcon,
      iconColor: 'text-secondary-500',
    },
    {
      id: 2,
      type: 'ai_alert',
      message: 'AI detected fall risk increase for William Anderson',
      time: '4 hours ago',
      icon: ExclamationTriangleIcon,
      iconColor: 'text-warning-500',
    },
    {
      id: 3,
      type: 'medication',
      message: 'Morning medications administered via AIMAR',
      time: '6 hours ago',
      icon: CheckCircleIcon,
      iconColor: 'text-secondary-500',
    },
    {
      id: 4,
      type: 'compliance',
      message: 'Monthly compliance report generated',
      time: '1 day ago',
      icon: DocumentTextIcon,
      iconColor: 'text-primary-500',
    },
  ];

  const upcomingTasks = [
    {
      id: 1,
      task: 'Morning medication round - 6 residents',
      time: '9:00 AM',
      priority: 'high',
    },
    {
      id: 2,
      task: 'Care plan review with family - Margaret Thompson',
      time: '11:00 AM',
      priority: 'medium',
    },
    {
      id: 3,
      task: 'Monthly health assessment - Dorothy Garcia',
      time: '2:00 PM',
      priority: 'medium',
    },
    {
      id: 4,
      task: 'Staff training - New AIMAR features',
      time: '4:00 PM',
      priority: 'low',
    },
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-danger-600 bg-danger-100';
      case 'medium':
        return 'text-warning-600 bg-warning-100';
      case 'low':
        return 'text-secondary-600 bg-secondary-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-8">
      {/* Ultra-Interactive Emblem Header */}
      <div className="relative overflow-hidden bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-3xl p-10 text-white shadow-2xl border-4 border-gradient-to-r from-amber-400 via-yellow-500 to-amber-600">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-800/30 via-blue-800/30 to-indigo-800/30"></div>
        <div className="absolute top-0 right-0 w-80 h-80 bg-gradient-to-bl from-amber-400/20 to-yellow-500/20 rounded-full animate-pulse transform translate-x-40 -translate-y-40"></div>
        <div className="absolute bottom-0 left-0 w-60 h-60 bg-gradient-to-tr from-purple-400/20 to-blue-400/20 rounded-full animate-bounce transform -translate-x-30 translate-y-30"></div>
        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-gradient-to-r from-yellow-400/10 to-amber-500/10 rounded-full animate-spin transform -translate-x-16 -translate-y-16"></div>

        {/* Interactive Floating Orbs */}
        <div className="absolute top-20 right-20 w-4 h-4 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full animate-ping"></div>
        <div className="absolute bottom-20 left-20 w-3 h-3 bg-gradient-to-r from-purple-400 to-blue-500 rounded-full animate-pulse"></div>
        <div className="absolute top-40 left-40 w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-bounce"></div>

        <div className="relative z-10 flex justify-between items-start">
          <div className="flex-1">
            {/* Interactive Logo Section */}
            <div className="flex items-center mb-6 group cursor-pointer" onClick={() => alert('Care-SolAI - Next Generation Healthcare Management')}>
              <div className="relative p-3 bg-gradient-to-br from-amber-400 via-yellow-500 to-amber-600 rounded-2xl mr-6 shadow-2xl group-hover:scale-110 transition-all duration-300 border-2 border-yellow-300">
                <img
                  src="/care-solai-logo.jpg"
                  alt="Care-SolAI Logo"
                  className="h-12 w-12 object-cover rounded-xl"
                />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-pulse"></div>
              </div>
              <div className="group-hover:scale-105 transition-all duration-300">
                <h1 className="text-4xl font-black bg-gradient-to-r from-amber-300 via-yellow-400 to-amber-300 bg-clip-text text-transparent animate-pulse">
                  Welcome back, {user?.first_name}!
                </h1>
                <h2 className="text-xl font-bold bg-gradient-to-r from-blue-200 via-purple-200 to-blue-200 bg-clip-text text-transparent">
                  Care-SolAI Healthcare Dashboard
                </h2>
                <div className="flex items-center mt-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                  <span className="text-green-300 text-sm font-medium">All Systems Operational</span>
                </div>
              </div>
            </div>

            <p className="text-blue-100 max-w-3xl leading-relaxed text-lg mb-6">
              AI-Enhanced Residential Care Management Platform. Monitor patient care, track medications,
              manage staff, and ensure compliance with intelligent automation and predictive analytics.
            </p>

            {/* Interactive Feature Badges */}
            <div className="flex flex-wrap gap-4">
              <button className="group relative overflow-hidden px-6 py-3 rounded-2xl bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-bold shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-purple-400">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <HeartIcon className="h-5 w-5 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  <span>Residential Care</span>
                </div>
              </button>

              <button className="group relative overflow-hidden px-6 py-3 rounded-2xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white font-bold shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-blue-400">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <BeakerIcon className="h-5 w-5 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  <span>AI-Powered EHR</span>
                </div>
              </button>

              <button className="group relative overflow-hidden px-6 py-3 rounded-2xl bg-gradient-to-r from-amber-500 to-yellow-600 hover:from-amber-400 hover:to-yellow-500 text-purple-900 font-bold shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-yellow-400">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <SparklesIcon className="h-5 w-5 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  <span>AIMAR System</span>
                </div>
              </button>

              <button className="group relative overflow-hidden px-6 py-3 rounded-2xl bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-500 hover:to-purple-600 text-white font-bold shadow-xl transform hover:scale-105 transition-all duration-300 border-2 border-indigo-400">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <ChartBarIcon className="h-5 w-5 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  <span>Predictive Analytics</span>
                </div>
              </button>
            </div>
          </div>

          {/* Ultra-Interactive Status Section */}
          <div className="flex flex-col items-end space-y-6 ml-8">
            {/* Time and Date */}
            <div className="group text-right text-blue-100 cursor-pointer hover:scale-105 transition-all duration-300">
              <div className="flex items-center justify-end mb-2">
                <ClockIcon className="h-6 w-6 mr-2 text-amber-300" />
                <div className="text-2xl font-bold group-hover:text-amber-300 transition-colors duration-300">
                  {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>
              <div className="text-sm opacity-90 group-hover:text-yellow-300 transition-colors duration-300">
                {currentTime.toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            </div>

            {/* Notifications */}
            <button className="group relative overflow-hidden px-6 py-3 bg-gradient-to-br from-amber-400 via-yellow-500 to-amber-600 text-purple-900 font-bold rounded-2xl hover:from-yellow-400 hover:to-amber-400 transform hover:scale-110 hover:rotate-1 transition-all duration-300 shadow-2xl border-4 border-yellow-300">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center">
                <BellIcon className="h-6 w-6 mr-3 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300" />
                <div className="text-left">
                  <div className="text-sm font-black">Notifications</div>
                  <div className="text-xs font-bold opacity-90">{notifications} new alerts</div>
                </div>
              </div>
              {notifications > 0 && (
                <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-black leading-none text-white bg-gradient-to-r from-purple-600 to-blue-600 rounded-full shadow-xl animate-bounce border-2 border-white">
                  {notifications}
                </span>
              )}
            </button>

            {/* Real-time Activity Indicator */}
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-amber-400 rounded-full animate-ping"></div>
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
              <span className="text-xs text-blue-200 ml-2">Live Activity</span>
            </div>
          </div>
        </div>
      </div>

      {/* Ultra-Interactive Emblem Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
        {stats.map((stat, index) => (
          <div
            key={stat.name}
            className={`group relative overflow-hidden rounded-3xl p-8 text-white shadow-2xl transform hover:-translate-y-3 hover:rotate-1 transition-all duration-500 cursor-pointer border-4 ${
              index === 0 ? 'bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-900 border-blue-400/30 hover:border-amber-400 hover:shadow-blue-500/25' :
              index === 1 ? 'bg-gradient-to-br from-purple-600 via-purple-700 to-violet-900 border-purple-400/30 hover:border-amber-400 hover:shadow-purple-500/25' :
              index === 2 ? 'bg-gradient-to-br from-amber-500 via-yellow-600 to-orange-700 border-amber-400/50 hover:border-purple-400 hover:shadow-amber-500/25' :
              'bg-gradient-to-br from-indigo-600 via-blue-700 to-purple-900 border-indigo-400/30 hover:border-amber-400 hover:shadow-indigo-500/25'
            }`}
          >
            {/* Animated Background Elements */}
            <div className={`absolute inset-0 ${
              index === 0 ? 'bg-gradient-to-br from-blue-500/20 via-indigo-600/20 to-blue-800/20' :
              index === 1 ? 'bg-gradient-to-br from-purple-500/20 via-violet-600/20 to-purple-800/20' :
              index === 2 ? 'bg-gradient-to-br from-amber-400/20 via-yellow-500/20 to-orange-600/20' :
              'bg-gradient-to-br from-indigo-500/20 via-blue-600/20 to-purple-800/20'
            }`}></div>
            <div className={`absolute top-0 right-0 w-24 h-24 rounded-full opacity-0 group-hover:opacity-100 transform translate-x-8 -translate-y-8 group-hover:animate-pulse transition-all duration-500 ${
              index === 2 ? 'bg-gradient-to-bl from-purple-400/30 to-blue-500/30' : 'bg-gradient-to-bl from-amber-400/30 to-yellow-500/30'
            }`}></div>
            <div className={`absolute bottom-0 left-0 w-16 h-16 rounded-full animate-bounce ${
              index === 0 ? 'bg-gradient-to-tr from-blue-300/20 to-purple-400/20' :
              index === 1 ? 'bg-gradient-to-tr from-purple-300/20 to-blue-400/20' :
              index === 2 ? 'bg-gradient-to-tr from-yellow-300/20 to-amber-400/20' :
              'bg-gradient-to-tr from-indigo-300/20 to-purple-400/20'
            }`}></div>

            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div className={`relative p-4 rounded-2xl shadow-xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-2 ${
                  index === 2 ? 'bg-gradient-to-br from-purple-600 to-blue-700 border-purple-400' : 'bg-gradient-to-br from-amber-400 to-yellow-500 border-yellow-300'
                }`}>
                  <stat.icon className={`h-8 w-8 transition-colors duration-300 ${
                    index === 2 ? 'text-amber-300 group-hover:text-yellow-300' : 'text-purple-900 group-hover:text-blue-900'
                  }`} />
                  <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full animate-ping ${
                    index === 0 ? 'bg-gradient-to-r from-purple-500 to-blue-500' :
                    index === 1 ? 'bg-gradient-to-r from-red-500 to-orange-500' :
                    index === 2 ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                    'bg-gradient-to-r from-orange-500 to-red-500'
                  }`}></div>
                </div>
                <div className="text-right group-hover:scale-110 transition-all duration-300">
                  <div className="text-4xl font-black bg-gradient-to-r from-amber-300 to-yellow-400 bg-clip-text text-transparent group-hover:animate-pulse">
                    {stat.value}
                  </div>
                  <div className={`text-sm font-bold group-hover:text-amber-200 transition-colors duration-300 ${
                    index === 0 ? 'text-blue-200' :
                    index === 1 ? 'text-purple-200' :
                    index === 2 ? 'text-yellow-100' :
                    'text-indigo-200'
                  }`}>{stat.name}</div>
                </div>
              </div>
              <div className="space-y-3">
                <div className={`text-sm font-medium group-hover:text-amber-100 transition-colors duration-300 ${
                  index === 0 ? 'text-blue-100' :
                  index === 1 ? 'text-purple-100' :
                  index === 2 ? 'text-yellow-100' :
                  'text-indigo-100'
                }`}>
                  {stat.change}
                </div>
                <div className={`w-full rounded-full h-3 border ${
                  index === 0 ? 'bg-blue-800/50 border-blue-600' :
                  index === 1 ? 'bg-purple-800/50 border-purple-600' :
                  index === 2 ? 'bg-yellow-700/50 border-yellow-600' :
                  'bg-indigo-800/50 border-indigo-600'
                }`}>
                  <div className={`h-3 rounded-full transition-all duration-1000 shadow-lg group-hover:animate-pulse ${
                    index === 2 ? 'bg-gradient-to-r from-purple-500 via-blue-600 to-purple-500 w-11/12' : 'bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 w-3/4'
                  }`}></div>
                </div>
                <div className={`text-xs transition-colors duration-300 ${
                  index === 0 ? 'text-blue-300 group-hover:text-yellow-300' :
                  index === 1 ? 'text-purple-300 group-hover:text-yellow-300' :
                  index === 2 ? 'text-yellow-200 group-hover:text-purple-200' :
                  'text-indigo-300 group-hover:text-yellow-300'
                }`}>
                  Click for detailed view
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activities - Enhanced */}
        <div className="group relative overflow-hidden bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 rounded-3xl shadow-2xl border-4 border-purple-200/50 hover:border-amber-400 transition-all duration-500">
          {/* Animated Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-blue-100/30 to-indigo-100/30"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-amber-400/20 to-yellow-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

          <div className="relative z-10 p-8">
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mr-4 shadow-lg group-hover:scale-110 transition-all duration-300">
                <ClockIcon className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-700 to-blue-700 bg-clip-text text-transparent">
                Recent Activities
              </h3>
            </div>

            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div
                  key={activity.id}
                  className="group/item flex items-start space-x-4 p-4 rounded-2xl bg-white/70 hover:bg-gradient-to-r hover:from-purple-100/80 hover:to-blue-100/80 transition-all duration-300 border-2 border-transparent hover:border-amber-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <div className="flex-shrink-0">
                    <div className="p-2 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-xl shadow-md group-hover/item:scale-110 group-hover/item:rotate-12 transition-all duration-300">
                      <activity.icon className="h-5 w-5 text-purple-900" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 group-hover/item:text-purple-800 transition-colors duration-300">
                      {activity.message}
                    </p>
                    <p className="text-xs text-gray-500 group-hover/item:text-blue-600 transition-colors duration-300">
                      {activity.time}
                    </p>
                  </div>
                  <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Today's Schedule - Enhanced */}
        <div className="group relative overflow-hidden bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 rounded-3xl shadow-2xl border-4 border-amber-200/50 hover:border-purple-400 transition-all duration-500">
          {/* Animated Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-amber-100/30 via-yellow-100/30 to-orange-100/30"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-purple-400/20 to-blue-500/20 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

          <div className="relative z-10 p-8">
            <div className="flex items-center mb-6">
              <div className="p-3 bg-gradient-to-r from-amber-500 to-yellow-600 rounded-2xl mr-4 shadow-lg group-hover:scale-110 transition-all duration-300">
                <CalendarDaysIcon className="h-6 w-6 text-purple-900" />
              </div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-amber-700 to-orange-700 bg-clip-text text-transparent">
                Today's Schedule
              </h3>
            </div>

            <div className="space-y-4">
              {upcomingTasks.map((task, index) => (
                <div
                  key={task.id}
                  className="group/task flex items-center justify-between p-4 rounded-2xl bg-white/70 hover:bg-gradient-to-r hover:from-amber-100/80 hover:to-yellow-100/80 transition-all duration-300 border-2 border-transparent hover:border-purple-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <div className="flex items-center space-x-4 flex-1">
                    <div className="p-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl shadow-md group-hover/task:scale-110 group-hover/task:rotate-12 transition-all duration-300">
                      <CheckCircleIcon className="h-4 w-4 text-amber-300" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 group-hover/task:text-amber-800 transition-colors duration-300">
                        {task.task}
                      </p>
                      <p className="text-xs text-gray-500 group-hover/task:text-orange-600 transition-colors duration-300">
                        {task.time}
                      </p>
                    </div>
                  </div>
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-bold transition-all duration-300 group-hover/task:scale-110 ${
                      task.priority === 'high'
                        ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white'
                        : task.priority === 'medium'
                        ? 'bg-gradient-to-r from-amber-500 to-yellow-500 text-purple-900'
                        : 'bg-gradient-to-r from-green-500 to-emerald-500 text-white'
                    } shadow-lg border-2 border-white`}
                  >
                    {task.priority.toUpperCase()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
            <button className="btn-outline flex flex-col items-center p-4" onClick={() => window.location.href = '/patients'}>
              <UserGroupIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Add Patient</span>
            </button>
            <button className="btn-outline flex flex-col items-center p-4" onClick={() => window.location.href = '/visits'}>
              <DocumentTextIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Log Visit</span>
            </button>
            <button className="btn-outline flex flex-col items-center p-4" onClick={() => window.location.href = '/inventory'}>
              <CubeIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Update Inventory</span>
            </button>
            <button className="btn-outline flex flex-col items-center p-4" onClick={() => window.location.href = '/invoices'}>
              <CurrencyDollarIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Create Invoice</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
