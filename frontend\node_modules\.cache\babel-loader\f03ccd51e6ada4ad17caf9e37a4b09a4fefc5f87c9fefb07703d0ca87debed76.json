{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\medisyn\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState, useCallback } from 'react';\nimport { useSupabase } from './SupabaseContext';\nimport { authService } from '../services/authService';\nimport toast from 'react-hot-toast';\n\n// Types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Create context\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Provider component\n\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const {\n    supabase\n  } = useSupabase();\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const {\n          data: {\n            session\n          }\n        } = await supabase.auth.getSession();\n        if (session !== null && session !== void 0 && session.user) {\n          await loadUserProfile(session.user);\n        }\n      } catch (error) {\n        console.error('Error getting initial session:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    getInitialSession();\n\n    // Listen for auth changes\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      if (event === 'SIGNED_IN' && session !== null && session !== void 0 && session.user) {\n        await loadUserProfile(session.user);\n      } else if (event === 'SIGNED_OUT') {\n        setUser(null);\n      }\n      setLoading(false);\n    });\n    return () => subscription.unsubscribe();\n  }, [supabase.auth, loadUserProfile]);\n  const loadUserProfile = useCallback(async authUser => {\n    try {\n      // Get caregiver profile\n      const {\n        data: caregiver,\n        error\n      } = await supabase.from('caregivers').select('id, first_name, last_name, role, is_active').eq('user_id', authUser.id).single();\n      if (error) {\n        console.error('Error loading caregiver profile:', error);\n        return;\n      }\n      if (!caregiver.is_active) {\n        toast.error('Your account is inactive. Please contact an administrator.');\n        await logout();\n        return;\n      }\n      setUser({\n        ...authUser,\n        caregiver_id: caregiver.id,\n        role: caregiver.role,\n        first_name: caregiver.first_name,\n        last_name: caregiver.last_name\n      });\n    } catch (error) {\n      console.error('Error loading user profile:', error);\n    }\n  }, [supabase]);\n  const login = async (email, password) => {\n    try {\n      setLoading(true);\n      const response = await authService.login(email, password);\n\n      // The auth state change listener will handle setting the user\n      toast.success(`Welcome back, ${response.user.first_name}!`);\n    } catch (error) {\n      toast.error(error.message || 'Login failed');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const logout = async () => {\n    try {\n      await authService.logout();\n      setUser(null);\n      toast.success('Logged out successfully');\n    } catch (error) {\n      toast.error(error.message || 'Logout failed');\n      throw error;\n    }\n  };\n  const updateProfile = async data => {\n    try {\n      const updatedUser = await authService.updateProfile(data);\n      if (user) {\n        setUser({\n          ...user,\n          ...updatedUser\n        });\n      }\n      toast.success('Profile updated successfully');\n    } catch (error) {\n      toast.error(error.message || 'Profile update failed');\n      throw error;\n    }\n  };\n  const value = {\n    user,\n    loading,\n    login,\n    logout,\n    updateProfile\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook to use auth\n_s(AuthProvider, \"Q0HpbSj5YHeL4E3i1Q/bWQ3CLiE=\", false, function () {\n  return [useSupabase];\n});\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "useCallback", "useSupabase", "authService", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "loading", "setLoading", "supabase", "getInitialSession", "data", "session", "auth", "getSession", "loadUserProfile", "error", "console", "subscription", "onAuthStateChange", "event", "unsubscribe", "authUser", "caregiver", "from", "select", "eq", "id", "single", "is_active", "logout", "caregiver_id", "role", "first_name", "last_name", "login", "email", "password", "response", "success", "message", "updateProfile", "updatedUser", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';\nimport { User } from '@supabase/supabase-js';\nimport { useSupabase } from './SupabaseContext';\nimport { authService } from '../services/authService';\nimport toast from 'react-hot-toast';\n\n// Types\ninterface AuthUser extends User {\n  caregiver_id?: string;\n  role?: string;\n  first_name?: string;\n  last_name?: string;\n}\n\ninterface AuthContextType {\n  user: AuthUser | null;\n  loading: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  updateProfile: (data: any) => Promise<void>;\n}\n\n// Create context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Provider component\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<AuthUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const { supabase } = useSupabase();\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const { data: { session } } = await supabase.auth.getSession();\n        if (session?.user) {\n          await loadUserProfile(session.user);\n        }\n      } catch (error) {\n        console.error('Error getting initial session:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    getInitialSession();\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          await loadUserProfile(session.user);\n        } else if (event === 'SIGNED_OUT') {\n          setUser(null);\n        }\n        setLoading(false);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, [supabase.auth, loadUserProfile]);\n\n  const loadUserProfile = useCallback(async (authUser: User) => {\n    try {\n      // Get caregiver profile\n      const { data: caregiver, error } = await supabase\n        .from('caregivers')\n        .select('id, first_name, last_name, role, is_active')\n        .eq('user_id', authUser.id)\n        .single();\n\n      if (error) {\n        console.error('Error loading caregiver profile:', error);\n        return;\n      }\n\n      if (!caregiver.is_active) {\n        toast.error('Your account is inactive. Please contact an administrator.');\n        await logout();\n        return;\n      }\n\n      setUser({\n        ...authUser,\n        caregiver_id: caregiver.id,\n        role: caregiver.role,\n        first_name: caregiver.first_name,\n        last_name: caregiver.last_name,\n      });\n    } catch (error) {\n      console.error('Error loading user profile:', error);\n    }\n  }, [supabase]);\n\n  const login = async (email: string, password: string) => {\n    try {\n      setLoading(true);\n      const response = await authService.login(email, password);\n      \n      // The auth state change listener will handle setting the user\n      toast.success(`Welcome back, ${response.user.first_name}!`);\n    } catch (error: any) {\n      toast.error(error.message || 'Login failed');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authService.logout();\n      setUser(null);\n      toast.success('Logged out successfully');\n    } catch (error: any) {\n      toast.error(error.message || 'Logout failed');\n      throw error;\n    }\n  };\n\n  const updateProfile = async (data: any) => {\n    try {\n      const updatedUser = await authService.updateProfile(data);\n      if (user) {\n        setUser({\n          ...user,\n          ...updatedUser,\n        });\n      }\n      toast.success('Profile updated successfully');\n    } catch (error: any) {\n      toast.error(error.message || 'Profile update failed');\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    loading,\n    login,\n    logout,\n    updateProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook to use auth\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAmB,OAAO;AAErG,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAgBA;AACA,MAAMC,WAAW,gBAAGV,aAAa,CAA8BW,SAAS,CAAC;;AAEzE;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAkB,IAAI,CAAC;EACvD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM;IAAEgB;EAAS,CAAC,GAAGd,WAAW,CAAC,CAAC;EAElCH,SAAS,CAAC,MAAM;IACd;IACA,MAAMkB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF,MAAM;UAAEC,IAAI,EAAE;YAAEC;UAAQ;QAAE,CAAC,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAACC,UAAU,CAAC,CAAC;QAC9D,IAAIF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEP,IAAI,EAAE;UACjB,MAAMU,eAAe,CAACH,OAAO,CAACP,IAAI,CAAC;QACrC;MACF,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD,CAAC,SAAS;QACRR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDE,iBAAiB,CAAC,CAAC;;IAEnB;IACA,MAAM;MAAEC,IAAI,EAAE;QAAEO;MAAa;IAAE,CAAC,GAAGT,QAAQ,CAACI,IAAI,CAACM,iBAAiB,CAChE,OAAOC,KAAK,EAAER,OAAO,KAAK;MACxB,IAAIQ,KAAK,KAAK,WAAW,IAAIR,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEP,IAAI,EAAE;QAC1C,MAAMU,eAAe,CAACH,OAAO,CAACP,IAAI,CAAC;MACrC,CAAC,MAAM,IAAIe,KAAK,KAAK,YAAY,EAAE;QACjCd,OAAO,CAAC,IAAI,CAAC;MACf;MACAE,UAAU,CAAC,KAAK,CAAC;IACnB,CACF,CAAC;IAED,OAAO,MAAMU,YAAY,CAACG,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,CAACZ,QAAQ,CAACI,IAAI,EAAEE,eAAe,CAAC,CAAC;EAEpC,MAAMA,eAAe,GAAGrB,WAAW,CAAC,MAAO4B,QAAc,IAAK;IAC5D,IAAI;MACF;MACA,MAAM;QAAEX,IAAI,EAAEY,SAAS;QAAEP;MAAM,CAAC,GAAG,MAAMP,QAAQ,CAC9Ce,IAAI,CAAC,YAAY,CAAC,CAClBC,MAAM,CAAC,4CAA4C,CAAC,CACpDC,EAAE,CAAC,SAAS,EAAEJ,QAAQ,CAACK,EAAE,CAAC,CAC1BC,MAAM,CAAC,CAAC;MAEX,IAAIZ,KAAK,EAAE;QACTC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD;MACF;MAEA,IAAI,CAACO,SAAS,CAACM,SAAS,EAAE;QACxBhC,KAAK,CAACmB,KAAK,CAAC,4DAA4D,CAAC;QACzE,MAAMc,MAAM,CAAC,CAAC;QACd;MACF;MAEAxB,OAAO,CAAC;QACN,GAAGgB,QAAQ;QACXS,YAAY,EAAER,SAAS,CAACI,EAAE;QAC1BK,IAAI,EAAET,SAAS,CAACS,IAAI;QACpBC,UAAU,EAAEV,SAAS,CAACU,UAAU;QAChCC,SAAS,EAAEX,SAAS,CAACW;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAM0B,KAAK,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAK;IACvD,IAAI;MACF7B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM8B,QAAQ,GAAG,MAAM1C,WAAW,CAACuC,KAAK,CAACC,KAAK,EAAEC,QAAQ,CAAC;;MAEzD;MACAxC,KAAK,CAAC0C,OAAO,CAAC,iBAAiBD,QAAQ,CAACjC,IAAI,CAAC4B,UAAU,GAAG,CAAC;IAC7D,CAAC,CAAC,OAAOjB,KAAU,EAAE;MACnBnB,KAAK,CAACmB,KAAK,CAACA,KAAK,CAACwB,OAAO,IAAI,cAAc,CAAC;MAC5C,MAAMxB,KAAK;IACb,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMlC,WAAW,CAACkC,MAAM,CAAC,CAAC;MAC1BxB,OAAO,CAAC,IAAI,CAAC;MACbT,KAAK,CAAC0C,OAAO,CAAC,yBAAyB,CAAC;IAC1C,CAAC,CAAC,OAAOvB,KAAU,EAAE;MACnBnB,KAAK,CAACmB,KAAK,CAACA,KAAK,CAACwB,OAAO,IAAI,eAAe,CAAC;MAC7C,MAAMxB,KAAK;IACb;EACF,CAAC;EAED,MAAMyB,aAAa,GAAG,MAAO9B,IAAS,IAAK;IACzC,IAAI;MACF,MAAM+B,WAAW,GAAG,MAAM9C,WAAW,CAAC6C,aAAa,CAAC9B,IAAI,CAAC;MACzD,IAAIN,IAAI,EAAE;QACRC,OAAO,CAAC;UACN,GAAGD,IAAI;UACP,GAAGqC;QACL,CAAC,CAAC;MACJ;MACA7C,KAAK,CAAC0C,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC,CAAC,OAAOvB,KAAU,EAAE;MACnBnB,KAAK,CAACmB,KAAK,CAACA,KAAK,CAACwB,OAAO,IAAI,uBAAuB,CAAC;MACrD,MAAMxB,KAAK;IACb;EACF,CAAC;EAED,MAAM2B,KAAK,GAAG;IACZtC,IAAI;IACJE,OAAO;IACP4B,KAAK;IACLL,MAAM;IACNW;EACF,CAAC;EAED,oBACE1C,OAAA,CAACC,WAAW,CAAC4C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAxC,QAAA,EAChCA;EAAQ;IAAA0C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA5C,EAAA,CA9HaF,YAAyC;EAAA,QAG/BP,WAAW;AAAA;AAAAsD,EAAA,GAHrB/C,YAAyC;AA+HtD,OAAO,MAAMgD,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAG7D,UAAU,CAACS,WAAW,CAAC;EACvC,IAAIoD,OAAO,KAAKnD,SAAS,EAAE;IACzB,MAAM,IAAIoD,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}