import React, { useState } from 'react';

interface Visitor {
  id: string;
  visitorName: string;
  residentVisited: string;
  purpose: string;
  date: string;
  timeIn: string;
  timeOut: string;
  notes: string;
}

const initialVisitors: Visitor[] = [
  {
    id: '1',
    visitorName: '<PERSON>',
    residentVisited: '<PERSON>',
    purpose: 'Family Visit',
    date: '2025-07-29',
    timeIn: '09:00',
    timeOut: '10:00',
    notes: 'Brought flowers.'
  },
  {
    id: '2',
    visitorName: 'Dr. <PERSON>',
    residentVisited: '<PERSON>',
    purpose: 'Medical Check',
    date: '2025-07-29',
    timeIn: '10:30',
    timeOut: '11:00',
    notes: 'Routine checkup.'
  }
];

const Visits: React.FC = () => {
  const [visitors, setVisitors] = useState<Visitor[]>(initialVisitors);
  const [form, setForm] = useState<Omit<Visitor, 'id'>>({
    visitorName: '',
    residentVisited: '',
    purpose: '',
    date: '',
    timeIn: '',
    timeOut: '',
    notes: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!form.visitorName || !form.residentVisited || !form.purpose || !form.date || !form.timeIn) {
      alert('Please fill in all required fields (Name, Resident, Purpose, Date, Time In).');
      return;
    }
    setVisitors([
      {
        id: String(Date.now()),
        ...form
      },
      ...visitors
    ]);
    setForm({ visitorName: '', residentVisited: '', purpose: '', date: '', timeIn: '', timeOut: '', notes: '' });
  };

  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Visitor Log</h1>
      <form onSubmit={handleSubmit} className="bg-white shadow rounded-lg p-6 mb-8 space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Visitor Name*</label>
            <input
              type="text"
              name="visitorName"
              value={form.visitorName}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Resident Visited*</label>
            <input
              type="text"
              name="residentVisited"
              value={form.residentVisited}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Purpose of Visit*</label>
            <input
              type="text"
              name="purpose"
              value={form.purpose}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Date*</label>
            <input
              type="date"
              name="date"
              value={form.date}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Time In*</label>
            <input
              type="time"
              name="timeIn"
              value={form.timeIn}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Time Out</label>
            <input
              type="time"
              name="timeOut"
              value={form.timeOut}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Notes</label>
          <textarea
            name="notes"
            value={form.notes}
            onChange={handleChange}
            rows={2}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            placeholder="Additional notes about the visit..."
          />
        </div>
        <div className="flex justify-end">
          <button type="submit" className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
            Log Visitor
          </button>
        </div>
      </form>
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-semibold mb-4">Recent Visitors</h2>
        <ul className="divide-y divide-gray-200">
          {visitors.map(visitor => (
            <li key={visitor.id} className="py-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-gray-900">{visitor.visitorName}</p>
                  <p className="text-sm text-gray-600">Resident: {visitor.residentVisited}</p>
                  <p className="text-xs text-gray-500">Purpose: {visitor.purpose}</p>
                  <p className="text-xs text-gray-500">{visitor.date} | In: {visitor.timeIn}{visitor.timeOut && ` | Out: ${visitor.timeOut}`}</p>
                  {visitor.notes && <p className="text-xs text-gray-700 mt-1">Notes: {visitor.notes}</p>}
                </div>
              </div>
            </li>
          ))}
        </ul>
        {visitors.length === 0 && <p className="text-gray-500">No visitors logged yet.</p>}
      </div>
    </div>
  );
};

export default Visits;
