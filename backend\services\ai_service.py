"""
AI Service for CareSyncAI
Integrates machine learning models for predictions and insights
"""

import sys
import os
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta, date
import json

# Add AI module to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'ai'))

from core.database import DatabaseService, get_db
from core.config import settings

logger = logging.getLogger(__name__)

class AIService:
    """Service for AI/ML operations"""
    
    def __init__(self):
        self.db_service = DatabaseService("ai_predictions")
        self.client = get_db()
        self.fall_risk_predictor = None
        self.nlp_analyzer = None
        
        # Initialize AI models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize AI models"""
        try:
            # Import AI modules (with fallback if not available)
            try:
                from fall_risk_predictor import FallRiskPredictor
                self.fall_risk_predictor = FallRiskPredictor()
                logger.info("Fall risk predictor initialized")
            except ImportError as e:
                logger.warning(f"Could not import fall risk predictor: {e}")
            
            try:
                from nlp_analyzer import CaregiverNotesAnalyzer
                self.nlp_analyzer = CaregiverNotesAnalyzer()
                logger.info("NLP analyzer initialized")
            except ImportError as e:
                logger.warning(f"Could not import NLP analyzer: {e}")
                
        except Exception as e:
            logger.error(f"Error initializing AI models: {e}")
    
    async def get_patient_data_for_ai(self, patient_id: str) -> Optional[Dict[str, Any]]:
        """Get patient data formatted for AI analysis"""
        try:
            # Get patient basic info
            patient_result = self.client.table("patients").select("*").eq("id", patient_id).execute()
            
            if not patient_result.data:
                return None
            
            patient = patient_result.data[0]
            
            # Get recent medical records
            medical_records_result = self.client.table("medical_records").select(
                "vitals, symptoms, notes, record_date, pain_level"
            ).eq("patient_id", patient_id).order(
                "record_date", desc=True
            ).limit(10).execute()
            
            # Get recent AI predictions
            predictions_result = self.client.table("ai_predictions").select(
                "*"
            ).eq("patient_id", patient_id).eq(
                "is_active", True
            ).order("created_at", desc=True).limit(5).execute()
            
            # Format data for AI
            ai_data = {
                'id': patient['id'],
                'date_of_birth': patient['date_of_birth'],
                'medical_conditions': patient.get('medical_conditions', []),
                'medications': patient.get('medications', []),
                'mobility_score': patient.get('mobility_score', 5),
                'cognitive_score': patient.get('cognitive_score', 10),
                'fall_risk_score': patient.get('fall_risk_score', 0.0),
                'care_level': patient.get('care_level', 1),
                'recent_vitals': [record.get('vitals', {}) for record in medical_records_result.data],
                'recent_symptoms': [record.get('symptoms', []) for record in medical_records_result.data],
                'recent_notes': [record.get('notes', '') for record in medical_records_result.data if record.get('notes')],
                'recent_pain_levels': [record.get('pain_level') for record in medical_records_result.data if record.get('pain_level') is not None],
                'existing_predictions': predictions_result.data
            }
            
            return ai_data
        except Exception as e:
            logger.error(f"Error getting patient data for AI: {e}")
            raise
    
    async def predict_fall_risk(
        self, 
        patient_data: Dict[str, Any], 
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """Predict fall risk for a patient"""
        try:
            # Check for existing recent prediction
            if not force_refresh:
                existing_predictions = patient_data.get('existing_predictions', [])
                for pred in existing_predictions:
                    if (pred.get('prediction_type') == 'fall_risk' and 
                        pred.get('created_at')):
                        created_at = datetime.fromisoformat(pred['created_at'].replace('Z', '+00:00'))
                        if datetime.now() - created_at < timedelta(hours=24):
                            return pred
            
            # Generate new prediction
            if self.fall_risk_predictor:
                prediction = self.fall_risk_predictor.predict_fall_risk(patient_data)
            else:
                # Fallback rule-based prediction
                prediction = self._rule_based_fall_risk_prediction(patient_data)
            
            # Add metadata
            prediction.update({
                'prediction_type': 'fall_risk',
                'patient_id': patient_data['id'],
                'created_at': datetime.now().isoformat(),
                'is_active': True
            })
            
            return prediction
        except Exception as e:
            logger.error(f"Error predicting fall risk: {e}")
            raise
    
    def _rule_based_fall_risk_prediction(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback rule-based fall risk prediction"""
        try:
            # Calculate age
            if isinstance(patient_data.get('date_of_birth'), str):
                birth_date = datetime.strptime(patient_data['date_of_birth'], '%Y-%m-%d').date()
            else:
                birth_date = patient_data.get('date_of_birth', date.today())
            
            age = (date.today() - birth_date).days / 365.25
            
            # Rule-based scoring
            risk_score = 0.0
            factors = {}
            
            # Age factor (0-0.3)
            age_factor = min(0.3, max(0, (age - 65) * 0.01))
            risk_score += age_factor
            factors['age_factor'] = age_factor
            
            # Mobility factor (0-0.4)
            mobility_score = patient_data.get('mobility_score', 5)
            mobility_factor = (10 - mobility_score) * 0.04
            risk_score += mobility_factor
            factors['mobility_factor'] = mobility_factor
            
            # Cognitive factor (0-0.2)
            cognitive_score = patient_data.get('cognitive_score', 10)
            cognitive_factor = (10 - cognitive_score) * 0.02
            risk_score += cognitive_factor
            factors['cognitive_factor'] = cognitive_factor
            
            # Medication factor (0-0.1)
            medications = patient_data.get('medications', [])
            med_count = len(medications) if isinstance(medications, list) else 0
            medication_factor = min(0.1, med_count * 0.01)
            risk_score += medication_factor
            factors['medication_factor'] = medication_factor
            
            # Medical conditions factor (0-0.2)
            conditions = patient_data.get('medical_conditions', [])
            high_risk_conditions = ['dementia', 'parkinsons', 'stroke', 'diabetes']
            condition_factor = 0.0
            for condition in conditions:
                if any(hrc in condition.lower() for hrc in high_risk_conditions):
                    condition_factor = 0.2
                    break
            risk_score += condition_factor
            factors['condition_factor'] = condition_factor
            
            # Cap at 1.0
            risk_score = min(1.0, risk_score)
            
            # Generate recommendations
            recommendations = self._generate_fall_risk_recommendations(patient_data, risk_score)
            
            return {
                'risk_probability': float(risk_score),
                'risk_level': 'high' if risk_score > 0.6 else 'medium' if risk_score > 0.3 else 'low',
                'confidence': 0.8,
                'contributing_factors': factors,
                'recommendations': recommendations,
                'model_version': 'rule_based_v1.0'
            }
            
        except Exception as e:
            logger.error(f"Error in rule-based fall risk prediction: {e}")
            raise
    
    def _generate_fall_risk_recommendations(
        self, 
        patient_data: Dict[str, Any], 
        risk_score: float
    ) -> List[str]:
        """Generate fall risk recommendations"""
        recommendations = []
        
        if risk_score > 0.6:
            recommendations.append("High fall risk detected. Implement immediate fall prevention measures.")
            recommendations.append("Consider home safety assessment and modifications.")
            recommendations.append("Increase supervision and monitoring frequency.")
        
        if patient_data.get('mobility_score', 5) < 5:
            recommendations.append("Consider physical therapy evaluation for mobility improvement.")
            recommendations.append("Implement balance and strength training exercises.")
        
        if patient_data.get('cognitive_score', 10) < 7:
            recommendations.append("Monitor for confusion and disorientation.")
            recommendations.append("Consider cognitive assessment and support strategies.")
        
        medications = patient_data.get('medications', [])
        if len(medications) > 5:
            recommendations.append("Review medication list for fall-risk medications.")
            recommendations.append("Consider medication reconciliation with physician.")
        
        if not recommendations:
            recommendations.append("Continue current care plan and monitor for changes.")
            recommendations.append("Maintain regular exercise and mobility activities.")
        
        return recommendations
    
    async def predict_health_decline(
        self, 
        patient_data: Dict[str, Any], 
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """Predict health decline risk"""
        try:
            # Simple rule-based health decline prediction
            decline_score = 0.0
            factors = {}
            
            # Recent vitals trend
            recent_vitals = patient_data.get('recent_vitals', [])
            if recent_vitals:
                # Check for concerning vital signs patterns
                for vitals in recent_vitals[:5]:  # Last 5 records
                    if vitals.get('heart_rate', 0) > 100 or vitals.get('heart_rate', 0) < 60:
                        decline_score += 0.1
                    if vitals.get('systolic_bp', 0) > 160 or vitals.get('systolic_bp', 0) < 90:
                        decline_score += 0.1
                    if vitals.get('oxygen_saturation', 100) < 95:
                        decline_score += 0.2
            
            # Recent pain levels
            recent_pain = patient_data.get('recent_pain_levels', [])
            if recent_pain:
                avg_pain = sum(recent_pain) / len(recent_pain)
                if avg_pain > 7:
                    decline_score += 0.2
                    factors['high_pain_levels'] = avg_pain
            
            # Care level and mobility
            care_level = patient_data.get('care_level', 1)
            mobility_score = patient_data.get('mobility_score', 5)
            
            if care_level >= 4:
                decline_score += 0.2
                factors['high_care_needs'] = care_level
            
            if mobility_score <= 3:
                decline_score += 0.2
                factors['low_mobility'] = mobility_score
            
            decline_score = min(1.0, decline_score)
            
            return {
                'prediction_type': 'health_decline',
                'patient_id': patient_data['id'],
                'risk_probability': float(decline_score),
                'risk_level': 'high' if decline_score > 0.6 else 'medium' if decline_score > 0.3 else 'low',
                'confidence': 0.7,
                'contributing_factors': factors,
                'recommendations': self._generate_health_decline_recommendations(decline_score),
                'model_version': 'rule_based_v1.0',
                'created_at': datetime.now().isoformat(),
                'is_active': True
            }
        except Exception as e:
            logger.error(f"Error predicting health decline: {e}")
            raise
    
    def _generate_health_decline_recommendations(self, decline_score: float) -> List[str]:
        """Generate health decline recommendations"""
        recommendations = []
        
        if decline_score > 0.6:
            recommendations.append("Monitor patient closely for signs of health deterioration.")
            recommendations.append("Consider increasing frequency of medical assessments.")
            recommendations.append("Review and adjust care plan as needed.")
        elif decline_score > 0.3:
            recommendations.append("Continue monitoring vital signs and overall condition.")
            recommendations.append("Maintain current care interventions.")
        else:
            recommendations.append("Patient appears stable. Continue routine monitoring.")
        
        return recommendations
    
    async def predict_medication_adherence(
        self, 
        patient_data: Dict[str, Any], 
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """Predict medication adherence risk"""
        try:
            adherence_score = 0.8  # Start with good adherence assumption
            factors = {}
            
            # Cognitive score impact
            cognitive_score = patient_data.get('cognitive_score', 10)
            if cognitive_score < 7:
                adherence_score -= 0.3
                factors['cognitive_impairment'] = cognitive_score
            
            # Number of medications
            medications = patient_data.get('medications', [])
            med_count = len(medications) if isinstance(medications, list) else 0
            if med_count > 5:
                adherence_score -= 0.2
                factors['medication_complexity'] = med_count
            
            # Recent notes analysis for medication refusal
            recent_notes = patient_data.get('recent_notes', [])
            refusal_mentions = 0
            for note in recent_notes:
                if 'refused' in note.lower() and 'medication' in note.lower():
                    refusal_mentions += 1
            
            if refusal_mentions > 0:
                adherence_score -= 0.3
                factors['medication_refusal_history'] = refusal_mentions
            
            adherence_score = max(0.0, min(1.0, adherence_score))
            
            return {
                'prediction_type': 'medication_adherence',
                'patient_id': patient_data['id'],
                'adherence_probability': float(adherence_score),
                'risk_level': 'low' if adherence_score > 0.7 else 'medium' if adherence_score > 0.4 else 'high',
                'confidence': 0.75,
                'contributing_factors': factors,
                'recommendations': self._generate_adherence_recommendations(adherence_score, factors),
                'model_version': 'rule_based_v1.0',
                'created_at': datetime.now().isoformat(),
                'is_active': True
            }
        except Exception as e:
            logger.error(f"Error predicting medication adherence: {e}")
            raise
    
    def _generate_adherence_recommendations(self, adherence_score: float, factors: Dict) -> List[str]:
        """Generate medication adherence recommendations"""
        recommendations = []
        
        if adherence_score < 0.4:
            recommendations.append("High risk of medication non-adherence detected.")
            recommendations.append("Consider simplified medication regimen.")
            recommendations.append("Implement medication reminders and supervision.")
        
        if 'cognitive_impairment' in factors:
            recommendations.append("Provide additional support for medication management due to cognitive concerns.")
        
        if 'medication_complexity' in factors:
            recommendations.append("Review medication list with physician for potential simplification.")
        
        if 'medication_refusal_history' in factors:
            recommendations.append("Address underlying reasons for medication refusal.")
            recommendations.append("Consider alternative administration methods.")
        
        if not recommendations:
            recommendations.append("Continue current medication management approach.")
        
        return recommendations

    async def analyze_caregiver_note(
        self,
        note_text: str,
        patient_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze caregiver note using NLP"""
        try:
            if self.nlp_analyzer:
                # Get patient context if available
                patient_context = None
                if patient_id:
                    patient_context = await self.get_patient_data_for_ai(patient_id)

                analysis = self.nlp_analyzer.analyze_note(note_text, patient_context)
            else:
                # Fallback rule-based analysis
                analysis = self._rule_based_note_analysis(note_text)

            return analysis
        except Exception as e:
            logger.error(f"Error analyzing caregiver note: {e}")
            raise

    def _rule_based_note_analysis(self, note_text: str) -> Dict[str, Any]:
        """Fallback rule-based note analysis"""
        try:
            text_lower = note_text.lower()

            # Simple sentiment analysis
            positive_words = ['good', 'well', 'better', 'improved', 'stable', 'cooperative', 'alert']
            negative_words = ['poor', 'worse', 'declined', 'confused', 'agitated', 'refused', 'pain']

            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)

            if positive_count > negative_count:
                sentiment = 'positive'
            elif negative_count > positive_count:
                sentiment = 'negative'
            else:
                sentiment = 'neutral'

            # Identify concerns
            concerns = []
            if any(word in text_lower for word in ['fall', 'fell', 'trip']):
                concerns.append({'type': 'fall_incident', 'severity': 'high'})
            if any(word in text_lower for word in ['refused', 'declined']):
                concerns.append({'type': 'compliance', 'severity': 'medium'})
            if any(word in text_lower for word in ['pain', 'hurt', 'ache']):
                concerns.append({'type': 'pain', 'severity': 'medium'})

            # Urgency assessment
            urgent_keywords = ['emergency', 'urgent', 'severe', 'critical']
            urgency_level = 'high' if any(word in text_lower for word in urgent_keywords) else 'low'

            return {
                'note_text': note_text,
                'sentiment': {'overall': sentiment},
                'concerns': concerns,
                'urgency_level': {'level': urgency_level},
                'recommendations': self._generate_note_recommendations(concerns),
                'summary': note_text[:100] + "..." if len(note_text) > 100 else note_text,
                'analysis_method': 'rule_based',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error in rule-based note analysis: {e}")
            raise

    def _generate_note_recommendations(self, concerns: List[Dict]) -> List[str]:
        """Generate recommendations based on note concerns"""
        recommendations = []

        for concern in concerns:
            if concern['type'] == 'fall_incident':
                recommendations.append("Implement fall prevention measures immediately")
                recommendations.append("Assess for injuries and document incident")
            elif concern['type'] == 'compliance':
                recommendations.append("Address compliance issues with patient and family")
                recommendations.append("Consider alternative approaches or interventions")
            elif concern['type'] == 'pain':
                recommendations.append("Assess pain levels and consider pain management")
                recommendations.append("Monitor pain medication effectiveness")

        if not recommendations:
            recommendations.append("Continue current care approach")

        return recommendations

    async def get_patient_predictions(self, patient_id: str) -> List[Dict[str, Any]]:
        """Get all predictions for a patient"""
        try:
            result = self.client.table("ai_predictions").select(
                "*"
            ).eq("patient_id", patient_id).eq(
                "is_active", True
            ).order("created_at", desc=True).execute()

            return result.data or []
        except Exception as e:
            logger.error(f"Error getting patient predictions: {e}")
            raise

    async def save_prediction(self, prediction: Dict[str, Any], created_by: str) -> None:
        """Save prediction to database"""
        try:
            # Prepare prediction data for database
            db_prediction = {
                'patient_id': prediction['patient_id'],
                'prediction_type': prediction['prediction_type'],
                'prediction_value': prediction.get('risk_probability', 0.0),
                'confidence_score': prediction.get('confidence', 0.0),
                'factors': prediction.get('contributing_factors', {}),
                'recommendation': '\n'.join(prediction.get('recommendations', [])),
                'model_version': prediction.get('model_version', 'unknown'),
                'is_active': True,
                'expires_at': (datetime.now() + timedelta(days=30)).isoformat()
            }

            await self.db_service.create(db_prediction)
            logger.info(f"Prediction saved for patient {prediction['patient_id']}")
        except Exception as e:
            logger.error(f"Error saving prediction: {e}")
            raise

    async def analyze_health_trends(self, patient_id: str, days: int = 30) -> Dict[str, Any]:
        """Analyze health trends for a patient"""
        try:
            # Get medical records for the specified period
            start_date = (datetime.now() - timedelta(days=days)).isoformat()

            records_result = self.client.table("medical_records").select(
                "record_date, vitals, pain_level, mood_assessment, symptoms"
            ).eq("patient_id", patient_id).gte(
                "record_date", start_date
            ).order("record_date", desc=False).execute()

            records = records_result.data or []

            if not records:
                return {
                    'patient_id': patient_id,
                    'period_days': days,
                    'trends': {},
                    'message': 'No data available for trend analysis'
                }

            # Analyze trends
            trends = {
                'vital_signs': self._analyze_vital_trends(records),
                'pain_levels': self._analyze_pain_trends(records),
                'mood_patterns': self._analyze_mood_trends(records),
                'symptom_frequency': self._analyze_symptom_trends(records)
            }

            return {
                'patient_id': patient_id,
                'period_days': days,
                'total_records': len(records),
                'trends': trends,
                'analysis_date': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error analyzing health trends: {e}")
            raise

    def _analyze_vital_trends(self, records: List[Dict]) -> Dict[str, Any]:
        """Analyze vital signs trends"""
        try:
            heart_rates = []
            blood_pressures = []
            temperatures = []

            for record in records:
                vitals = record.get('vitals', {})
                if vitals.get('heart_rate'):
                    heart_rates.append(vitals['heart_rate'])
                if vitals.get('systolic_bp') and vitals.get('diastolic_bp'):
                    blood_pressures.append((vitals['systolic_bp'], vitals['diastolic_bp']))
                if vitals.get('temperature'):
                    temperatures.append(vitals['temperature'])

            trends = {}

            if heart_rates:
                trends['heart_rate'] = {
                    'average': sum(heart_rates) / len(heart_rates),
                    'trend': 'stable',  # Simplified - would need more complex analysis
                    'latest': heart_rates[-1] if heart_rates else None
                }

            if blood_pressures:
                avg_systolic = sum(bp[0] for bp in blood_pressures) / len(blood_pressures)
                avg_diastolic = sum(bp[1] for bp in blood_pressures) / len(blood_pressures)
                trends['blood_pressure'] = {
                    'average_systolic': avg_systolic,
                    'average_diastolic': avg_diastolic,
                    'trend': 'stable'
                }

            if temperatures:
                trends['temperature'] = {
                    'average': sum(temperatures) / len(temperatures),
                    'trend': 'stable'
                }

            return trends
        except Exception as e:
            logger.error(f"Error analyzing vital trends: {e}")
            return {}

    def _analyze_pain_trends(self, records: List[Dict]) -> Dict[str, Any]:
        """Analyze pain level trends"""
        try:
            pain_levels = [record.get('pain_level') for record in records if record.get('pain_level') is not None]

            if not pain_levels:
                return {'message': 'No pain data available'}

            avg_pain = sum(pain_levels) / len(pain_levels)

            return {
                'average_pain': avg_pain,
                'latest_pain': pain_levels[-1],
                'trend': 'improving' if pain_levels[-1] < avg_pain else 'worsening' if pain_levels[-1] > avg_pain else 'stable',
                'total_assessments': len(pain_levels)
            }
        except Exception as e:
            logger.error(f"Error analyzing pain trends: {e}")
            return {}

    def _analyze_mood_trends(self, records: List[Dict]) -> Dict[str, Any]:
        """Analyze mood assessment trends"""
        try:
            moods = [record.get('mood_assessment') for record in records if record.get('mood_assessment')]

            if not moods:
                return {'message': 'No mood data available'}

            mood_counts = {}
            for mood in moods:
                mood_counts[mood] = mood_counts.get(mood, 0) + 1

            most_common_mood = max(mood_counts, key=mood_counts.get) if mood_counts else None

            return {
                'most_common_mood': most_common_mood,
                'mood_distribution': mood_counts,
                'latest_mood': moods[-1] if moods else None,
                'total_assessments': len(moods)
            }
        except Exception as e:
            logger.error(f"Error analyzing mood trends: {e}")
            return {}

    def _analyze_symptom_trends(self, records: List[Dict]) -> Dict[str, Any]:
        """Analyze symptom frequency trends"""
        try:
            all_symptoms = []
            for record in records:
                symptoms = record.get('symptoms', [])
                if symptoms:
                    all_symptoms.extend(symptoms)

            if not all_symptoms:
                return {'message': 'No symptom data available'}

            symptom_counts = {}
            for symptom in all_symptoms:
                symptom_counts[symptom] = symptom_counts.get(symptom, 0) + 1

            # Sort by frequency
            sorted_symptoms = sorted(symptom_counts.items(), key=lambda x: x[1], reverse=True)

            return {
                'most_frequent_symptoms': sorted_symptoms[:5],
                'total_unique_symptoms': len(symptom_counts),
                'total_symptom_reports': len(all_symptoms)
            }
        except Exception as e:
            logger.error(f"Error analyzing symptom trends: {e}")
            return {}
