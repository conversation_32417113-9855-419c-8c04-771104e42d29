import React, { useState } from 'react';
import {
  HeartI<PERSON>,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ChartBarIcon,
  BeakerIcon,
  UserIcon,
  BellIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  CogIcon,
} from '@heroicons/react/24/outline';

interface HealthMetric {
  id: string;
  residentId: string;
  residentName: string;
  type: 'blood_pressure' | 'heart_rate' | 'temperature' | 'blood_sugar' | 'weight' | 'oxygen_saturation';
  value: string;
  unit: string;
  timestamp: string;
  status: 'normal' | 'warning' | 'critical';
  aiAnalysis: {
    trend: 'improving' | 'stable' | 'declining';
    riskScore: number;
    predictions: string[];
    recommendations: string[];
  };
}

interface HealthAlert {
  id: string;
  residentId: string;
  residentName: string;
  type: 'fall_risk' | 'medication_interaction' | 'vital_anomaly' | 'health_decline' | 'emergency';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  aiConfidence: number;
  timestamp: string;
  status: 'active' | 'acknowledged' | 'resolved';
  recommendedActions: string[];
}

const MedicalManagement: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'vitals' | 'alerts' | 'analytics'>('overview');
  const [showTrendsModal, setShowTrendsModal] = useState(false);
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [selectedMetricForTrends, setSelectedMetricForTrends] = useState<HealthMetric | null>(null);
  const [selectedMetricForConfig, setSelectedMetricForConfig] = useState<HealthMetric | null>(null);

  // AI Alerts state
  const [showAlertDetailsModal, setShowAlertDetailsModal] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState<HealthAlert | null>(null);
  const [alerts, setAlerts] = useState<HealthAlert[]>([]);

  // Mock data for health metrics
  const healthMetrics: HealthMetric[] = [
    {
      id: '1',
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      type: 'blood_pressure',
      value: '145/92',
      unit: 'mmHg',
      timestamp: '2024-01-20T09:30:00Z',
      status: 'warning',
      aiAnalysis: {
        trend: 'declining',
        riskScore: 75,
        predictions: ['Hypertensive crisis risk in 48-72 hours', 'Medication adjustment needed'],
        recommendations: ['Increase monitoring frequency', 'Contact physician', 'Review sodium intake']
      }
    },
    {
      id: '2',
      residentId: 'R002',
      residentName: 'William Anderson',
      type: 'blood_sugar',
      value: '180',
      unit: 'mg/dL',
      timestamp: '2024-01-20T08:15:00Z',
      status: 'warning',
      aiAnalysis: {
        trend: 'stable',
        riskScore: 65,
        predictions: ['Post-meal spike pattern detected', 'Insulin timing optimization needed'],
        recommendations: ['Adjust meal timing', 'Monitor carbohydrate intake', 'Consider insulin adjustment']
      }
    },
    {
      id: '3',
      residentId: 'R003',
      residentName: 'Dorothy Garcia',
      type: 'heart_rate',
      value: '95',
      unit: 'bpm',
      timestamp: '2024-01-20T10:00:00Z',
      status: 'normal',
      aiAnalysis: {
        trend: 'improving',
        riskScore: 25,
        predictions: ['Cardiovascular health stable', 'Exercise tolerance improving'],
        recommendations: ['Continue current activity level', 'Regular monitoring sufficient']
      }
    }
  ];

  // Initialize alerts state with mock data
  React.useEffect(() => {
    const initialAlerts: HealthAlert[] = [
    {
      id: '1',
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      type: 'fall_risk',
      severity: 'high',
      message: 'AI detected increased fall risk based on gait analysis and medication changes',
      aiConfidence: 0.89,
      timestamp: '2024-01-20T07:45:00Z',
      status: 'active',
      recommendedActions: [
        'Implement fall prevention protocol',
        'Increase supervision during mobility',
        'Review blood pressure medications',
        'Consider physical therapy evaluation'
      ]
    },
    {
      id: '2',
      residentId: 'R002',
      residentName: 'William Anderson',
      type: 'medication_interaction',
      severity: 'medium',
      message: 'Potential drug interaction detected between new antibiotic and diabetes medication',
      aiConfidence: 0.92,
      timestamp: '2024-01-20T06:30:00Z',
      status: 'acknowledged',
      recommendedActions: [
        'Consult with pharmacist',
        'Monitor blood glucose more frequently',
        'Consider alternative antibiotic',
        'Adjust insulin dosing if needed'
      ]
    },
    {
      id: '3',
      residentId: 'R004',
      residentName: 'Robert Davis',
      type: 'health_decline',
      severity: 'critical',
      message: 'AI analysis indicates rapid cognitive decline pattern over past 7 days',
      aiConfidence: 0.85,
      timestamp: '2024-01-20T05:15:00Z',
      status: 'active',
      recommendedActions: [
        'Immediate physician consultation',
        'Neurological assessment',
        'Review all medications',
        'Family notification',
        'Enhanced monitoring protocol'
      ]
    }
    ];
    setAlerts(initialAlerts);
  }, []);

  // Handler functions for View Trends and Configure buttons
  const handleViewTrends = (metric: HealthMetric) => {
    setSelectedMetricForTrends(metric);
    setShowTrendsModal(true);
  };

  const handleConfigure = (metric: HealthMetric) => {
    setSelectedMetricForConfig(metric);
    setShowConfigModal(true);
  };

  // AI Alerts handler functions
  const handleAcknowledgeAlert = (healthAlert: HealthAlert) => {
    setAlerts(prevAlerts =>
      prevAlerts.map(a =>
        a.id === healthAlert.id
          ? { ...a, status: 'acknowledged' as const }
          : a
      )
    );
    alert(`Alert "${healthAlert.message}" has been acknowledged.`);
  };

  const handleViewAlertDetails = (healthAlert: HealthAlert) => {
    setSelectedAlert(healthAlert);
    setShowAlertDetailsModal(true);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'critical':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-blue-100 text-blue-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />;
      case 'declining':
        return <ArrowTrendingDownIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ChartBarIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'vitals', name: 'Vital Signs', icon: HeartIcon },
    { id: 'alerts', name: 'AI Alerts', icon: BellIcon },
    { id: 'analytics', name: 'Analytics', icon: BeakerIcon },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">AI Medical Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          Predictive health analytics and intelligent monitoring for residential care
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            🤖 Predictive Analytics
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            📊 Real-time Monitoring
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            ⚡ Early Warning System
          </span>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {selectedTab === 'overview' && (
            <div className="space-y-6">
              {/* Health Summary Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">Active Residents</p>
                      <p className="text-2xl font-bold">18</p>
                    </div>
                    <UserIcon className="h-8 w-8 text-blue-200" />
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">Stable Vitals</p>
                      <p className="text-2xl font-bold">15</p>
                    </div>
                    <CheckCircleIcon className="h-8 w-8 text-green-200" />
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-yellow-100 text-sm">Needs Attention</p>
                      <p className="text-2xl font-bold">3</p>
                    </div>
                    <ExclamationTriangleIcon className="h-8 w-8 text-yellow-200" />
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-red-100 text-sm">Critical Alerts</p>
                      <p className="text-2xl font-bold">1</p>
                    </div>
                    <BellIcon className="h-8 w-8 text-red-200" />
                  </div>
                </div>
              </div>

              {/* Recent Critical Alerts */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Critical AI Alerts</h3>
                <div className="space-y-3">
                  {alerts.filter(alert => alert.severity === 'critical').map((alert) => (
                    <div key={alert.id} className="border border-red-200 rounded-lg p-4 bg-red-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                            <h4 className="font-medium text-red-900">{alert.residentName}</h4>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                              {alert.severity.toUpperCase()}
                            </span>
                          </div>
                          <p className="mt-2 text-sm text-red-800">{alert.message}</p>
                          <p className="mt-1 text-xs text-red-600">
                            AI Confidence: {Math.round(alert.aiConfidence * 100)}%
                          </p>
                        </div>
                        <button
                          onClick={() => handleViewAlertDetails(alert)}
                          className="ml-4 px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700"
                        >
                          View Details
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'vitals' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Recent Vital Signs with AI Analysis</h3>
              {healthMetrics.map((metric) => (
                <div key={metric.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(metric.status)}
                        <div>
                          <h4 className="font-medium text-gray-900">{metric.residentName}</h4>
                          <p className="text-sm text-gray-600">
                            {metric.type.replace('_', ' ').toUpperCase()}: {metric.value} {metric.unit}
                          </p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(metric.status)}`}>
                          {metric.status.toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-md">
                        <div className="flex items-start">
                          <BeakerIcon className="h-5 w-5 text-purple-600 mt-0.5" />
                          <div className="ml-3">
                            <h5 className="text-sm font-medium text-purple-800 flex items-center">
                              AI Analysis
                              {getTrendIcon(metric.aiAnalysis.trend)}
                              <span className="ml-2 text-xs">Risk Score: {metric.aiAnalysis.riskScore}%</span>
                            </h5>
                            <div className="mt-2 space-y-1">
                              <div>
                                <p className="text-xs font-medium text-purple-700">Predictions:</p>
                                <ul className="text-xs text-purple-600 ml-2">
                                  {metric.aiAnalysis.predictions.map((prediction, index) => (
                                    <li key={index}>• {prediction}</li>
                                  ))}
                                </ul>
                              </div>
                              <div>
                                <p className="text-xs font-medium text-purple-700">Recommendations:</p>
                                <ul className="text-xs text-purple-600 ml-2">
                                  {metric.aiAnalysis.recommendations.map((rec, index) => (
                                    <li key={index}>• {rec}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="ml-4 flex flex-col space-y-2">
                      <button
                        onClick={() => handleViewTrends(metric)}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                      >
                        <EyeIcon className="h-4 w-4 inline mr-1" />
                        View Trends
                      </button>
                      <button
                        onClick={() => handleConfigure(metric)}
                        className="px-3 py-1 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700"
                      >
                        <CogIcon className="h-4 w-4 inline mr-1" />
                        Configure
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'alerts' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">AI Health Alerts</h3>
              {alerts.map((alert) => (
                <div key={alert.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <BellIcon className="h-5 w-5 text-orange-500" />
                        <div>
                          <h4 className="font-medium text-gray-900">{alert.residentName}</h4>
                          <p className="text-sm text-gray-600">{alert.type.replace('_', ' ').toUpperCase()}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                          {alert.severity.toUpperCase()}
                        </span>
                      </div>
                      
                      <p className="mt-2 text-sm text-gray-800">{alert.message}</p>
                      <p className="mt-1 text-xs text-gray-500">
                        AI Confidence: {Math.round(alert.aiConfidence * 100)}% • {new Date(alert.timestamp).toLocaleString()}
                      </p>
                      
                      <div className="mt-3">
                        <p className="text-xs font-medium text-gray-700">Recommended Actions:</p>
                        <ul className="mt-1 text-xs text-gray-600 ml-2">
                          {alert.recommendedActions.map((action, index) => (
                            <li key={index}>• {action}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    
                    <div className="ml-4 flex flex-col space-y-2">
                      <button
                        onClick={() => handleAcknowledgeAlert(alert)}
                        disabled={alert.status === 'acknowledged'}
                        className={`px-3 py-1 text-white text-sm rounded-md ${
                          alert.status === 'acknowledged'
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700'
                        }`}
                      >
                        {alert.status === 'acknowledged' ? 'Acknowledged' : 'Acknowledge'}
                      </button>
                      <button
                        onClick={() => handleViewAlertDetails(alert)}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'analytics' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Predictive Health Analytics</h3>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Fall Risk Prediction Model</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>High Risk Residents</span>
                      <span className="font-medium text-red-600">3</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Medium Risk Residents</span>
                      <span className="font-medium text-yellow-600">5</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Low Risk Residents</span>
                      <span className="font-medium text-green-600">10</span>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-blue-50 rounded-md">
                    <p className="text-xs text-blue-800">
                      🤖 AI Model Accuracy: 94.2% • Last Updated: 2 hours ago
                    </p>
                  </div>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Health Decline Detection</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Rapid Decline Detected</span>
                      <span className="font-medium text-red-600">1</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Gradual Decline</span>
                      <span className="font-medium text-yellow-600">2</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Stable/Improving</span>
                      <span className="font-medium text-green-600">15</span>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-50 rounded-md">
                    <p className="text-xs text-green-800">
                      📈 Early Detection Rate: 89.7% • Prevention Success: 92.1%
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* View Trends Modal */}
      {showTrendsModal && selectedMetricForTrends && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowTrendsModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <ChartBarIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Health Trends Analysis
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedMetricForTrends.residentName} - {selectedMetricForTrends.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowTrendsModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Current Reading */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Current Reading</h4>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-2xl font-bold text-gray-900">
                          {selectedMetricForTrends.value} {selectedMetricForTrends.unit}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(selectedMetricForTrends.timestamp).toLocaleString()}
                        </div>
                      </div>
                      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        selectedMetricForTrends.status === 'normal' ? 'bg-green-100 text-green-800' :
                        selectedMetricForTrends.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {selectedMetricForTrends.status.charAt(0).toUpperCase() + selectedMetricForTrends.status.slice(1)}
                      </div>
                    </div>
                  </div>

                  {/* AI Trend Analysis */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-900 mb-3 flex items-center">
                      <ArrowTrendingUpIcon className="h-5 w-5 mr-2" />
                      AI Trend Analysis
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center mb-2">
                          <span className="text-sm font-medium text-blue-700">Trend Direction:</span>
                          <div className={`ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            selectedMetricForTrends.aiAnalysis.trend === 'improving' ? 'bg-green-100 text-green-800' :
                            selectedMetricForTrends.aiAnalysis.trend === 'stable' ? 'bg-blue-100 text-blue-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {selectedMetricForTrends.aiAnalysis.trend === 'improving' && <ArrowTrendingUpIcon className="h-3 w-3 mr-1" />}
                            {selectedMetricForTrends.aiAnalysis.trend === 'declining' && <ArrowTrendingDownIcon className="h-3 w-3 mr-1" />}
                            {selectedMetricForTrends.aiAnalysis.trend.charAt(0).toUpperCase() + selectedMetricForTrends.aiAnalysis.trend.slice(1)}
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-blue-700">Risk Score:</span>
                          <div className="ml-2 flex items-center">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${
                                  selectedMetricForTrends.aiAnalysis.riskScore < 30 ? 'bg-green-500' :
                                  selectedMetricForTrends.aiAnalysis.riskScore < 70 ? 'bg-yellow-500' :
                                  'bg-red-500'
                                }`}
                                style={{ width: `${selectedMetricForTrends.aiAnalysis.riskScore}%` }}
                              ></div>
                            </div>
                            <span className="ml-2 text-sm text-blue-600">{selectedMetricForTrends.aiAnalysis.riskScore}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Historical Chart Placeholder */}
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">7-Day Trend Chart</h4>
                    <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-500">Interactive trend chart would be displayed here</p>
                        <p className="text-sm text-gray-400">Showing historical data, patterns, and predictions</p>
                      </div>
                    </div>
                  </div>

                  {/* AI Predictions */}
                  <div className="bg-purple-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-purple-900 mb-3">AI Predictions</h4>
                    <ul className="space-y-2">
                      {selectedMetricForTrends.aiAnalysis.predictions.map((prediction, index) => (
                        <li key={index} className="flex items-start">
                          <div className="flex-shrink-0 h-5 w-5 text-purple-600 mt-0.5">
                            <BeakerIcon className="h-4 w-4" />
                          </div>
                          <span className="ml-2 text-sm text-purple-700">{prediction}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Recommendations */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-green-900 mb-3">Recommended Actions</h4>
                    <ul className="space-y-2">
                      {selectedMetricForTrends.aiAnalysis.recommendations.map((recommendation, index) => (
                        <li key={index} className="flex items-start">
                          <div className="flex-shrink-0 h-5 w-5 text-green-600 mt-0.5">
                            <CheckCircleIcon className="h-4 w-4" />
                          </div>
                          <span className="ml-2 text-sm text-green-700">{recommendation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => setShowTrendsModal(false)}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Configure Modal */}
      {showConfigModal && selectedMetricForConfig && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowConfigModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 sm:mx-0 sm:h-10 sm:w-10">
                      <CogIcon className="h-6 w-6 text-gray-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Configure Monitoring
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedMetricForConfig.residentName} - {selectedMetricForConfig.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowConfigModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Monitoring Frequency */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Monitoring Frequency
                    </label>
                    <select className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                      <option>Every 4 hours</option>
                      <option>Every 6 hours</option>
                      <option>Every 8 hours</option>
                      <option>Every 12 hours</option>
                      <option>Daily</option>
                      <option>As needed</option>
                    </select>
                  </div>

                  {/* Alert Thresholds */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Alert Thresholds
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs font-medium text-gray-600">Warning Level</label>
                        <input
                          type="text"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-yellow-500 focus:ring-yellow-500 sm:text-sm"
                          placeholder="Enter threshold"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-600">Critical Level</label>
                        <input
                          type="text"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm"
                          placeholder="Enter threshold"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Notification Settings */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Notification Settings
                    </label>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Email notifications</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">SMS alerts for critical values</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Push notifications</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">AI trend analysis alerts</span>
                      </label>
                    </div>
                  </div>

                  {/* AI Configuration */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-blue-900 mb-3">AI Analysis Settings</h4>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-blue-700">Enable predictive analysis</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          defaultChecked
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-blue-700">Pattern recognition</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-blue-700">Correlation analysis with other metrics</span>
                      </label>
                    </div>
                  </div>

                  {/* Notes */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Special Instructions
                    </label>
                    <textarea
                      rows={3}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      placeholder="Any special monitoring instructions or notes..."
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => {
                    alert('Monitoring configuration saved successfully!');
                    setShowConfigModal(false);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Save Configuration
                </button>
                <button
                  onClick={() => setShowConfigModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Alert Details Modal */}
      {showAlertDetailsModal && selectedAlert && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAlertDetailsModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className={`mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10 ${
                      selectedAlert.severity === 'critical' ? 'bg-red-100' :
                      selectedAlert.severity === 'high' ? 'bg-orange-100' :
                      selectedAlert.severity === 'medium' ? 'bg-yellow-100' :
                      'bg-blue-100'
                    }`}>
                      <ExclamationTriangleIcon className={`h-6 w-6 ${
                        selectedAlert.severity === 'critical' ? 'text-red-600' :
                        selectedAlert.severity === 'high' ? 'text-orange-600' :
                        selectedAlert.severity === 'medium' ? 'text-yellow-600' :
                        'text-blue-600'
                      }`} />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        AI Health Alert Details
                      </h3>
                      <p className="text-sm text-gray-500">
                        {selectedAlert.residentName} • {selectedAlert.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAlertDetailsModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Alert Summary */}
                  <div className={`rounded-lg p-4 ${
                    selectedAlert.severity === 'critical' ? 'bg-red-50 border border-red-200' :
                    selectedAlert.severity === 'high' ? 'bg-orange-50 border border-orange-200' :
                    selectedAlert.severity === 'medium' ? 'bg-yellow-50 border border-yellow-200' :
                    'bg-blue-50 border border-blue-200'
                  }`}>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className={`text-md font-medium ${
                        selectedAlert.severity === 'critical' ? 'text-red-900' :
                        selectedAlert.severity === 'high' ? 'text-orange-900' :
                        selectedAlert.severity === 'medium' ? 'text-yellow-900' :
                        'text-blue-900'
                      }`}>
                        Alert Message
                      </h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        selectedAlert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                        selectedAlert.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                        selectedAlert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {selectedAlert.severity.toUpperCase()} PRIORITY
                      </span>
                    </div>
                    <p className={`text-sm ${
                      selectedAlert.severity === 'critical' ? 'text-red-700' :
                      selectedAlert.severity === 'high' ? 'text-orange-700' :
                      selectedAlert.severity === 'medium' ? 'text-yellow-700' :
                      'text-blue-700'
                    }`}>
                      {selectedAlert.message}
                    </p>
                  </div>

                  {/* AI Analysis */}
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-purple-900 mb-3 flex items-center">
                      <BeakerIcon className="h-5 w-5 mr-2" />
                      AI Analysis
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center mb-2">
                          <span className="text-sm font-medium text-purple-700">Confidence Level:</span>
                          <div className="ml-2 flex items-center">
                            <div className="w-20 bg-purple-200 rounded-full h-2">
                              <div
                                className="h-2 rounded-full bg-purple-600"
                                style={{ width: `${selectedAlert.aiConfidence * 100}%` }}
                              ></div>
                            </div>
                            <span className="ml-2 text-sm text-purple-600">{Math.round(selectedAlert.aiConfidence * 100)}%</span>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-purple-700">Status:</span>
                          <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            selectedAlert.status === 'active' ? 'bg-red-100 text-red-800' :
                            selectedAlert.status === 'acknowledged' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {selectedAlert.status.charAt(0).toUpperCase() + selectedAlert.status.slice(1)}
                          </span>
                        </div>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-purple-700">Detected:</span>
                        <p className="text-sm text-purple-600">{new Date(selectedAlert.timestamp).toLocaleString()}</p>
                      </div>
                    </div>
                  </div>

                  {/* Recommended Actions */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-green-900 mb-3 flex items-center">
                      <CheckCircleIcon className="h-5 w-5 mr-2" />
                      Recommended Actions
                    </h4>
                    <ul className="space-y-2">
                      {selectedAlert.recommendedActions.map((action, index) => (
                        <li key={index} className="flex items-start">
                          <div className="flex-shrink-0 h-5 w-5 text-green-600 mt-0.5">
                            <CheckCircleIcon className="h-4 w-4" />
                          </div>
                          <span className="ml-2 text-sm text-green-700">{action}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Alert History */}
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                      <ClockIcon className="h-5 w-5 mr-2" />
                      Alert Timeline
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center text-sm">
                        <div className="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                        <span className="text-gray-600">Alert triggered by AI analysis</span>
                        <span className="ml-auto text-gray-500">{new Date(selectedAlert.timestamp).toLocaleString()}</span>
                      </div>
                      {selectedAlert.status === 'acknowledged' && (
                        <div className="flex items-center text-sm">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                          <span className="text-gray-600">Alert acknowledged by staff</span>
                          <span className="ml-auto text-gray-500">Recently</span>
                        </div>
                      )}
                      <div className="flex items-center text-sm text-gray-400">
                        <div className="w-2 h-2 bg-gray-300 rounded-full mr-3"></div>
                        <span>Awaiting resolution...</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                {selectedAlert.status !== 'acknowledged' && (
                  <button
                    onClick={() => {
                      handleAcknowledgeAlert(selectedAlert);
                      setShowAlertDetailsModal(false);
                    }}
                    className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm"
                  >
                    Acknowledge Alert
                  </button>
                )}
                <button
                  onClick={() => setShowAlertDetailsModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MedicalManagement;
