import React, { useState } from 'react';
import {
  HeartIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ChartBarIcon,
  BeakerIcon,
  UserIcon,
  BellIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  CogIcon,
} from '@heroicons/react/24/outline';

interface HealthMetric {
  id: string;
  residentId: string;
  residentName: string;
  type: 'blood_pressure' | 'heart_rate' | 'temperature' | 'blood_sugar' | 'weight' | 'oxygen_saturation';
  value: string;
  unit: string;
  timestamp: string;
  status: 'normal' | 'warning' | 'critical';
  aiAnalysis: {
    trend: 'improving' | 'stable' | 'declining';
    riskScore: number;
    predictions: string[];
    recommendations: string[];
  };
}

interface HealthAlert {
  id: string;
  residentId: string;
  residentName: string;
  type: 'fall_risk' | 'medication_interaction' | 'vital_anomaly' | 'health_decline' | 'emergency';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  aiConfidence: number;
  timestamp: string;
  status: 'active' | 'acknowledged' | 'resolved';
  recommendedActions: string[];
}

const MedicalManagement: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'vitals' | 'alerts' | 'analytics'>('overview');

  // Mock data for health metrics
  const healthMetrics: HealthMetric[] = [
    {
      id: '1',
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      type: 'blood_pressure',
      value: '145/92',
      unit: 'mmHg',
      timestamp: '2024-01-20T09:30:00Z',
      status: 'warning',
      aiAnalysis: {
        trend: 'declining',
        riskScore: 75,
        predictions: ['Hypertensive crisis risk in 48-72 hours', 'Medication adjustment needed'],
        recommendations: ['Increase monitoring frequency', 'Contact physician', 'Review sodium intake']
      }
    },
    {
      id: '2',
      residentId: 'R002',
      residentName: 'William Anderson',
      type: 'blood_sugar',
      value: '180',
      unit: 'mg/dL',
      timestamp: '2024-01-20T08:15:00Z',
      status: 'warning',
      aiAnalysis: {
        trend: 'stable',
        riskScore: 65,
        predictions: ['Post-meal spike pattern detected', 'Insulin timing optimization needed'],
        recommendations: ['Adjust meal timing', 'Monitor carbohydrate intake', 'Consider insulin adjustment']
      }
    },
    {
      id: '3',
      residentId: 'R003',
      residentName: 'Dorothy Garcia',
      type: 'heart_rate',
      value: '95',
      unit: 'bpm',
      timestamp: '2024-01-20T10:00:00Z',
      status: 'normal',
      aiAnalysis: {
        trend: 'improving',
        riskScore: 25,
        predictions: ['Cardiovascular health stable', 'Exercise tolerance improving'],
        recommendations: ['Continue current activity level', 'Regular monitoring sufficient']
      }
    }
  ];

  // Mock data for health alerts
  const healthAlerts: HealthAlert[] = [
    {
      id: '1',
      residentId: 'R001',
      residentName: 'Margaret Thompson',
      type: 'fall_risk',
      severity: 'high',
      message: 'AI detected increased fall risk based on gait analysis and medication changes',
      aiConfidence: 0.89,
      timestamp: '2024-01-20T07:45:00Z',
      status: 'active',
      recommendedActions: [
        'Implement fall prevention protocol',
        'Increase supervision during mobility',
        'Review blood pressure medications',
        'Consider physical therapy evaluation'
      ]
    },
    {
      id: '2',
      residentId: 'R002',
      residentName: 'William Anderson',
      type: 'medication_interaction',
      severity: 'medium',
      message: 'Potential drug interaction detected between new antibiotic and diabetes medication',
      aiConfidence: 0.92,
      timestamp: '2024-01-20T06:30:00Z',
      status: 'acknowledged',
      recommendedActions: [
        'Consult with pharmacist',
        'Monitor blood glucose more frequently',
        'Consider alternative antibiotic',
        'Adjust insulin dosing if needed'
      ]
    },
    {
      id: '3',
      residentId: 'R004',
      residentName: 'Robert Davis',
      type: 'health_decline',
      severity: 'critical',
      message: 'AI analysis indicates rapid cognitive decline pattern over past 7 days',
      aiConfidence: 0.85,
      timestamp: '2024-01-20T05:15:00Z',
      status: 'active',
      recommendedActions: [
        'Immediate physician consultation',
        'Neurological assessment',
        'Review all medications',
        'Family notification',
        'Enhanced monitoring protocol'
      ]
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'normal':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'critical':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-blue-100 text-blue-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />;
      case 'declining':
        return <ArrowTrendingDownIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ChartBarIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: ChartBarIcon },
    { id: 'vitals', name: 'Vital Signs', icon: HeartIcon },
    { id: 'alerts', name: 'AI Alerts', icon: BellIcon },
    { id: 'analytics', name: 'Analytics', icon: BeakerIcon },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">AI Medical Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          Predictive health analytics and intelligent monitoring for residential care
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            🤖 Predictive Analytics
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            📊 Real-time Monitoring
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            ⚡ Early Warning System
          </span>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {selectedTab === 'overview' && (
            <div className="space-y-6">
              {/* Health Summary Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">Active Residents</p>
                      <p className="text-2xl font-bold">18</p>
                    </div>
                    <UserIcon className="h-8 w-8 text-blue-200" />
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">Stable Vitals</p>
                      <p className="text-2xl font-bold">15</p>
                    </div>
                    <CheckCircleIcon className="h-8 w-8 text-green-200" />
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-yellow-100 text-sm">Needs Attention</p>
                      <p className="text-2xl font-bold">3</p>
                    </div>
                    <ExclamationTriangleIcon className="h-8 w-8 text-yellow-200" />
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-4 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-red-100 text-sm">Critical Alerts</p>
                      <p className="text-2xl font-bold">1</p>
                    </div>
                    <BellIcon className="h-8 w-8 text-red-200" />
                  </div>
                </div>
              </div>

              {/* Recent Critical Alerts */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Critical AI Alerts</h3>
                <div className="space-y-3">
                  {healthAlerts.filter(alert => alert.severity === 'critical').map((alert) => (
                    <div key={alert.id} className="border border-red-200 rounded-lg p-4 bg-red-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                            <h4 className="font-medium text-red-900">{alert.residentName}</h4>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                              {alert.severity.toUpperCase()}
                            </span>
                          </div>
                          <p className="mt-2 text-sm text-red-800">{alert.message}</p>
                          <p className="mt-1 text-xs text-red-600">
                            AI Confidence: {Math.round(alert.aiConfidence * 100)}%
                          </p>
                        </div>
                        <button className="ml-4 px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700">
                          View Details
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {selectedTab === 'vitals' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Recent Vital Signs with AI Analysis</h3>
              {healthMetrics.map((metric) => (
                <div key={metric.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(metric.status)}
                        <div>
                          <h4 className="font-medium text-gray-900">{metric.residentName}</h4>
                          <p className="text-sm text-gray-600">
                            {metric.type.replace('_', ' ').toUpperCase()}: {metric.value} {metric.unit}
                          </p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(metric.status)}`}>
                          {metric.status.toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-md">
                        <div className="flex items-start">
                          <BeakerIcon className="h-5 w-5 text-purple-600 mt-0.5" />
                          <div className="ml-3">
                            <h5 className="text-sm font-medium text-purple-800 flex items-center">
                              AI Analysis
                              {getTrendIcon(metric.aiAnalysis.trend)}
                              <span className="ml-2 text-xs">Risk Score: {metric.aiAnalysis.riskScore}%</span>
                            </h5>
                            <div className="mt-2 space-y-1">
                              <div>
                                <p className="text-xs font-medium text-purple-700">Predictions:</p>
                                <ul className="text-xs text-purple-600 ml-2">
                                  {metric.aiAnalysis.predictions.map((prediction, index) => (
                                    <li key={index}>• {prediction}</li>
                                  ))}
                                </ul>
                              </div>
                              <div>
                                <p className="text-xs font-medium text-purple-700">Recommendations:</p>
                                <ul className="text-xs text-purple-600 ml-2">
                                  {metric.aiAnalysis.recommendations.map((rec, index) => (
                                    <li key={index}>• {rec}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="ml-4 flex flex-col space-y-2">
                      <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                        <EyeIcon className="h-4 w-4 inline mr-1" />
                        View Trends
                      </button>
                      <button className="px-3 py-1 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700">
                        <CogIcon className="h-4 w-4 inline mr-1" />
                        Configure
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'alerts' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">AI Health Alerts</h3>
              {healthAlerts.map((alert) => (
                <div key={alert.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <BellIcon className="h-5 w-5 text-orange-500" />
                        <div>
                          <h4 className="font-medium text-gray-900">{alert.residentName}</h4>
                          <p className="text-sm text-gray-600">{alert.type.replace('_', ' ').toUpperCase()}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                          {alert.severity.toUpperCase()}
                        </span>
                      </div>
                      
                      <p className="mt-2 text-sm text-gray-800">{alert.message}</p>
                      <p className="mt-1 text-xs text-gray-500">
                        AI Confidence: {Math.round(alert.aiConfidence * 100)}% • {new Date(alert.timestamp).toLocaleString()}
                      </p>
                      
                      <div className="mt-3">
                        <p className="text-xs font-medium text-gray-700">Recommended Actions:</p>
                        <ul className="mt-1 text-xs text-gray-600 ml-2">
                          {alert.recommendedActions.map((action, index) => (
                            <li key={index}>• {action}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    
                    <div className="ml-4 flex flex-col space-y-2">
                      <button className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700">
                        Acknowledge
                      </button>
                      <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                        View Details
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedTab === 'analytics' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Predictive Health Analytics</h3>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Fall Risk Prediction Model</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>High Risk Residents</span>
                      <span className="font-medium text-red-600">3</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Medium Risk Residents</span>
                      <span className="font-medium text-yellow-600">5</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Low Risk Residents</span>
                      <span className="font-medium text-green-600">10</span>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-blue-50 rounded-md">
                    <p className="text-xs text-blue-800">
                      🤖 AI Model Accuracy: 94.2% • Last Updated: 2 hours ago
                    </p>
                  </div>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Health Decline Detection</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Rapid Decline Detected</span>
                      <span className="font-medium text-red-600">1</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Gradual Decline</span>
                      <span className="font-medium text-yellow-600">2</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Stable/Improving</span>
                      <span className="font-medium text-green-600">15</span>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-50 rounded-md">
                    <p className="text-xs text-green-800">
                      📈 Early Detection Rate: 89.7% • Prevention Success: 92.1%
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MedicalManagement;
