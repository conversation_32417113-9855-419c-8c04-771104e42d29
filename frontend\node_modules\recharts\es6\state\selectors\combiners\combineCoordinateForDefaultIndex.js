export var combineCoordinateForDefaultIndex = (width, height, layout, offset, tooltipTicks, defaultIndex, tooltipConfigurations, tooltipPayloadSearcher) => {
  if (defaultIndex == null || tooltipPayloadSearcher == null) {
    return undefined;
  }
  // With defaultIndex alone, we don't have enough information to decide _which_ of the multiple tooltips to display. So we choose the first one.
  var firstConfiguration = tooltipConfigurations[0];
  // @ts-expect-error we need to rethink the tooltipPayloadSearcher type
  var maybePosition = firstConfiguration == null ? undefined : tooltipPayloadSearcher(firstConfiguration.positions, defaultIndex);
  if (maybePosition != null) {
    return maybePosition;
  }
  var tick = tooltipTicks === null || tooltipTicks === void 0 ? void 0 : tooltipTicks[Number(defaultIndex)];
  if (!tick) {
    return undefined;
  }
  switch (layout) {
    case 'horizontal':
      {
        return {
          x: tick.coordinate,
          y: (offset.top + height) / 2
        };
      }
    default:
      {
        // This logic is not super sound - it conflates vertical, radial, centric layouts into just one. TODO improve!
        return {
          x: (offset.left + width) / 2,
          y: tick.coordinate
        };
      }
  }
};