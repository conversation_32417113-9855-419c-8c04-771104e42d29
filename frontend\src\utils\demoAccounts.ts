// Demo Accounts for Care-SolAI Platform
// These accounts are used for demonstration and testing purposes

import { UserRole } from './roleBasedAccess';

export interface DemoUser {
  id: string;
  email: string;
  password: string;
  role: UserRole;
  first_name: string;
  last_name: string;
  caregiver_id: string;
  is_active: boolean;
  department?: string;
  title?: string;
  description: string;
  avatar?: string;
}

export const DEMO_ACCOUNTS: DemoUser[] = [
  {
    id: 'demo-admin-001',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    first_name: '<PERSON>',
    last_name: 'Administrator',
    caregiver_id: 'admin-001',
    is_active: true,
    department: 'Administration',
    title: 'System Administrator',
    description: 'Full system access - Can manage all aspects of the Care-SolAI platform including users, settings, billing, and compliance.',
    avatar: '👩‍💼'
  },
  {
    id: 'demo-supervisor-001',
    email: '<EMAIL>',
    password: 'nurse123',
    role: 'supervisor',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    caregiver_id: 'supervisor-001',
    is_active: true,
    department: 'Clinical Operations',
    title: 'Care Supervisor',
    description: 'Clinical operations access - Can oversee patient care, manage staff, handle inventory and purchasing. Cannot access billing, reports, or system settings.',
    avatar: '👨‍⚕️'
  },
  {
    id: 'demo-nurse-001',
    email: '<EMAIL>',
    password: 'nurse123',
    role: 'nurse',
    first_name: 'Emily',
    last_name: 'Rodriguez',
    caregiver_id: 'nurse-001',
    is_active: true,
    department: 'Nursing',
    title: 'Registered Nurse',
    description: 'Full clinical access - Can manage patient care, medications, scheduling, purchasing, and clinical documentation. Focused on direct patient care and clinical operations.',
    avatar: '👩‍⚕️'
  },
  {
    id: 'demo-caregiver-001',
    email: '<EMAIL>',
    password: 'care123',
    role: 'caregiver',
    first_name: 'Emily',
    last_name: 'Rodriguez',
    caregiver_id: 'caregiver-001',
    is_active: true,
    department: 'Patient Care',
    title: 'Registered Nurse',
    description: 'Patient-focused access - Can manage assigned patients, administer medications via AIMAR, document care, and request supplies. Cannot access billing or administrative functions.',
    avatar: '👩‍⚕️'
  },
  {
    id: 'demo-billing-001',
    email: '<EMAIL>',
    password: 'billing123',
    role: 'billing',
    first_name: 'David',
    last_name: 'Chen',
    caregiver_id: 'billing-001',
    is_active: true,
    department: 'Financial Services',
    title: 'Billing Specialist',
    description: 'Financial access - Can manage billing, process payments, generate financial reports, and handle insurance claims. Cannot access clinical records or patient care functions.',
    avatar: '👨‍💼'
  }
];

// Demo login credentials for easy reference
export const DEMO_CREDENTIALS = {
  admin: {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'System Administrator',
    access: 'Full system access including billing, reports, and settings'
  },
  supervisor: {
    email: '<EMAIL>',
    password: 'nurse123',
    role: 'Care Supervisor',
    access: 'Clinical operations access (no billing, reports, or system settings)'
  },
  caregiver: {
    email: '<EMAIL>',
    password: 'care123',
    role: 'Care Provider',
    access: 'Patient care access (no administrative functions)'
  },
  billing: {
    email: '<EMAIL>',
    password: 'billing123',
    role: 'Billing Specialist',
    access: 'Financial management and billing access'
  }
};

// Function to get demo user by email
export const getDemoUserByEmail = (email: string): DemoUser | null => {
  return DEMO_ACCOUNTS.find(user => user.email.toLowerCase() === email.toLowerCase()) || null;
};

// Function to validate demo credentials
export const validateDemoCredentials = (email: string, password: string): DemoUser | null => {
  const user = getDemoUserByEmail(email);
  if (user && user.password === password && user.is_active) {
    return user;
  }
  return null;
};

// Function to check if email is a demo account
export const isDemoAccount = (email: string): boolean => {
  return email.toLowerCase().includes('@care-solai.com');
};

// Function to get all demo accounts (for admin purposes)
export const getAllDemoAccounts = (): DemoUser[] => {
  return DEMO_ACCOUNTS.filter(user => user.is_active);
};

// Function to get demo accounts by role
export const getDemoAccountsByRole = (role: UserRole): DemoUser[] => {
  return DEMO_ACCOUNTS.filter(user => user.role === role && user.is_active);
};

// Demo patient assignments for caregivers
export const DEMO_PATIENT_ASSIGNMENTS = {
  'caregiver-001': ['patient-101', 'patient-102', 'patient-105', 'patient-108'],
  'supervisor-001': [], // Supervisors can see all patients
};

// Demo facility information
export const DEMO_FACILITY = {
  name: 'Sunset Manor Care Facility',
  address: '123 Healthcare Drive, Wellness City, HC 12345',
  phone: '(*************',
  email: '<EMAIL>',
  license: 'HC-2025-001',
  capacity: 24,
  current_residents: 18,
  established: '2020',
  type: 'Residential Care Home',
  specialties: ['Memory Care', 'Assisted Living', 'Medication Management', 'Physical Therapy']
};

// Demo statistics for dashboard
export const DEMO_STATS = {
  total_residents: 18,
  active_caregivers: 12,
  pending_tasks: 8,
  completed_today: 24,
  medication_administrations_today: 67,
  inventory_alerts: 3,
  monthly_revenue: 125000,
  compliance_score: 97.8,
  patient_satisfaction: 4.8,
  staff_efficiency: 94.2
};

// Function to simulate database delay
export const simulateNetworkDelay = (ms: number = 1000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Function to generate demo session token
export const generateDemoToken = (user: DemoUser): string => {
  const payload = {
    user_id: user.id,
    email: user.email,
    role: user.role,
    exp: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
    iss: 'care-solai-com'
  };
  
  // In a real app, this would be a proper JWT
  return btoa(JSON.stringify(payload));
};

// Function to validate demo token
export const validateDemoToken = (token: string): DemoUser | null => {
  try {
    const payload = JSON.parse(atob(token));
    
    if (payload.exp < Date.now()) {
      return null; // Token expired
    }
    
    if (payload.iss !== 'care-solai-com') {
      return null; // Invalid issuer
    }
    
    return getDemoUserByEmail(payload.email);
  } catch (error) {
    return null; // Invalid token
  }
};
