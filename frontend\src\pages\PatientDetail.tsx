import React from 'react';
import { useParams } from 'react-router-dom';

const PatientDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="space-y-6">
      <div className="card">
        <div className="card-body">
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Patient Detail View
            </h3>
            <p className="text-gray-600 mb-4">
              Patient ID: {id}
            </p>
            <p className="text-sm text-gray-500">
              This page will show detailed patient information, medical records,
              care plans, and AI predictions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientDetail;
