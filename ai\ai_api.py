"""
AI Service API for CareSyncAI
Standalone FastAPI service for AI/ML operations
"""

from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import logging
import uvicorn

# Import AI modules
from fall_risk_predictor import FallRiskPredictor
from nlp_analyzer import CaregiverNotesAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="CareSyncAI ML Service",
    description="AI/ML microservice for healthcare predictions",
    version="1.0.0"
)

# Initialize AI models
fall_risk_predictor = FallRiskPredictor()
nlp_analyzer = CaregiverNotesAnalyzer()

# Request/Response models
class FallRiskRequest(BaseModel):
    patient_data: Dict[str, Any]

class NoteAnalysisRequest(BaseModel):
    note_text: str
    patient_context: Optional[Dict[str, Any]] = None

class PredictionResponse(BaseModel):
    prediction: Dict[str, Any]
    status: str = "success"

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "CareSyncAI ML Service",
        "version": "1.0.0"
    }

@app.post("/predict/fall-risk", response_model=PredictionResponse)
async def predict_fall_risk(request: FallRiskRequest):
    """Predict fall risk for a patient"""
    try:
        prediction = fall_risk_predictor.predict_fall_risk(request.patient_data)
        return PredictionResponse(prediction=prediction)
    except Exception as e:
        logger.error(f"Error predicting fall risk: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/analyze/note", response_model=PredictionResponse)
async def analyze_note(request: NoteAnalysisRequest):
    """Analyze caregiver note using NLP"""
    try:
        analysis = nlp_analyzer.analyze_note(
            request.note_text, 
            request.patient_context
        )
        return PredictionResponse(prediction=analysis)
    except Exception as e:
        logger.error(f"Error analyzing note: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/analyze/notes-batch")
async def analyze_notes_batch(notes: List[Dict[str, Any]]):
    """Analyze multiple notes for trends"""
    try:
        analysis = nlp_analyzer.analyze_multiple_notes(notes)
        return PredictionResponse(prediction=analysis)
    except Exception as e:
        logger.error(f"Error analyzing notes batch: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models/status")
async def get_models_status():
    """Get status of loaded AI models"""
    return {
        "fall_risk_predictor": {
            "loaded": fall_risk_predictor.model is not None,
            "version": fall_risk_predictor.model_version
        },
        "nlp_analyzer": {
            "loaded": nlp_analyzer.sentiment_analyzer is not None,
            "models": {
                "sentiment": nlp_analyzer.sentiment_analyzer is not None,
                "ner": nlp_analyzer.medical_ner is not None
            }
        }
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
