import React, { useState, useRef } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  UserIcon,
  CalendarIcon,
  PhoneIcon,
  MapPinIcon,
  HeartIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon,
  PencilIcon,
  EyeIcon,
  PhotoIcon,
  PrinterIcon,
  TrashIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline';

interface Resident {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  age: number;
  gender: 'male' | 'female' | 'other';
  roomNumber: string;
  admissionDate: string;
  status: 'active' | 'inactive' | 'discharged';
  careLevel: 'independent' | 'assisted' | 'memory_care' | 'skilled_nursing';
  primaryCaregiver: string;
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
  };
  medicalInfo: {
    allergies: {
      drug: string[];
      food: string[];
      environmental: string[];
    };
    medications: string[];
    conditions: string[];
    diagnosis: string[];
    dietType: string;
    dietaryRestrictions: string[];
    height: {
      feet: number;
      inches: number;
    };
    weight: number; // in pounds
  };
  primaryCareProvider: {
    name: string;
    phone: string;
    email?: string;
    address?: string;
    specialty?: string;
  };
  powerOfAttorney: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
    address?: string;
    type: 'healthcare' | 'financial' | 'general';
  };
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  insurance: {
    provider: string;
    policyNumber: string;
    groupNumber?: string;
  };
  notes?: string;
  imageUrl?: string; // Resident image as data URL
}

const Patients: React.FC = () => {
  const [residents, setResidents] = useState<Resident[]>([
    {
      id: 'R001',
      firstName: 'John',
      lastName: 'Smith',
      dateOfBirth: '1945-03-15',
      age: 79,
      gender: 'male',
      roomNumber: '101A',
      admissionDate: '2024-01-15',
      status: 'active',
      careLevel: 'assisted',
      primaryCaregiver: 'Emily Rodriguez',
      emergencyContact: {
        name: 'Sarah Smith',
        relationship: 'Daughter',
        phone: '******-0123',
        email: '<EMAIL>'
      },
      medicalInfo: {
        allergies: {
          drug: ['Penicillin', 'Sulfa'],
          food: ['Shellfish', 'Nuts'],
          environmental: ['Pollen']
        },
        medications: ['Lisinopril 10mg', 'Metformin 500mg'],
        conditions: ['Hypertension', 'Type 2 Diabetes'],
        diagnosis: ['Essential Hypertension', 'Type 2 Diabetes Mellitus'],
        dietType: 'Diabetic Diet',
        dietaryRestrictions: ['Low Sodium', 'Sugar-Free'],
        height: { feet: 5, inches: 8 },
        weight: 165
      },
      primaryCareProvider: {
        name: 'Dr. Sarah Williams',
        phone: '******-0199',
        email: '<EMAIL>',
        address: '456 Medical Plaza, Springfield, IL 62701',
        specialty: 'Internal Medicine'
      },
      powerOfAttorney: {
        name: 'Sarah Smith',
        relationship: 'Daughter',
        phone: '******-0123',
        email: '<EMAIL>',
        address: '789 Family Lane, Springfield, IL 62702',
        type: 'healthcare'
      },
      address: {
        street: '123 Oak Street',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62701'
      },
      insurance: {
        provider: 'Medicare',
        policyNumber: 'MED123456789'
      },
      notes: 'Enjoys reading and gardening. Prefers morning activities.'
    },
    {
      id: 'R002',
      firstName: 'Mary',
      lastName: 'Johnson',
      dateOfBirth: '1938-07-22',
      age: 86,
      gender: 'female',
      roomNumber: '102B',
      admissionDate: '2023-11-08',
      status: 'active',
      careLevel: 'memory_care',
      primaryCaregiver: 'David Wilson',
      emergencyContact: {
        name: 'Michael Johnson',
        relationship: 'Son',
        phone: '******-0456',
        email: '<EMAIL>'
      },
      medicalInfo: {
        allergies: {
          drug: ['Latex'],
          food: [],
          environmental: ['Dust mites']
        },
        medications: ['Donepezil 10mg', 'Vitamin D3'],
        conditions: ['Alzheimer\'s Disease', 'Osteoporosis'],
        diagnosis: ['Alzheimer\'s Disease - Moderate Stage', 'Osteoporosis'],
        dietType: 'Soft Mechanical Diet',
        dietaryRestrictions: ['Soft Foods', 'High Calcium'],
        height: { feet: 5, inches: 4 },
        weight: 142
      },
      primaryCareProvider: {
        name: 'Dr. Michael Chen',
        phone: '******-0288',
        email: '<EMAIL>',
        address: '789 Brain Health Center, Springfield, IL 62703',
        specialty: 'Neurology'
      },
      powerOfAttorney: {
        name: 'Michael Johnson',
        relationship: 'Son',
        phone: '******-0456',
        email: '<EMAIL>',
        address: '321 Oak Street, Springfield, IL 62704',
        type: 'general'
      },
      address: {
        street: '456 Maple Avenue',
        city: 'Springfield',
        state: 'IL',
        zipCode: '62702'
      },
      insurance: {
        provider: 'Blue Cross Blue Shield',
        policyNumber: 'BC987654321',
        groupNumber: 'GRP001'
      },
      notes: 'Responds well to music therapy. Family visits on weekends.'
    }
  ]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedResident, setSelectedResident] = useState<Resident | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [caregiverFilter, setCaregiverFilter] = useState('all');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [newResident, setNewResident] = useState<Partial<Resident>>({
  imageUrl: '',
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: 'male',
    roomNumber: '',
    careLevel: 'independent',
    primaryCaregiver: '',
    emergencyContact: {
      name: '',
      relationship: '',
      phone: '',
      email: ''
    },
    medicalInfo: {
      allergies: {
        drug: [],
        food: [],
        environmental: []
      },
      medications: [],
      conditions: [],
      diagnosis: [],
      dietType: '',
      dietaryRestrictions: [],
      height: { feet: 0, inches: 0 },
      weight: 0
    },
    primaryCareProvider: {
      name: '',
      phone: '',
      email: '',
      address: '',
      specialty: ''
    },
    powerOfAttorney: {
      name: '',
      relationship: '',
      phone: '',
      email: '',
      address: '',
      type: 'healthcare'
    },
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: ''
    },
    insurance: {
      provider: '',
      policyNumber: '',
      groupNumber: ''
    },
    notes: ''
  });

  // Handler functions
  const handleAddResident = () => {
    setNewResident({
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      gender: 'male',
      roomNumber: '',
      careLevel: 'independent',
      primaryCaregiver: '',
      emergencyContact: {
        name: '',
        relationship: '',
        phone: '',
        email: ''
      },
      medicalInfo: {
        allergies: {
          drug: [],
          food: [],
          environmental: []
        },
        medications: [],
        conditions: [],
        diagnosis: [],
        dietType: '',
        dietaryRestrictions: [],
        height: { feet: 0, inches: 0 },
        weight: 0
      },
      primaryCareProvider: {
        name: '',
        phone: '',
        email: '',
        address: '',
        specialty: ''
      },
      powerOfAttorney: {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address: '',
        type: 'healthcare'
      },
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: ''
      },
      insurance: {
        provider: '',
        policyNumber: '',
        groupNumber: ''
      },
      notes: ''
    });
    setShowAddModal(true);
  };

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const saveResident = () => {
    if (!newResident.firstName || !newResident.lastName || !newResident.dateOfBirth) {
      alert('Please fill in all required fields (First Name, Last Name, Date of Birth)');
      return;
    }

    const resident: Resident = {
  imageUrl: newResident.imageUrl || '',
      id: `R${String(residents.length + 1).padStart(3, '0')}`,
      firstName: newResident.firstName!,
      lastName: newResident.lastName!,
      dateOfBirth: newResident.dateOfBirth!,
      age: calculateAge(newResident.dateOfBirth!),
      gender: newResident.gender || 'male',
      roomNumber: newResident.roomNumber || 'TBD',
      admissionDate: new Date().toISOString().split('T')[0],
      status: 'active',
      careLevel: newResident.careLevel || 'independent',
      primaryCaregiver: newResident.primaryCaregiver || 'To be assigned',
      emergencyContact: newResident.emergencyContact || {
        name: '',
        relationship: '',
        phone: ''
      },
      medicalInfo: newResident.medicalInfo || {
        allergies: {
          drug: [],
          food: [],
          environmental: []
        },
        medications: [],
        conditions: [],
        diagnosis: [],
        dietType: '',
        dietaryRestrictions: [],
        height: { feet: 0, inches: 0 },
        weight: 0
      },
      primaryCareProvider: newResident.primaryCareProvider || {
        name: '',
        phone: '',
        email: '',
        address: '',
        specialty: ''
      },
      powerOfAttorney: newResident.powerOfAttorney || {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address: '',
        type: 'healthcare'
      },
      address: newResident.address || {
        street: '',
        city: '',
        state: '',
        zipCode: ''
      },
      insurance: newResident.insurance || {
        provider: '',
        policyNumber: ''
      },
      notes: newResident.notes
    };

    setResidents(prev => [resident, ...prev]);
    setShowAddModal(false);
    alert('Resident added successfully!');
  };

  // Filter residents based on search and filters
  const filteredResidents = residents.filter(resident => {
    const matchesSearch = searchTerm === '' ||
      `${resident.firstName} ${resident.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resident.roomNumber.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || resident.status === statusFilter;
    const matchesCaregiver = caregiverFilter === 'all' || resident.primaryCaregiver === caregiverFilter;

    return matchesSearch && matchesStatus && matchesCaregiver;
  });

  const getCaregivers = () => {
    const caregiverSet = new Set(residents.map(r => r.primaryCaregiver));
    const caregivers = Array.from(caregiverSet);
    return caregivers.filter(c => c && c !== 'To be assigned');
  };

  // Image handling functions
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file (JPG, PNG, GIF)');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Image size must be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        setNewResident(prev => ({
          ...prev,
          imageUrl: imageUrl
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setNewResident(prev => ({
      ...prev,
      imageUrl: ''
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // View resident details
  const handleViewResident = (resident: Resident) => {
    setSelectedResident(resident);
    setShowViewModal(true);
  };

  // Print functionality
  const handlePrintResident = (resident: Resident) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const printContent = generatePrintContent(resident);
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    }
  };

  const handlePrintAllResidents = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const printContent = generateAllResidentsPrintContent(filteredResidents);
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    }
  };

  const generatePrintContent = (resident: Resident) => {
    const heightDisplay = `${resident.medicalInfo.height.feet}'${resident.medicalInfo.height.inches}"`;
    const allergiesDisplay = [
      ...resident.medicalInfo.allergies.drug,
      ...resident.medicalInfo.allergies.food,
      ...resident.medicalInfo.allergies.environmental
    ].join(', ') || 'None';

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Resident Profile - ${resident.firstName} ${resident.lastName}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #7c3aed; margin-bottom: 10px; }
            .resident-photo { width: 150px; height: 150px; border-radius: 50%; object-fit: cover; margin: 0 auto 20px; display: block; border: 3px solid #e5e7eb; }
            .section { margin-bottom: 25px; }
            .section-title { font-size: 18px; font-weight: bold; color: #374151; border-bottom: 1px solid #d1d5db; padding-bottom: 5px; margin-bottom: 15px; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
            .info-item { margin-bottom: 10px; }
            .label { font-weight: bold; color: #6b7280; }
            .value { color: #111827; }
            .allergies { color: #dc2626; font-weight: bold; }
            .footer { margin-top: 40px; text-align: center; font-size: 12px; color: #6b7280; border-top: 1px solid #e5e7eb; padding-top: 20px; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="logo">Care-SolAI Residential Healthcare</div>
            <h1>Resident Profile</h1>
            ${resident.imageUrl ? `<img src="${resident.imageUrl}" alt="Resident Photo" class="resident-photo" />` : ''}
          </div>

          <div class="section">
            <div class="section-title">Personal Information</div>
            <div class="info-grid">
              <div class="info-item"><span class="label">Name:</span> <span class="value">${resident.firstName} ${resident.lastName}</span></div>
              <div class="info-item"><span class="label">Date of Birth:</span> <span class="value">${resident.dateOfBirth}</span></div>
              <div class="info-item"><span class="label">Age:</span> <span class="value">${resident.age} years</span></div>
              <div class="info-item"><span class="label">Gender:</span> <span class="value">${resident.gender}</span></div>
              <div class="info-item"><span class="label">Room Number:</span> <span class="value">${resident.roomNumber}</span></div>
              <div class="info-item"><span class="label">Admission Date:</span> <span class="value">${resident.admissionDate}</span></div>
              <div class="info-item"><span class="label">Status:</span> <span class="value">${resident.status}</span></div>
              <div class="info-item"><span class="label">Care Level:</span> <span class="value">${resident.careLevel}</span></div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Medical Information</div>
            <div class="info-item"><span class="label">Height:</span> <span class="value">${heightDisplay}</span></div>
            <div class="info-item"><span class="label">Weight:</span> <span class="value">${resident.medicalInfo.weight} lbs</span></div>
            <div class="info-item"><span class="label">Diet Type:</span> <span class="value">${resident.medicalInfo.dietType}</span></div>
            <div class="info-item"><span class="label">Allergies:</span> <span class="value allergies">${allergiesDisplay}</span></div>
            <div class="info-item"><span class="label">Diagnosis:</span> <span class="value">${resident.medicalInfo.diagnosis.join(', ')}</span></div>
            <div class="info-item"><span class="label">Medications:</span> <span class="value">${resident.medicalInfo.medications.join(', ')}</span></div>
          </div>

          <div class="section">
            <div class="section-title">Emergency Contact</div>
            <div class="info-grid">
              <div class="info-item"><span class="label">Name:</span> <span class="value">${resident.emergencyContact.name}</span></div>
              <div class="info-item"><span class="label">Relationship:</span> <span class="value">${resident.emergencyContact.relationship}</span></div>
              <div class="info-item"><span class="label">Phone:</span> <span class="value">${resident.emergencyContact.phone}</span></div>
              <div class="info-item"><span class="label">Email:</span> <span class="value">${resident.emergencyContact.email || 'N/A'}</span></div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Primary Care Provider</div>
            <div class="info-grid">
              <div class="info-item"><span class="label">Name:</span> <span class="value">${resident.primaryCareProvider.name}</span></div>
              <div class="info-item"><span class="label">Specialty:</span> <span class="value">${resident.primaryCareProvider.specialty}</span></div>
              <div class="info-item"><span class="label">Phone:</span> <span class="value">${resident.primaryCareProvider.phone}</span></div>
              <div class="info-item"><span class="label">Email:</span> <span class="value">${resident.primaryCareProvider.email || 'N/A'}</span></div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Power of Attorney</div>
            <div class="info-grid">
              <div class="info-item"><span class="label">Name:</span> <span class="value">${resident.powerOfAttorney.name}</span></div>
              <div class="info-item"><span class="label">Relationship:</span> <span class="value">${resident.powerOfAttorney.relationship}</span></div>
              <div class="info-item"><span class="label">Type:</span> <span class="value">${resident.powerOfAttorney.type}</span></div>
              <div class="info-item"><span class="label">Phone:</span> <span class="value">${resident.powerOfAttorney.phone}</span></div>
            </div>
          </div>

          <div class="footer">
            <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
            <p>Care-SolAI Residential Healthcare Management System</p>
          </div>
        </body>
      </html>
    `;
  };

  const generateAllResidentsPrintContent = (residents: Resident[]) => {
    const residentsTable = residents.map(resident => {
      const allergiesDisplay = [
        ...resident.medicalInfo.allergies.drug,
        ...resident.medicalInfo.allergies.food,
        ...resident.medicalInfo.allergies.environmental
      ].join(', ') || 'None';

      return `
        <tr>
          <td>${resident.firstName} ${resident.lastName}</td>
          <td>${resident.roomNumber}</td>
          <td>${resident.age}</td>
          <td>${resident.careLevel}</td>
          <td>${resident.status}</td>
          <td>${resident.primaryCaregiver}</td>
          <td class="allergies">${allergiesDisplay}</td>
          <td>${resident.emergencyContact.name}<br/>${resident.emergencyContact.phone}</td>
        </tr>
      `;
    }).join('');

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>All Residents Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #7c3aed; margin-bottom: 10px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
            th { background-color: #f8f9fa; font-weight: bold; }
            .allergies { color: #dc2626; font-weight: bold; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #6b7280; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="logo">Care-SolAI Residential Healthcare</div>
            <h1>All Residents Report</h1>
            <p>Total Residents: ${residents.length}</p>
          </div>

          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>Room</th>
                <th>Age</th>
                <th>Care Level</th>
                <th>Status</th>
                <th>Primary Caregiver</th>
                <th>Allergies</th>
                <th>Emergency Contact</th>
              </tr>
            </thead>
            <tbody>
              ${residentsTable}
            </tbody>
          </table>

          <div class="footer">
            <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
            <p>Care-SolAI Residential Healthcare Management System</p>
          </div>
        </body>
      </html>
    `;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Residents</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage resident information and care records for your home-based care facility
          </p>
          <div className="mt-3 flex flex-wrap gap-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              🏠 Residential Care Management
            </span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              🤖 AI-Enhanced Care Planning
            </span>
          </div>
        </div>
        <div className="mt-4 sm:mt-0 flex gap-3">
          <button
            onClick={handlePrintAllResidents}
            className="btn-secondary"
            title="Print All Residents Report"
          >
            <PrinterIcon className="h-4 w-4 mr-2" />
            Print Report
          </button>
          <button
            onClick={handleAddResident}
            className="btn-primary"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Resident
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search residents by name or room..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="form-input pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="discharged">Discharged</option>
              </select>
              <select
                value={caregiverFilter}
                onChange={(e) => setCaregiverFilter(e.target.value)}
                className="form-input"
              >
                <option value="all">All Caregivers</option>
                {getCaregivers().map(caregiver => (
                  <option key={caregiver} value={caregiver}>{caregiver}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Residents List */}
      <div className="card">
        <div className="card-body">
          {filteredResidents.length === 0 ? (
            <div className="text-center py-12">
              <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <UserIcon className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {residents.length === 0 ? 'No Residents Yet' : 'No Matching Residents'}
              </h3>
              <p className="text-gray-600 mb-4">
                {residents.length === 0
                  ? 'Get started by adding your first resident to the care facility.'
                  : 'Try adjusting your search criteria or filters.'
                }
              </p>
              {residents.length === 0 && (
                <button
                  onClick={handleAddResident}
                  className="btn-primary"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add First Resident
                </button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  Residents ({filteredResidents.length})
                </h3>
                <div className="text-sm text-gray-500">
                  Showing {filteredResidents.length} of {residents.length} residents
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredResidents.map((resident) => (
                  <div key={resident.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center overflow-hidden">
                          {resident.imageUrl ? (
                            <img src={resident.imageUrl} alt={`${resident.firstName} ${resident.lastName}`} className="h-12 w-12 object-cover rounded-full" />
                          ) : (
                            <UserIcon className="h-6 w-6 text-blue-600" />
                          )}
                        </div>
                        <div className="ml-3">
                          <h4 className="text-lg font-medium text-gray-900">
                            {resident.firstName} {resident.lastName}
                          </h4>
                          <p className="text-sm text-gray-500">ID: {resident.id}</p>
                        </div>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        resident.status === 'active' ? 'bg-green-100 text-green-800' :
                        resident.status === 'inactive' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {resident.status.charAt(0).toUpperCase() + resident.status.slice(1)}
                      </span>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center text-sm text-gray-600">
                        <CalendarIcon className="h-4 w-4 mr-2" />
                        <span>Age: {resident.age} • Room: {resident.roomNumber}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <HeartIcon className="h-4 w-4 mr-2" />
                        <span>Care Level: {resident.careLevel.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <UserIcon className="h-4 w-4 mr-2" />
                        <span>Caregiver: {resident.primaryCaregiver}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <PhoneIcon className="h-4 w-4 mr-2" />
                        <span>Emergency: {resident.emergencyContact.name}</span>
                      </div>

                      {resident.medicalInfo.conditions.length > 0 && (
                        <div className="flex items-start text-sm text-gray-600">
                          <ExclamationTriangleIcon className="h-4 w-4 mr-2 mt-0.5 text-yellow-500" />
                          <span>Conditions: {resident.medicalInfo.conditions.slice(0, 2).join(', ')}
                            {resident.medicalInfo.conditions.length > 2 && ` +${resident.medicalInfo.conditions.length - 2} more`}
                          </span>
                        </div>
                      )}

                      {(resident.medicalInfo.allergies.drug.length > 0 ||
                        resident.medicalInfo.allergies.food.length > 0 ||
                        resident.medicalInfo.allergies.environmental.length > 0) && (
                        <div className="flex items-start text-sm text-red-600">
                          <ExclamationTriangleIcon className="h-4 w-4 mr-2 mt-0.5" />
                          <span>
                            Allergies: {[
                              ...resident.medicalInfo.allergies.drug,
                              ...resident.medicalInfo.allergies.food,
                              ...resident.medicalInfo.allergies.environmental
                            ].join(', ')}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-200 flex justify-between">
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleViewResident(resident)}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                          title="View resident details"
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          View
                        </button>
                        <button
                          onClick={() => handlePrintResident(resident)}
                          className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                          title="Print resident profile"
                        >
                          <PrinterIcon className="h-4 w-4 mr-1" />
                          Print
                        </button>
                      </div>
                      <button
                        onClick={() => alert('Edit functionality coming soon!')}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        title="Edit resident information"
                      >
                        <PencilIcon className="h-4 w-4 mr-1" />
                        Edit
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add Resident Modal */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowAddModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                      <PlusIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Add New Resident
                      </h3>
                      <p className="text-sm text-gray-500">
                        Enter the resident's information to add them to your care facility
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAddModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Personal Information
                    </h4>

                    <div className="grid grid-cols-2 gap-4">
  <div>
    <label className="block text-sm font-medium text-gray-700">First Name *</label>
    <input
      type="text"
      value={newResident.firstName || ''}
      onChange={(e) => setNewResident(prev => ({ ...prev, firstName: e.target.value }))}
      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
      placeholder="John"
    />
  </div>
  <div>
    <label className="block text-sm font-medium text-gray-700">Last Name *</label>
    <input
      type="text"
      value={newResident.lastName || ''}
      onChange={(e) => setNewResident(prev => ({ ...prev, lastName: e.target.value }))}
      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
      placeholder="Smith"
    />
  </div>
  <div className="col-span-2">
    <label className="block text-sm font-medium text-gray-700">Resident Image</label>
    <div className="mt-1 flex items-center space-x-4">
      <div className="flex-shrink-0">
        {newResident.imageUrl ? (
          <img
            src={newResident.imageUrl}
            alt="Resident Preview"
            className="h-20 w-20 object-cover rounded-full border-2 border-gray-300"
          />
        ) : (
          <div className="h-20 w-20 bg-gray-100 rounded-full flex items-center justify-center border-2 border-gray-300">
            <PhotoIcon className="h-8 w-8 text-gray-400" />
          </div>
        )}
      </div>
      <div className="flex-1">
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/gif"
          onChange={handleImageUpload}
          className="hidden"
        />
        <div className="flex gap-2">
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <PhotoIcon className="h-4 w-4 mr-2" />
            Choose Image
          </button>
          {newResident.imageUrl && (
            <button
              type="button"
              onClick={removeImage}
              className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Remove
            </button>
          )}
        </div>
        <p className="mt-1 text-xs text-gray-500">
          JPG, PNG, or GIF up to 5MB. Recommended: 400x400px
        </p>
      </div>
    </div>
  </div>
</div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Date of Birth *</label>
                        <input
                          type="date"
                          value={newResident.dateOfBirth || ''}
                          onChange={(e) => setNewResident(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Gender</label>
                        <select
                          value={newResident.gender || 'male'}
                          onChange={(e) => setNewResident(prev => ({ ...prev, gender: e.target.value as 'male' | 'female' | 'other' }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="male">Male</option>
                          <option value="female">Female</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Room Number</label>
                        <input
                          type="text"
                          value={newResident.roomNumber || ''}
                          onChange={(e) => setNewResident(prev => ({ ...prev, roomNumber: e.target.value }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="101A"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Care Level</label>
                        <select
                          value={newResident.careLevel || 'independent'}
                          onChange={(e) => setNewResident(prev => ({ ...prev, careLevel: e.target.value as any }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="independent">Independent Living</option>
                          <option value="assisted">Assisted Living</option>
                          <option value="memory_care">Memory Care</option>
                          <option value="skilled_nursing">Skilled Nursing</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Primary Caregiver</label>
                      <input
                        type="text"
                        value={newResident.primaryCaregiver || ''}
                        onChange={(e) => setNewResident(prev => ({ ...prev, primaryCaregiver: e.target.value }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="Emily Rodriguez"
                      />
                    </div>
                  </div>

                  {/* Contact & Emergency Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Emergency Contact
                    </h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Contact Name</label>
                      <input
                        type="text"
                        value={newResident.emergencyContact?.name || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          emergencyContact: { ...prev.emergencyContact!, name: e.target.value }
                        }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="Sarah Smith"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Relationship</label>
                        <input
                          type="text"
                          value={newResident.emergencyContact?.relationship || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            emergencyContact: { ...prev.emergencyContact!, relationship: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Daughter"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input
                          type="tel"
                          value={newResident.emergencyContact?.phone || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            emergencyContact: { ...prev.emergencyContact!, phone: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="******-0123"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email (Optional)</label>
                      <input
                        type="email"
                        value={newResident.emergencyContact?.email || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          emergencyContact: { ...prev.emergencyContact!, email: e.target.value }
                        }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  {/* Primary Care Provider */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Primary Care Provider (PCP)
                    </h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Provider Name *</label>
                      <input
                        type="text"
                        value={newResident.primaryCareProvider?.name || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          primaryCareProvider: { ...prev.primaryCareProvider!, name: e.target.value }
                        }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="Dr. Sarah Williams"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Phone Number *</label>
                        <input
                          type="tel"
                          value={newResident.primaryCareProvider?.phone || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            primaryCareProvider: { ...prev.primaryCareProvider!, phone: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="******-0199"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Specialty</label>
                        <input
                          type="text"
                          value={newResident.primaryCareProvider?.specialty || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            primaryCareProvider: { ...prev.primaryCareProvider!, specialty: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Internal Medicine"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email (Optional)</label>
                      <input
                        type="email"
                        value={newResident.primaryCareProvider?.email || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          primaryCareProvider: { ...prev.primaryCareProvider!, email: e.target.value }
                        }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Address</label>
                      <textarea
                        value={newResident.primaryCareProvider?.address || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          primaryCareProvider: { ...prev.primaryCareProvider!, address: e.target.value }
                        }))}
                        rows={2}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="456 Medical Plaza, Springfield, IL 62701"
                      />
                    </div>
                  </div>

                  {/* Power of Attorney */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Power of Attorney
                    </h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Name *</label>
                      <input
                        type="text"
                        value={newResident.powerOfAttorney?.name || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          powerOfAttorney: { ...prev.powerOfAttorney!, name: e.target.value }
                        }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="Sarah Smith"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Relationship</label>
                        <input
                          type="text"
                          value={newResident.powerOfAttorney?.relationship || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            powerOfAttorney: { ...prev.powerOfAttorney!, relationship: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Daughter"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Type of POA</label>
                        <select
                          value={newResident.powerOfAttorney?.type || 'healthcare'}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            powerOfAttorney: { ...prev.powerOfAttorney!, type: e.target.value as 'healthcare' | 'financial' | 'general' }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        >
                          <option value="healthcare">Healthcare</option>
                          <option value="financial">Financial</option>
                          <option value="general">General</option>
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Phone Number *</label>
                        <input
                          type="tel"
                          value={newResident.powerOfAttorney?.phone || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            powerOfAttorney: { ...prev.powerOfAttorney!, phone: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="******-0123"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Email (Optional)</label>
                        <input
                          type="email"
                          value={newResident.powerOfAttorney?.email || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            powerOfAttorney: { ...prev.powerOfAttorney!, email: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Address</label>
                      <textarea
                        value={newResident.powerOfAttorney?.address || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          powerOfAttorney: { ...prev.powerOfAttorney!, address: e.target.value }
                        }))}
                        rows={2}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="789 Family Lane, Springfield, IL 62702"
                      />
                    </div>
                  </div>

                  {/* Resident Address */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Address
                    </h4>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">Street Address</label>
                      <input
                        type="text"
                        value={newResident.address?.street || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          address: { ...prev.address!, street: e.target.value }
                        }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="123 Oak Street"
                      />
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">City</label>
                        <input
                          type="text"
                          value={newResident.address?.city || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            address: { ...prev.address!, city: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Springfield"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">State</label>
                        <input
                          type="text"
                          value={newResident.address?.state || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            address: { ...prev.address!, state: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="IL"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">ZIP Code</label>
                        <input
                          type="text"
                          value={newResident.address?.zipCode || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            address: { ...prev.address!, zipCode: e.target.value }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="62701"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  {/* Medical Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Medical Information
                    </h4>

                    {/* Height and Weight */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Height</label>
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <input
                              type="number"
                              value={newResident.medicalInfo?.height?.feet || ''}
                              onChange={(e) => setNewResident(prev => ({
                                ...prev,
                                medicalInfo: {
                                  ...prev.medicalInfo!,
                                  height: { ...prev.medicalInfo!.height, feet: parseInt(e.target.value) || 0 }
                                }
                              }))}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                              placeholder="5"
                              min="0"
                              max="8"
                            />
                            <label className="text-xs text-gray-500">Feet</label>
                          </div>
                          <div>
                            <input
                              type="number"
                              value={newResident.medicalInfo?.height?.inches || ''}
                              onChange={(e) => setNewResident(prev => ({
                                ...prev,
                                medicalInfo: {
                                  ...prev.medicalInfo!,
                                  height: { ...prev.medicalInfo!.height, inches: parseInt(e.target.value) || 0 }
                                }
                              }))}
                              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                              placeholder="8"
                              min="0"
                              max="11"
                            />
                            <label className="text-xs text-gray-500">Inches</label>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Weight (lbs)</label>
                        <input
                          type="number"
                          value={newResident.medicalInfo?.weight || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            medicalInfo: { ...prev.medicalInfo!, weight: parseInt(e.target.value) || 0 }
                          }))}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="165"
                          min="0"
                        />
                      </div>
                    </div>

                    {/* Diet Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Diet Type</label>
                      <select
                        value={newResident.medicalInfo?.dietType || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          medicalInfo: { ...prev.medicalInfo!, dietType: e.target.value }
                        }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      >
                        <option value="">Select Diet Type</option>
                        <option value="Regular Diet">Regular Diet</option>
                        <option value="Diabetic Diet">Diabetic Diet</option>
                        <option value="Low Sodium Diet">Low Sodium Diet</option>
                        <option value="Heart Healthy Diet">Heart Healthy Diet</option>
                        <option value="Soft Mechanical Diet">Soft Mechanical Diet</option>
                        <option value="Pureed Diet">Pureed Diet</option>
                        <option value="Liquid Diet">Liquid Diet</option>
                        <option value="Renal Diet">Renal Diet</option>
                        <option value="Gluten-Free Diet">Gluten-Free Diet</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>

                    {/* Allergies */}
                    <div className="space-y-3">
                      <label className="block text-sm font-medium text-gray-700">Allergies</label>

                      <div>
                        <label className="block text-xs font-medium text-red-600 mb-1">Drug Allergies</label>
                        <textarea
                          value={newResident.medicalInfo?.allergies?.drug?.join(', ') || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            medicalInfo: {
                              ...prev.medicalInfo!,
                              allergies: {
                                ...prev.medicalInfo!.allergies,
                                drug: e.target.value.split(',').map(item => item.trim()).filter(item => item)
                              }
                            }
                          }))}
                          rows={2}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Penicillin, Sulfa, etc. (separate with commas)"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-orange-600 mb-1">Food Allergies</label>
                        <textarea
                          value={newResident.medicalInfo?.allergies?.food?.join(', ') || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            medicalInfo: {
                              ...prev.medicalInfo!,
                              allergies: {
                                ...prev.medicalInfo!.allergies,
                                food: e.target.value.split(',').map(item => item.trim()).filter(item => item)
                              }
                            }
                          }))}
                          rows={2}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Shellfish, Nuts, Dairy, etc. (separate with commas)"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-green-600 mb-1">Environmental Allergies</label>
                        <textarea
                          value={newResident.medicalInfo?.allergies?.environmental?.join(', ') || ''}
                          onChange={(e) => setNewResident(prev => ({
                            ...prev,
                            medicalInfo: {
                              ...prev.medicalInfo!,
                              allergies: {
                                ...prev.medicalInfo!.allergies,
                                environmental: e.target.value.split(',').map(item => item.trim()).filter(item => item)
                              }
                            }
                          }))}
                          rows={2}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          placeholder="Pollen, Dust mites, Latex, etc. (separate with commas)"
                        />
                      </div>
                    </div>

                    {/* Diagnosis */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Primary Diagnosis</label>
                      <textarea
                        value={newResident.medicalInfo?.diagnosis?.join(', ') || ''}
                        onChange={(e) => setNewResident(prev => ({
                          ...prev,
                          medicalInfo: {
                            ...prev.medicalInfo!,
                            diagnosis: e.target.value.split(',').map(item => item.trim()).filter(item => item)
                          }
                        }))}
                        rows={2}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="Essential Hypertension, Type 2 Diabetes Mellitus, etc. (separate with commas)"
                      />
                    </div>
                  </div>

                  <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2 mb-4">
                    Additional Notes
                  </h4>
                  <textarea
                    value={newResident.notes || ''}
                    onChange={(e) => setNewResident(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="Any additional notes about the resident's preferences, care needs, or special considerations..."
                  />
                </div>

                <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-blue-800">Resident Registration</span>
                  </div>
                  <div className="mt-2 text-xs text-blue-700">
                    <p>• Resident will be added with "Active" status</p>
                    <p>• Medical information can be added after registration</p>
                    <p>• Care plans and assessments can be created from the resident profile</p>
                    <p>• All information is HIPAA-compliant and securely stored</p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={saveResident}
                  disabled={!newResident.firstName || !newResident.lastName || !newResident.dateOfBirth}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Resident
                </button>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Resident Modal */}
      {showViewModal && selectedResident && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowViewModal(false)} />

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10 overflow-hidden">
                      {selectedResident.imageUrl ? (
                        <img src={selectedResident.imageUrl} alt={`${selectedResident.firstName} ${selectedResident.lastName}`} className="h-12 w-12 object-cover rounded-full" />
                      ) : (
                        <EyeIcon className="h-6 w-6 text-blue-600" />
                      )}
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        {selectedResident.firstName} {selectedResident.lastName}
                      </h3>
                      <p className="text-sm text-gray-500">
                        Resident ID: {selectedResident.id} • Room: {selectedResident.roomNumber}
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handlePrintResident(selectedResident)}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <PrinterIcon className="h-4 w-4 mr-2" />
                      Print
                    </button>
                    <button
                      onClick={() => setShowViewModal(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="h-6 w-6" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Personal Information
                    </h4>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Full Name:</span>
                        <span className="text-sm text-gray-900">{selectedResident.firstName} {selectedResident.lastName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Date of Birth:</span>
                        <span className="text-sm text-gray-900">{selectedResident.dateOfBirth}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Age:</span>
                        <span className="text-sm text-gray-900">{selectedResident.age} years</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Gender:</span>
                        <span className="text-sm text-gray-900 capitalize">{selectedResident.gender}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Room Number:</span>
                        <span className="text-sm text-gray-900">{selectedResident.roomNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Admission Date:</span>
                        <span className="text-sm text-gray-900">{selectedResident.admissionDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Status:</span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          selectedResident.status === 'active' ? 'bg-green-100 text-green-800' :
                          selectedResident.status === 'inactive' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {selectedResident.status.charAt(0).toUpperCase() + selectedResident.status.slice(1)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Care Level:</span>
                        <span className="text-sm text-gray-900 capitalize">{selectedResident.careLevel.replace('_', ' ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Primary Caregiver:</span>
                        <span className="text-sm text-gray-900">{selectedResident.primaryCaregiver}</span>
                      </div>
                    </div>
                  </div>

                  {/* Medical Information */}
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Medical Information
                    </h4>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Height:</span>
                        <span className="text-sm text-gray-900">{selectedResident.medicalInfo.height.feet}'{selectedResident.medicalInfo.height.inches}"</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Weight:</span>
                        <span className="text-sm text-gray-900">{selectedResident.medicalInfo.weight} lbs</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Diet Type:</span>
                        <span className="text-sm text-gray-900">{selectedResident.medicalInfo.dietType || 'Not specified'}</span>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">Drug Allergies:</span>
                        <div className="mt-1">
                          {selectedResident.medicalInfo.allergies.drug.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {selectedResident.medicalInfo.allergies.drug.map((allergy, index) => (
                                <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                  {allergy}
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">None</span>
                          )}
                        </div>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">Food Allergies:</span>
                        <div className="mt-1">
                          {selectedResident.medicalInfo.allergies.food.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {selectedResident.medicalInfo.allergies.food.map((allergy, index) => (
                                <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                  {allergy}
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">None</span>
                          )}
                        </div>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">Primary Diagnosis:</span>
                        <div className="mt-1">
                          {selectedResident.medicalInfo.diagnosis.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {selectedResident.medicalInfo.diagnosis.map((diagnosis, index) => (
                                <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  {diagnosis}
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">None specified</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Emergency Contact & PCP */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Emergency Contact
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Name:</span>
                        <span className="text-sm text-gray-900">{selectedResident.emergencyContact.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Relationship:</span>
                        <span className="text-sm text-gray-900">{selectedResident.emergencyContact.relationship}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Phone:</span>
                        <span className="text-sm text-gray-900">{selectedResident.emergencyContact.phone}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Email:</span>
                        <span className="text-sm text-gray-900">{selectedResident.emergencyContact.email || 'Not provided'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">
                      Primary Care Provider
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Name:</span>
                        <span className="text-sm text-gray-900">{selectedResident.primaryCareProvider.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Specialty:</span>
                        <span className="text-sm text-gray-900">{selectedResident.primaryCareProvider.specialty}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Phone:</span>
                        <span className="text-sm text-gray-900">{selectedResident.primaryCareProvider.phone}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm font-medium text-gray-500">Email:</span>
                        <span className="text-sm text-gray-900">{selectedResident.primaryCareProvider.email || 'Not provided'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Notes */}
                {selectedResident.notes && (
                  <div className="mt-6">
                    <h4 className="text-md font-medium text-gray-900 border-b border-gray-200 pb-2 mb-3">
                      Additional Notes
                    </h4>
                    <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                      {selectedResident.notes}
                    </p>
                  </div>
                )}
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  onClick={() => setShowViewModal(false)}
                  className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Patients;
