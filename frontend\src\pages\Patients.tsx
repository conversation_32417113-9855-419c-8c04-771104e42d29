import React from 'react';
import { PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

const Patients: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Residents</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage resident information and care records for your home-based care facility
          </p>
          <div className="mt-3 flex flex-wrap gap-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              🏠 Residential Care Management
            </span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              🤖 AI-Enhanced Care Planning
            </span>
          </div>
        </div>
        <div className="mt-4 sm:mt-0">
          <button className="btn-primary">
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Resident
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search patients..."
                  className="form-input pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select className="form-input">
                <option>All Status</option>
                <option>Active</option>
                <option>Inactive</option>
              </select>
              <select className="form-input">
                <option>All Caregivers</option>
                <option>Emily Rodriguez</option>
                <option>David Wilson</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Patients List */}
      <div className="card">
        <div className="card-body">
          <div className="text-center py-12">
            <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-gray-400 text-xl">👥</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Patient Management
            </h3>
            <p className="text-gray-600 mb-4">
              This section will display your patient roster with detailed information,
              care plans, and AI-powered insights.
            </p>
            <p className="text-sm text-gray-500">
              Features coming soon: Patient profiles, medical history, care scheduling,
              fall risk assessments, and medication management.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Patients;
