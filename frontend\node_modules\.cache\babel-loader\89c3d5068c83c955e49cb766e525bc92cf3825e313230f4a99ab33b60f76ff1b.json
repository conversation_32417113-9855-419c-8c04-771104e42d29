{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\medisyn\\\\frontend\\\\src\\\\components\\\\Layout\\\\AuthLayout.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthLayout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-12 w-12 bg-primary-600 rounded-lg flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white font-bold text-xl\",\n            children: \"CS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900\",\n        children: \"CareSyncAI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-center text-sm text-gray-600\",\n        children: \"AI-Powered Homecare Management System\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: \"\\xA9 2024 CareSyncAI. All rights reserved.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = AuthLayout;\nexport default AuthLayout;\nvar _c;\n$RefreshReg$(_c, \"AuthLayout\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AuthLayout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/medisyn/frontend/src/components/Layout/AuthLayout.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface AuthLayoutProps {\n  children: React.ReactNode;\n}\n\nconst AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"flex justify-center\">\n          <div className=\"h-12 w-12 bg-primary-600 rounded-lg flex items-center justify-center\">\n            <span className=\"text-white font-bold text-xl\">CS</span>\n          </div>\n        </div>\n        <h2 className=\"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900\">\n          CareSyncAI\n        </h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          AI-Powered Homecare Management System\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          {children}\n        </div>\n      </div>\n\n      <div className=\"mt-8 text-center\">\n        <p className=\"text-xs text-gray-500\">\n          © 2024 CareSyncAI. All rights reserved.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthLayout;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM1B,MAAMC,UAAqC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC9D,oBACEF,OAAA;IAAKG,SAAS,EAAC,4EAA4E;IAAAD,QAAA,gBACzFF,OAAA;MAAKG,SAAS,EAAC,kCAAkC;MAAAD,QAAA,gBAC/CF,OAAA;QAAKG,SAAS,EAAC,qBAAqB;QAAAD,QAAA,eAClCF,OAAA;UAAKG,SAAS,EAAC,sEAAsE;UAAAD,QAAA,eACnFF,OAAA;YAAMG,SAAS,EAAC,8BAA8B;YAAAD,QAAA,EAAC;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNP,OAAA;QAAIG,SAAS,EAAC,kEAAkE;QAAAD,QAAA,EAAC;MAEjF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLP,OAAA;QAAGG,SAAS,EAAC,wCAAwC;QAAAD,QAAA,EAAC;MAEtD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENP,OAAA;MAAKG,SAAS,EAAC,uCAAuC;MAAAD,QAAA,eACpDF,OAAA;QAAKG,SAAS,EAAC,kDAAkD;QAAAD,QAAA,EAC9DA;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENP,OAAA;MAAKG,SAAS,EAAC,kBAAkB;MAAAD,QAAA,eAC/BF,OAAA;QAAGG,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAC;MAErC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GA9BIP,UAAqC;AAgC3C,eAAeA,UAAU;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}